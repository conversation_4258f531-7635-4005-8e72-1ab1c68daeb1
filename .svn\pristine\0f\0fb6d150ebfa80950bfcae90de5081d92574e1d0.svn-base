const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  openWindow: (title) => ipcRenderer.send('open-window', title),
  asynchronousMessage: (data) => ipcRenderer.send('asynchronous-message', data),
  downLoadAllFile: (data) => ipcRenderer.invoke('download-folder', data),
  uploadFile: (data) => ipcRenderer.invoke('upload-files', data),
  getLocalIps: (data) => ipcRenderer.invoke('get-local-ips')
})

contextBridge.exposeInMainWorld('rendererOperate', {
  on: (message, data) => ipcRenderer.on(message, data),
  send: (data) => ipcRenderer.send(data)
})

contextBridge.exposeInMainWorld('electronDownload', {
  downloadFile: (fileUrl) => ipcRenderer.send('download-file', fileUrl)
})

// #region 网页内容获取相关API - 2025-09-12
contextBridge.exposeInMainWorld('webContentAPI', {
  // 获取网页内容 - 使用iframe方式（最稳定）
  getWebContentByIframe: (url) => ipcRenderer.invoke('get-web-content-iframe', url),
  // 打开登录窗口 - 用于登录千里马等需要认证的网站
  openLoginWindow: (url) => ipcRenderer.invoke('open-login-window', url),
  // 创建预览BrowserView - 在主窗口中嵌入完整的原网页
  createPreviewBrowserView: (url) => ipcRenderer.invoke('create-preview-browserview', url),
  // 销毁预览BrowserView
  destroyPreviewBrowserView: () => ipcRenderer.invoke('destroy-preview-browserview')
})
// #endregion
