<template>
  <div class="newMeeting-container">
    <!-- 顶部 -->
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/meeting/meeting_icon.png" alt="" />
            会议管理
          </span>
        </div>
      </el-col>
    </el-row>

    <div class="newMeeting-body">
      <!-- 日期时间选择组件 -->
      <DateTimeSelector :current-week="currentWeek" :checked-date="checkedDate" @week-change="handleWeekChange" @date-click="checkedDate = $event" />

      <!-- 会议室预约组件 -->
      <MeetingRoomSchedule
        :current-week="currentWeek"
        :meeting-data="meetingData"
        :selected-date="checkedDate"
        @booking-click="handleBookingClick"
        @meeting-click="handleMeetingClick"
        @meeting-detail="handleMeetingDetail"
      />

      <!-- 今日概览组件 -->
      <TodayOverview :checked-date="checkedDate" :today-meetings="todayMeetings" :free-meetings="freeMeetings" @free-room-click="handleFreeRoomClick" />

      <!-- 未来预告组件 -->
      <FuturePreview :future-meetings="futureMeetings" />
    </div>

    <!-- 预约抽屉组件 -->
    <BookingDrawer
      ref="BookingDrawerRef"
      :visible.sync="bookingDrawerVisible"
      :booking-info="currentBookingInfo"
      :selected-date="checkedDate"
      :meeting-data="currentMeetingData"
      :all-meeting-data="meetingData"
      @booking-success="handleBookingSuccess"
    />

    <!-- 会议详情抽屉组件 -->
    <MeetingDetailDrawer :visible.sync="detailDrawerVisible" :meeting-data="currentDetailMeeting" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'
import DateTimeSelector from './components/DateTimeSelector.vue'
import MeetingRoomSchedule from './components/MeetingRoomSchedule.vue'
import TodayOverview from './components/TodayOverview.vue'
import FuturePreview from './components/FuturePreview.vue'
import BookingDrawer from './components/BookingDrawer.vue'
import MeetingDetailDrawer from './components/MeetingDetailDrawer.vue'
import { conference_list, conference_next, conference_neutral_list, conference_preview } from '@/api/newMeeting.js'

export default {
  name: 'NewMeeting',
  components: {
    DateTimeSelector,
    MeetingRoomSchedule,
    TodayOverview,
    FuturePreview,
    BookingDrawer,
    MeetingDetailDrawer
  },
  data() {
    return {
      currentWeek: this.getCurrentWeek(),
      checkedDate: formatDate(new Date(), 'yyyy-MM-dd'),
      meetingData: {
        one: [], // 大会议室数据
        two: [] // 小会议室数据
      },
      todayMeetings: {}, // 下一场会议
      freeMeetings: {}, // 空挡建议
      futureMeetings: [], // 未来两天预告
      bookingDrawerVisible: false,
      currentBookingInfo: {},
      currentMeetingData: null, // 当前编辑的会议数据
      detailDrawerVisible: false, // 会议详情抽屉显示状态
      currentDetailMeeting: {} // 当前查看详情的会议数据
    }
  },
  computed: {
    ...mapGetters(['userId'])
  },
  watch: {
    checkedDate() {
      this.loadMeetingData()
    }
  },

  created() {
    this.loadMeetingData()
  },

  methods: {
    // 获取当前周的日期数组
    getCurrentWeek() {
      const today = new Date()
      const currentDay = today.getDay() === 0 ? 7 : today.getDay() // 周日为0，调整为7
      const monday = new Date(today)
      monday.setDate(today.getDate() - currentDay + 1)

      const week = []
      for (let i = 0; i < 7; i++) {
        const date = new Date(monday)
        date.setDate(monday.getDate() + i)
        week.push({
          date: formatDate(new Date(date), 'yyyy-MM-dd'), // 创建新的Date对象避免引用问题
          day: date.getDate(),
          weekDay: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i]
        })
      }
      return week
    },

    // 处理周切换
    handleWeekChange(direction) {
      const firstDay = new Date(this.currentWeek[0].date)
      firstDay.setDate(firstDay.getDate() + direction * 7)

      const newWeek = []
      for (let i = 0; i < 7; i++) {
        const date = new Date(firstDay)
        date.setDate(firstDay.getDate() + i)
        newWeek.push({
          date: formatDate(new Date(date), 'yyyy-MM-dd'),
          day: date.getDate(),
          weekDay: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i]
        })
      }
      this.currentWeek = newWeek
      this.loadMeetingData()
    },

    // 加载会议数据
    async loadMeetingData() {
      // 会议数据
      const { data } = await conference_list({ dateStr: this.checkedDate })
      this.meetingData = data
      // 今日会议
      const { data: nextMeeting } = await conference_next({ dateStr: this.checkedDate })
      this.todayMeetings = nextMeeting
      // 空挡建议
      const { data: neutralMeeting } = await conference_neutral_list({ dateStr: this.checkedDate })
      this.freeMeetings = neutralMeeting
      // 未来两天预告
      const { data: futureMeeting } = await conference_preview({ dateStr: formatDate(new Date(), 'yyyy-MM-dd') })
      this.futureMeetings = futureMeeting
    },

    // 处理预约点击事件
    handleBookingClick(bookingInfo) {
      this.currentBookingInfo = bookingInfo
      this.currentMeetingData = null // 清除会议数据，表示新建模式
      this.bookingDrawerVisible = true
    },

    // 处理会议点击事件（修改会议）
    handleMeetingClick(meeting) {
      if (meeting.createUserId !== this.userId) {
        return
      }
      this.currentMeetingData = meeting
      // 设置预约信息，用于显示会议室和时间
      this.currentBookingInfo = {
        roomType: meeting.conferenceRoom === 'one' ? 'large' : 'small',
        startTime: meeting.startTime,
        endTime: meeting.endTime
      }
      this.bookingDrawerVisible = true
    },

    // 处理会议详情点击事件
    handleMeetingDetail(meeting) {
      if (!meeting.conferenceUserIds.includes(this.userId)) {
        return
      }
      this.currentDetailMeeting = meeting
      this.detailDrawerVisible = true
    },

    // 处理空闲时段点击事件
    handleFreeRoomClick(roomInfo) {
      this.currentBookingInfo = roomInfo
      this.currentMeetingData = null // 清除会议数据，表示新建模式

      this.bookingDrawerVisible = true
      this.$nextTick(() => {
        // 确保组件已渲染
        this.$refs['BookingDrawerRef'].duration = 60
      })
    },

    // 处理预约成功
    handleBookingSuccess() {
      this.loadMeetingData() // 重新加载会议数据
    }
  }
}
</script>

<style scoped lang="scss">
.newMeeting-container {
  padding: 18px 18px;
  background-color: #e8eaed;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  overflow: hidden;
  @media (min-height: 970px) {
    padding: 20px 18px;
  }

  .top {
    @media (max-height: 970px) {
      display: none;
    }
    .top_left {
      display: flex;
      align-items: center;
      .meeting_icon {
        display: flex;
        align-items: center;
        margin-right: 10px;
        font-size: 16px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
        img {
          margin-right: 8px;
        }
      }
    }
  }
  .newMeeting-body {
    width: 100%;
    padding: 25px 40px;
    background: #ffffff;
    border-radius: 12px 12px 12px 12px;
  }
}
</style>
