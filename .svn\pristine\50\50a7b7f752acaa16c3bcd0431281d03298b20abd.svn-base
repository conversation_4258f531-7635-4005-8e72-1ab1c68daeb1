'use strict'
import autoUpdate from '@/utils/autoUpdate'

// const process = require('process')
const path = require('path')
import { app, protocol, BrowserWindow, globalShortcut, ipcMain, Menu, Tray } from 'electron'
import { createProtocol } from 'vue-cli-plugin-electron-builder/lib'
const fs = require('fs')
const os = require('os')
const axios = require('axios')
const FormData = require('form-data')
// #region 网页内容获取相关依赖 - 2025-09-12
// 网站内容提取规则配置
const SITE_EXTRACTION_RULES = {
  qianlima: {
    selector: '#detailContentHtml, #detailInfoContainer, .title-module',
    description: '千里马网站详情内容'
  },
  // 可以继续添加其他网站的规则
  // 示例：其他招标网站
  ccgp: {
    selector: '.vF_detail_content_container, .vF_detail_content',
    description: '中国政府采购网'
  },
  ggzy: {
    selector: '.detail_con, .detail-content, .content',
    description: '公共资源交易网站内容'
  },
  default: {
    selector: '.vF_detail_main, .content, .detail, .main-content, article, .post-content, .entry-content',
    description: '默认内容选择器'
  }
}

// 根据URL判断网站类型并获取对应的提取规则
function getSiteExtractionRule(url) {
  try {
    const hostname = new URL(url).hostname.toLowerCase()
    console.log(hostname)

    // 检查是否包含特定网站标识
    for (const [siteKey, rule] of Object.entries(SITE_EXTRACTION_RULES)) {
      if (siteKey !== 'default' && hostname.includes(siteKey)) {
        console.log(`检测到${rule.description}，使用选择器: ${rule.selector}`)
        return rule
      }
    }

    // 返回默认规则
    console.log('使用默认内容选择器')
    return SITE_EXTRACTION_RULES.default
  } catch (error) {
    console.error('URL解析失败:', error)
    return SITE_EXTRACTION_RULES.default
  }
}

// 简单的HTML解析函数，支持智能内容提取
function parseHtmlContent(html, url) {
  // 提取title
  const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i)
  const title = titleMatch ? titleMatch[1].trim() : ''

  // 根据网站获取提取规则
  const rule = getSiteExtractionRule(url)

  // 尝试提取特定内容
  let specificContent = null
  const selectors = rule.selector.split(',').map((s) => s.trim())

  for (const selector of selectors) {
    let regex
    if (selector.startsWith('#')) {
      // ID选择器
      const id = selector.substring(1)
      regex = new RegExp(`<[^>]*id=['""]${id}['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
    } else if (selector.startsWith('.')) {
      // Class选择器
      const className = selector.substring(1)
      regex = new RegExp(`<[^>]*class=['""][^'""]*${className}[^'""]*['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
    } else {
      // 标签选择器
      regex = new RegExp(`<${selector}[^>]*>([\\s\\S]*?)<\/${selector}>`, 'i')
    }

    const match = html.match(regex)
    if (match && match[1]) {
      specificContent = match[1].trim()
      console.log(`成功使用选择器 ${selector} 提取到内容`)
      break
    }
  }

  // 如果没有找到特定内容，提取纯文本作为备选
  const fullTextContent = html
    .replace(/<script[\s\S]*?<\/script>/gi, '')
    .replace(/<style[\s\S]*?<\/style>/gi, '')
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim()

  return {
    title,
    html,
    specificContent,
    textContent: specificContent || fullTextContent, // 优先返回特定内容
    extractionRule: rule.description,
    extractedSelector: specificContent
      ? selectors.find((s) => {
          // 找到成功的选择器
          let regex
          if (s.startsWith('#')) {
            const id = s.substring(1)
            regex = new RegExp(`<[^>]*id=['""]${id}['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
          } else if (s.startsWith('.')) {
            const className = s.substring(1)
            regex = new RegExp(`<[^>]*class=['""][^'""]*${className}[^'""]*['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
          } else {
            regex = new RegExp(`<${s}[^>]*>([\\s\\S]*?)<\/${s}>`, 'i')
          }
          return html.match(regex)
        })
      : '无匹配'
  }
}
// #endregion

// import installExtension, { VUEJS_DEVTOOLS } from 'electron-devtools-installer'
const isDevelopment = process.env.NODE_ENV !== 'production'

// 定义静态资源路径
const __static = isDevelopment ? path.join(__dirname, '../public') : path.join(__dirname, '../public')

// Scheme must be registered before the app is ready
protocol.registerSchemesAsPrivileged([{ scheme: 'app', privileges: { secure: true, standard: true } }])
let win
async function createWindow() {
  // Create the browser window.
  win = new BrowserWindow({
    width: 1920,
    height: 1080,
    icon: path.join(__static, 'favicon.ico'),
    webPreferences: {
      // Use pluginOptions.nodeIntegration, leave this alone
      // See nklayman.github.io/vue-cli-plugin-electron-builder/guide/security.html#node-integration for more info
      preload: path.join(__dirname, 'preload.js'),
      // preload: path.join(app.getAppPath(), '..', '..', 'src', 'preload.js'),

      // nodeIntegration: process.env.ELECTRON_NODE_INTEGRATION,
      contextIsolation: true,
      nodeIntegration: false,
      enableRemoteModule: false,
      // 安全
      webSecurity: false,
      // 启用硬件加速和 WebGL 支持
      hardwareAcceleration: true,
      // 允许运行不安全内容以支持 Unity WebGL
      allowRunningInsecureContent: false,
      // 启用实验性功能以更好支持 WebGL
      experimentalFeatures: true
    }
  })
  win.show() // 显示并聚焦于窗口
  Menu.setApplicationMenu(null)
  win.setMenu(null) // 去除菜单
  win.maximize() // 最大化窗口
  if (process.env.WEBPACK_DEV_SERVER_URL) {
    // Load the url of the dev server if in development mode
    await win.loadURL(process.env.WEBPACK_DEV_SERVER_URL)
    if (!process.env.IS_TEST) win.webContents.openDevTools()
  } else {
    createProtocol('app')
    // Load the index.html when not in development
    win.loadURL('app://./index.html')
  }
  ipcMain.on('open-window', (event) => {
    win.show() // 显示并聚焦于窗口
    win.maximize() // 最大化窗口
  })

  app.setAppUserModelId('公司内部管理平台')
}

// Quit when all windows are closed.
app.on('window-all-closed', () => {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// Enable hardware acceleration and Unity WebGL support
// 启用硬件加速和 Unity WebGL 支持
app.disableDomainBlockingFor3DAPIs()

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', async () => {
  // // Install Vue Devtools
  //   try {
  //     await installExtension(VUEJS_DEVTOOLS)
  //   } catch (e) {
  //     console.error('Vue Devtools failed to install:', e.toString())
  //   }
  // }
  globalShortcut.register('Alt+CommandOrControl+I', () => {
    BrowserWindow.getFocusedWindow().webContents.openDevTools()
  })
  createWindow()
  autoUpdate.handleUpdate(win)
})

ipcMain.on('asynchronous-message', () => {
  // eslint-disable-next-line eqeqeq
  if (win.isVisible() && !timer) {
    shinkTray()
  }
  // eslint-disable-next-line eqeqeq
  // if (timer && arg == '0') {
  //   tray.setImage(trayIcon)
  //   clearInterval(timer)
  // }
})

ipcMain.on('download-file', (event, fileUrl) => {
  // 使用当前窗口的 webContents 触发下载
  const win = BrowserWindow.getFocusedWindow()
  if (win) {
    win.webContents.downloadURL(fileUrl)
  }
})

// 处理文件下载请求-根据层级自动划分
ipcMain.handle('download-folder', async (event, { files, folderName }) => {
  const downloadFolderPath = path.join('D:', folderName)

  // 创建文件夹
  if (!fs.existsSync(downloadFolderPath)) {
    fs.mkdirSync(downloadFolderPath) // 创建新的文件夹
  }

  const downloadPaths = []

  // console.log(`文件存放地址:${downloadFolderPath}`)

  for (const file of files) {
    const fileUrl = new URL(file.fileUrl)
    // 使用decodeURIComponent对中文进行编码
    const relativePath = decodeURIComponent(fileUrl.pathname.split('emulation')[1]) // 获取路径

    // 拼接路径
    const filePath = path.join(downloadFolderPath, relativePath)
    // 获取文件的目录部分
    const directoryPath = path.dirname(filePath)
    // console.log(`文件路径：${filePath}`)

    // 创建目录
    if (!fs.existsSync(directoryPath)) {
      fs.mkdirSync(directoryPath, { recursive: true }) // 创建多层级目录
    }
    const writer = fs.createWriteStream(filePath)

    const fileResponse = await axios({
      url: fileUrl.href,
      method: 'GET',
      responseType: 'stream'
    })

    fileResponse.data.pipe(writer)

    await new Promise((resolve, reject) => {
      writer.on('finish', () => {
        downloadPaths.push(filePath) // 记录下载路径
        resolve()
      })
      writer.on('error', reject)
    })
  }

  return downloadPaths // 返回所有下载文件的路径
})

// 处理文件上传请求
ipcMain.handle('upload-files', async (event, { filePaths, uploadUrl, token }) => {
  const uploadResults = []
  const date = new Date().getTime()

  for (const filePath of filePaths) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`)
      }
      // const stats = fs.statSync(filePath)
      // 取路径下最后面的文件名
      const fileName = filePath.split('\\').pop()

      // 去掉以a开头的时间戳
      const path = filePath.split('emulationFiles\\')[1].replace(/\\/g, '/')
      const newPath = path.replace(/^a[^\/]*/, `b${date}`)

      const form = new FormData()
      form.append('file', fs.createReadStream(filePath)) // 使用流上传
      form.append('fileName', 'productRelease/' + newPath)

      const response = await axios.post(uploadUrl, form, {
        headers: {
          ...form.getHeaders(),
          Authorization: `Bearer ${token}`
        },
        maxBodyLength: Infinity,
        maxContentLength: Infinity
      })
      uploadResults.push({ filePath, response: response.data.data, fileName })
    } catch (error) {
      console.log(error)

      throw error
      // console.log(error.message)

      // uploadResults.push({ filePath, error: error.message })
    }
  }
  const folderName = filePaths[0].split('\\').slice(0, 3).join('\\')

  deleteFolder(folderName)
  return uploadResults
})

// 删除文件夹的函数
function deleteFolder(folderPath) {
  fs.rm(folderPath, { recursive: true, force: true }, (err) => {
    if (err) {
      console.error(`删除文件夹时出错: ${err.message}`)
      return
    }
    console.log('文件夹已成功删除')
  })
}

// 设置托盘
let tray = null
var timer = null
var trayIcon = path.join(__static, 'favicon.ico')
var trayAIcon = path.join(__static, 'faviconT.ico')
var trayAIcon_lucency = path.join(__static, 'lucency.ico')
app.whenReady().then(() => {
  tray = new Tray(path.join(trayIcon))
  const contextMenu = Menu.buildFromTemplate([
    {
      label: '打开',
      click: function () {
        win.show()
        win.maximize() // 最大化窗口
      } // 打开相应页面
    },
    {
      label: '退出',
      click: function () {
        app.quit()
      }
    }
  ])
  // 设置此托盘图标的悬停提示内容
  tray.setToolTip('公司内部管理平台')
  // 设置此图标的上下文菜单
  tray.setContextMenu(contextMenu)
  // 单点击 1.主窗口显示隐藏切换 2.清除闪烁
  tray.on('click', function () {
    if (timer) {
      win.show()
      tray.setImage(trayIcon)
      clearInterval(timer)
      timer = null
    } else {
      // 主窗口显示隐藏切换
      win.isVisible() ? win.hide() : win.show()
    }
  })
})

function shinkTray() {
  // 系统托盘图标闪烁
  var count = 0
  timer = setInterval(function () {
    count++
    // eslint-disable-next-line eqeqeq
    if (count == 0) {
      tray.setImage(trayIcon)
      // eslint-disable-next-line eqeqeq
    } else if (count % 2 == 0) {
      tray.setImage(trayAIcon_lucency)
    } else {
      tray.setImage(trayAIcon)
    }
  }, 500)
}

// if (process.platform === 'win32') {
//   app.setAppUserModelId(process.execPath)
// }
// Exit cleanly on request from parent process in development mode.
if (isDevelopment) {
  if (process.platform === 'win32') {
    process.on('message', (data) => {
      if (data === 'graceful-exit') {
        app.quit()
      }
    })
  } else {
    process.on('SIGTERM', () => {
      app.quit()
    })
  }
}

// #region 判断当前网络环境是内网还是外网
ipcMain.handle('get-local-ips', () => {
  const interfaces = os.networkInterfaces()
  const ips = []

  for (const inter in interfaces) {
    for (const details of interfaces[inter]) {
      if (details.family === 'IPv4' && !details.internal) {
        ips.push(details.address)
      }
    }
  }
  return ips
})

// #endregion

app.on('will-quit', () => {
  globalShortcut.unregisterAll()
})

// #region 网页内容获取相关IPC处理器 - 2025-09-12

/**
 * 方法1: 使用iframe方式获取网页内容，支持登录状态保持
 * 通过共享session和设置User-Agent来模拟真实浏览器环境
 */
ipcMain.handle('get-web-content-iframe', async (event, url) => {
  try {
    // 创建一个隐藏的BrowserWindow来加载网页，共享主窗口的session以保持登录状态
    const hiddenWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false, // 允许跨域
        allowRunningInsecureContent: true,
        session: win.webContents.session, // 关键：共享主窗口的session，包括cookies和登录状态
        // 设置User-Agent，避免被识别为自动化工具
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    })

    // 设置额外的请求头，模拟真实浏览器
    hiddenWindow.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
      details.requestHeaders['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
      details.requestHeaders['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8'
      details.requestHeaders['Cache-Control'] = 'no-cache'
      details.requestHeaders['Pragma'] = 'no-cache'
      callback({ requestHeaders: details.requestHeaders })
    })

    // 等待页面完全加载，包括异步内容
    await new Promise((resolve) => {
      let resolved = false

      // 方案1：等待did-finish-load事件（必须在loadURL之前注册）
      hiddenWindow.webContents.once('did-finish-load', () => {
        console.log('页面基础加载完成')
        if (!resolved) {
          // 再等待一段时间让JavaScript执行完成
          setTimeout(() => {
            if (!resolved) {
              resolved = true
              console.log('页面加载等待完成')
              resolve()
            }
          }, 2000) // 等待2秒让动态内容加载
        }
      })

      // 方案2：超时保护，避免无限等待
      setTimeout(() => {
        if (!resolved) {
          resolved = true
          console.log('页面加载超时，继续执行')
          resolve()
        }
      }, 10000) // 最多等待10秒

      // 开始加载页面
      hiddenWindow
        .loadURL(url)
        .then(() => {
          console.log('页面开始加载')
        })
        .catch((error) => {
          console.error('页面加载失败:', error)
          if (!resolved) {
            resolved = true
            resolve()
          }
        })
    })

    // 获取页面内容，使用智能选择器
    const rule = getSiteExtractionRule(url)
    const selectors = rule.selector.split(',').map((s) => s.trim())

    const content = await hiddenWindow.webContents.executeJavaScript(`
      (function() {
        const selectors = ${JSON.stringify(selectors)};
        let specificContent = null;
        let matchedSelector = '无匹配';

        // 尝试每个选择器
        for (const selector of selectors) {
          let element = null;
          try {
            if (selector.startsWith('#')) {
              element = document.getElementById(selector.substring(1));
            } else if (selector.startsWith('.')) {
              element = document.querySelector(selector);
            } else {
              element = document.querySelector(selector);
            }

            if (element && element.innerHTML.trim()) {
              specificContent = element.innerHTML;
              matchedSelector = selector;
              console.log('成功匹配选择器:', selector);
              break;
            }
          } catch (e) {
            console.warn('选择器匹配失败:', selector, e);
          }
        }

        return {
          html: document.documentElement.outerHTML,
          title: document.title,
          url: window.location.href,
          specificContent: specificContent,
          textContent: specificContent || document.body.textContent,
          extractionRule: '${rule.description}',
          extractedSelector: matchedSelector
        };
      })()
    `)
    hiddenWindow.close()
    return { success: true, data: content }
  } catch (error) {
    console.error('获取网页内容失败:', error)
    return { success: false, error: error.message }
  }
})

/**
 * 打开登录窗口 - 专门用于登录千里马等需要认证的网站
 * 登录后的状态会自动保存到主窗口的session中，供后续内容获取使用
 */
ipcMain.handle('open-login-window', async (event, url) => {
  try {
    // 提取网站的根域名用于登录
    const urlObj = new URL(url)
    const loginUrl = `${urlObj.protocol}//${urlObj.hostname}`

    // 创建登录窗口，共享主窗口的session
    const loginWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false,
        session: win.webContents.session // 关键：共享session，登录状态会保存到主窗口
      },
      title: '网站登录 - 登录完成后请关闭此窗口',
      icon: path.join(__static, 'icon.png')
    })

    // 加载网站首页或登录页面
    await loginWindow.loadURL(loginUrl)

    // 监听窗口关闭事件
    loginWindow.on('closed', () => {
      console.log('登录窗口已关闭，登录状态已保存')
    })

    return {
      success: true,
      message: '登录窗口已打开，请完成登录后关闭窗口。登录状态将自动保存。',
      loginUrl: loginUrl
    }
  } catch (error) {
    console.error('打开登录窗口失败:', error)
    return { success: false, error: error.message }
  }
})

// 全局变量存储预览BrowserView
let previewBrowserView = null

/**
 * 创建预览BrowserView - 在主窗口中嵌入完整的原网页
 */
// ipcMain.handle('create-preview-browserview', async (event, url) => {
//   try {
//     const { BrowserView } = require('electron')

//     // 如果已存在预览视图，先销毁
//     if (previewBrowserView) {
//       win.removeBrowserView(previewBrowserView)
//       previewBrowserView.destroy()
//       previewBrowserView = null
//     }

//     // 创建新的BrowserView，共享主窗口的session
//     previewBrowserView = new BrowserView({
//       webPreferences: {
//         nodeIntegration: false,
//         contextIsolation: true,
//         webSecurity: false,
//         session: win.webContents.session // 关键：共享session，包括登录状态
//       }
//     })

//     // 将BrowserView添加到主窗口
//     win.setBrowserView(previewBrowserView)

//     // 获取主窗口尺寸并设置BrowserView位置
//     const bounds = win.getBounds()
//     const previewBounds = {
//       x: 100,
//       y: 150, // 为顶部控件留出空间
//       width: Math.floor(bounds.width * 0.6), // 占窗口宽度的60%
//       height: 600
//     }
//     previewBrowserView.setBounds(previewBounds)

//     // 加载目标网页
//     try {
//       await previewBrowserView.webContents.loadURL(url)
//       console.log('预览页面加载成功:', url)
//     } catch (loadError) {
//       console.error('预览页面加载失败:', loadError)
//       throw new Error('页面加载失败: ' + loadError.message)
//     }

//     // 监听窗口大小变化，自动调整BrowserView大小
//     const resizeHandler = () => {
//       if (previewBrowserView && previewBrowserView.webContents && !previewBrowserView.webContents.isDestroyed()) {
//         try {
//           const newBounds = win.getBounds()
//           const newPreviewBounds = {
//             x: 20,
//             y: 150,
//             width: Math.floor(newBounds.width * 0.6),
//             height: 600
//           }
//           previewBrowserView.setBounds(newPreviewBounds)
//         } catch (error) {
//           console.error('调整预览窗口大小失败:', error)
//         }
//       }
//     }

//     // 移除之前的监听器，添加新的
//     win.removeAllListeners('resize')
//     win.on('resize', resizeHandler)

//     console.log('预览BrowserView创建成功，URL:', url)

//     return {
//       success: true,
//       message: '预览视图已创建，显示完整的原网页（包含登录状态）',
//       url: url
//     }
//   } catch (error) {
//     console.error('创建预览BrowserView失败:', error)
//     return { success: false, error: error.message }
//   }
// })

/**
 * 销毁预览BrowserView
 */
ipcMain.handle('destroy-preview-browserview', async () => {
  try {
    if (previewBrowserView) {
      // 移除resize监听器
      win.removeAllListeners('resize')

      // 安全地移除和销毁BrowserView
      try {
        win.removeBrowserView(previewBrowserView)
      } catch (removeError) {
        console.warn('移除BrowserView时出现警告:', removeError.message)
      }

      try {
        if (previewBrowserView.webContents && !previewBrowserView.webContents.isDestroyed()) {
          previewBrowserView.webContents.destroy()
        }
      } catch (destroyError) {
        console.warn('销毁BrowserView webContents时出现警告:', destroyError.message)
      }

      previewBrowserView = null
      console.log('预览BrowserView已销毁')
    }

    return {
      success: true,
      message: '预览视图已关闭'
    }
  } catch (error) {
    console.error('销毁预览BrowserView失败:', error)
    return { success: false, error: error.message }
  }
})

/**  打开一个新窗口预览，不嵌入网页*/
ipcMain.handle('create-preview-browserview', async (event, url) => {
  const showWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false, // 允许跨域
      allowRunningInsecureContent: true,
      session: win.webContents.session, // 关键：共享主窗口的session，包括cookies和登录状态
      // 设置User-Agent，避免被识别为自动化工具
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
  })
  showWindow.loadURL(url)
  
    return {
      success: true,
      message: '预览视图已创建，显示完整的原网页（包含登录状态）',
      url: url
    }
})
// #endregion

// #region 应用生命周期管理 - 2025-09-15
// 监听主窗口关闭事件，清理预览BrowserView
if (win) {
  win.on('closed', () => {
    if (previewBrowserView) {
      try {
        if (previewBrowserView.webContents && !previewBrowserView.webContents.isDestroyed()) {
          previewBrowserView.webContents.destroy()
        }
        previewBrowserView = null
        console.log('窗口关闭时已清理预览BrowserView')
      } catch (error) {
        console.warn('窗口关闭时清理预览BrowserView出现警告:', error.message)
      }
    }
  })
}

// 监听应用退出事件，清理资源
app.on('before-quit', () => {
  console.log('应用即将退出，清理资源')
  if (previewBrowserView) {
    try {
      if (win && !win.isDestroyed()) {
        win.removeBrowserView(previewBrowserView)
      }
      if (previewBrowserView.webContents && !previewBrowserView.webContents.isDestroyed()) {
        previewBrowserView.webContents.destroy()
      }
      previewBrowserView = null
    } catch (error) {
      console.warn('应用退出时清理预览BrowserView出现警告:', error.message)
    }
  }
})
// #endregion
