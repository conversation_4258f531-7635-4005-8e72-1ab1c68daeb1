<template>
  <div class="">
    <div class="upload-box">
      <div class="file-box">
        <div v-show="showLeftArrow" class="arrow direction_left" @click="togglePage('last')"><i class="el-icon-arrow-left"></i></div>
        <div class="file-cut">
          <ul class="file-list horizontal" :style="{ transform: fileHorizontalTransformStyle }">
            <li v-for="item in fileList" :key="item.name">
              <img src="@/assets/agent/pdf_icon.png" alt="" />
              <div class="item-info">
                <el-tooltip effect="dark" :content="item.name" placement="top">
                  <p>{{ item.name }}</p>
                </el-tooltip>
                <p>{{ formattingFileSize(item.size) }}</p>
              </div>
              <i class="el-icon-error" @click="removeFile(item)"></i>
            </li>
          </ul>
        </div>
        <div v-show="showRightArrow" class="arrow direction_right" @click="togglePage('next')"><i class="el-icon-arrow-right"></i></div>
      </div>

      <el-upload
        ref="uploadRef"
        multiple
        accept=".pdf"
        :limit="5"
        :disabled="fileList.length >= 5 || loading"
        :show-file-list="false"
        :auto-upload="false"
        action="#"
        :file-list="fileList"
        :on-exceed="handleExceed"
        :on-change="handleChange"
      >
        <span slot="trigger" class="upload_button" :class="{ disabled: fileList.length >= 5 }">
          <img src="@/assets/agent/upload_icon.png" alt="" />
          上传文件
        </span>
        <img class="send-icon" :class="{ disabled: loading }" src="@/assets/agent/send_icon.png" alt="" @click="sendData" />
      </el-upload>
    </div>
  </div>
</template>
<script>
export default {
  name: '',
  data() {
    return {
      fileList: [],
      filePageNum: 1
    }
  },
  computed: {
    fileHorizontalTransformStyle() {
      return `translateX(-${(this.filePageNum - 1) * 510}px)`
    },
    showLeftArrow() {
      return this.filePageNum > 1 && this.fileList.length > 2
    },
    showRightArrow() {
      return this.fileList.length > 2 && this.fileList.length / 2 > this.filePageNum
    }
  },
  created() {},
  methods: {
    handleExceed() {
      this.$message.warning('最多只能上传5个文件')
    },
    handleChange(file) {
      // 限制大小<10Mb
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件大小不能超过10Mb')
        return false
      }
      const findData = this.fileList.find((item) => item.name === file.name)
      if (!findData || findData.name !== file.name) {
        this.fileList.push(file)
      }

      return true
    },
    togglePage(type) {
      if (type === 'next') {
        this.filePageNum++
      } else {
        this.filePageNum--
      }
    },
    removeFile(item) {
      this.fileList = this.fileList.filter((file) => file.uid !== item.uid)
      if (this.filePageNum > 1) {
        if (this.fileList.length % 2 === 0 && this.filePageNum * 2 > this.fileList.length) {
          this.filePageNum--
        }
      }
    },
    async sendData() {
      if (this.loading) return ''
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择文件')
        return
      }

      this.restData()
      this.loading = true
      this.invoiceList = JSON.parse(JSON.stringify(this.fileList))

      try {
        // 创建 FormData 对象
        const formData = new FormData()

        // 将所有文件添加到 FormData 中
        this.fileList.forEach((file, index) => {
          formData.append('files', file.raw || file)
        })

        // 清空文件列表
        this.fileList = []
        // 调用 API 接口
        const response = await ai_invoice_extract(formData)

        // 处理返回的数据
        if (response && response.data) {
          // 这里可以根据实际返回的数据结构来处理结果
          this.excelData = response.data
          if (this.excelData.length === 0) {
            this.$message.warning('不是发票文件')
            return
          }
          this.scrollToBottom()
        }
      } catch (error) {
        console.error('上传失败:', error)
        this.$message.error('文件上传失败，请重试')
        this.invoiceList = []
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
<style scoped lang="scss"></style>
