<template>
  <div v-if="!emptylist || emptylist.length <= 0" class="emptybox" :style="'height:' + emptyheight + ';'">
    <div class="emptyitem">
      <slot>
        <img src="@/assets/resources/empty.png" />
      </slot>
      <div class="emptyitem_text">{{ emptytext }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Emptymain',
  props: {
    emptytext: {
      type: String,
      default: '暂无数据'
    },
    emptylist: {
      type: Array,
      default: () => {
        return []
      }
    },
    emptyheight: {
      type: String,
      default: '500px'
    }
  }
}
</script>
