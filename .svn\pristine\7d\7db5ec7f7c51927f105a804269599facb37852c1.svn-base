<template>
  <div class="app-container">
    <el-form ref="searchForm" label-width="80px" inline class="searchForm">
      <el-form-item label="合同编号:">
        <el-input v-model="queryInfo.contractCode" size="small" placeholder="请输入合同编号" clearable></el-input>
      </el-form-item>
      <el-form-item label="合同名称:">
        <el-input v-model="queryInfo.contractName" size="small" placeholder="请输入合同名称" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item label="项目名称:">
        <el-input v-model="queryInfo.projectName" size="small" placeholder="请输入项目名称" clearable></el-input>
      </el-form-item> -->
      <el-form-item label="客户名称:">
        <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="签订日期:">
        <el-date-picker
          v-model="signingTime"
          size="small"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="datePickerChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="质保日期:">
        <el-date-picker
          v-model="warrantyTime"
          size="small"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="warrantyTimeChange"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="退质保金日期:" label-width="120px">
        <el-date-picker
          v-model="refundTime"
          size="small"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="refundTimeChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" @click="getContract_contractList">查询</el-button>
        <el-button type="primary" size="small" plain @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="addDialog = true">新增合同</el-button>
        <el-button type="primary" size="small" @click="exportList">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border @row-click="lookDeatils">
      <el-table-column align="center" prop="contractName" label="合同名称" width="width"> </el-table-column>
      <!-- <el-table-column align="center" prop="projectName" label="项目名称" width="width"> </el-table-column> -->
      <el-table-column align="center" prop="customerName" label="客户名称" width="width"> </el-table-column>
      <el-table-column align="center" prop="signingTime" label="签订日期" width="width"> </el-table-column>
      <el-table-column align="center" prop="warrantyTime" label="质保日期" width="width"> </el-table-column>
      <el-table-column align="center" prop="refundTime" label="退质保金日期" width="width"> </el-table-column>
      <el-table-column align="center" prop="realityRefundTime" label="实际退质保金日期" width="width"> </el-table-column>
      <!-- <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column> -->
      <el-table-column align="center" prop="prop" label="操作" width="width">
        <template v-slot="{ row }">
          <el-button type="warning" size="small" @click.stop="edit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="list.length > 0"
      layout="total,prev, pager, next"
      style="margin-top: 15px; text-align: right"
      :page-sizes="[5, 10, 15, 20]"
      background
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      @size-change="getContract_contractList"
      @current-change="getContract_contractList"
    />

    <el-dialog :title="dialogTitle" :visible.sync="addDialog" width="850px" top="2vh" :close-on-click-modal="false" @close="addContractDialogClose">
      <el-form ref="form" :model="formInfo" label-width="140px" :rules="rules" inline>
        <el-form-item label="客户名称:" prop="customerId" class="level">
          <el-select v-model="formInfo.customerId" filterable placeholder="请选择客户" @focus="getSecretCustomerCustomerList">
            <el-option v-for="item in allCustomer" :key="item.customerId" :value="item.customerId" :label="item.customerName"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="签订日期:" prop="signingTime">
          <el-date-picker v-model="formInfo.signingTime" type="date" value-format="yyyy-MM-dd" placeholder="签订日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="验收日期:">
          <el-date-picker v-model="formInfo.acceptanceTime" type="date" value-format="yyyy-MM-dd" placeholder="验收日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="质保日期:">
          <el-date-picker v-model="formInfo.warrantyTime" type="date" value-format="yyyy-MM-dd" placeholder="质保日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="退质保金日期:">
          <el-date-picker v-model="formInfo.refundTime" type="date" value-format="yyyy-MM-dd" placeholder="退质保金日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="实际退质保金日期:">
          <el-date-picker v-model="formInfo.realityRefundTime" type="date" value-format="yyyy-MM-dd" placeholder="实际退质保金日期"> </el-date-picker>
        </el-form-item>
        <el-form-item label="合同名称:" prop="contractName" class="aloneLine">
          <el-input v-model="formInfo.contractName" placeholder="请输入合同名称" type="textarea" maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="经销商名称:" class="aloneLine">
          <el-input v-model="formInfo.dealerName" placeholder="请输入经销商名称" type="textarea" maxlength="500"></el-input>
        </el-form-item>
        <el-form-item label="产品内容:" class="aloneLine">
          <el-input v-model="formInfo.remark" placeholder="请输入产品内容" type="textarea" :autosize="{ minRows: 5, maxRows: 6 }" maxlength="2000"></el-input>
        </el-form-item>
        <!-- <el-form-item label="项目名称:" prop="projectId" class="level">
          <el-select v-model="formInfo.projectId" placeholder="请选择项目" size="small" @focus="getProjectList">
            <el-option v-for="item in projectList" :key="item.id" :value="item.id" :label="item.name"> </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="上传合同:">
          <el-upload class="upload-demo" drag :action="action" :limit="1" :headers="header" :file-list="fileList" :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传一个文件、 上传类型: .doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addDialog = false">取 消</el-button>
        <el-button type="primary" @click="submitContractData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { secretCustomeAllCustomer } from '@/api/clientele'
import { contract_contractList, contract_saveContract, contract_updateContract, contract_exportList } from '@/api/contract'
import { projectList } from '@/api/project'
import { mapGetters } from 'vuex'

export default {
  name: '',
  data() {
    return {
      queryInfo: {
        contractCode: null,
        contractName: null, // 合同名称
        projectName: null, // 项目名称
        customerName: null, // 客户名称
        signingStartTime: null, // 签订日期开始时间
        signingEndTime: null, // 签订日期结束时间
        warrantyStartTime: null, // 质保日期开始时间
        warrantyEndTime: null, // 质保日期结束时间
        refundStartTime: null, // 退质保金日期开始时间
        refundEndTime: null, // 退质保金日期结束时间
        pageNum: 1,
        pageSize: 10
      },
      signingTime: null,
      warrantyTime: null,
      refundTime: null,
      list: [],
      total: 0,
      addDialog: false,
      formInfo: {
        contractName: null, // 合同名称
        customerId: null, // 客户id
        projectId: null, // 项目id
        remark: null, // 备注
        meetingFileReqs: [], // 附件
        signingTime: null, // 签订日期
        acceptanceTime: null, // 验收日期
        warrantyTime: null, // 质保日期
        refundTime: null, // 退质保金日期
        realityRefundTime: null, // 实际退质保金日期
        dealerName: null // 经销商名称
      },
      allCustomer: [],
      projectList: [],
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      fileList: [],
      rules: {
        contractName: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
        customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
        signingTime: [{ required: true, message: '请选择签订日期', trigger: 'change' }]
        // projectId: [{ required: true, message: '请选择项目', trigger: 'change' }]
        // meetingFileReqs: [{ required: true, message: '请上传合同', trigger: 'change', type: 'array' }]
      }
    }
  },
  computed: {
    ...mapGetters(['token']),
    dialogTitle() {
      return this.formInfo.contractId ? '修改合同' : '新增合同'
    }
  },
  created() {
    this.getContract_contractList()
  },
  methods: {
    // 获取合同列表
    async getContract_contractList() {
      const { data } = await contract_contractList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.signingStartTime = val[0]
        this.queryInfo.signingEndTime = val[1]
      } else {
        this.queryInfo.signingStartTime = null
        this.queryInfo.signingEndTime = null
      }
      this.getContract_contractList()
    },
    warrantyTimeChange(val) {
      if (val) {
        this.queryInfo.warrantyStartTime = val[0]
        this.queryInfo.warrantyEndTime = val[1]
      } else {
        this.queryInfo.warrantyStartTime = null
        this.queryInfo.warrantyStartTime = null
      }
      this.getContract_contractList()
    },
    refundTimeChange(val) {
      if (val) {
        this.queryInfo.refundStartTime = val[0]
        this.queryInfo.refundEndTime = val[1]
      } else {
        this.queryInfo.refundStartTime = null
        this.queryInfo.refundEndTime = null
      }
      this.getContract_contractList()
    },

    // 搜索重置
    reset() {
      this.queryInfo = {
        contractCode: null,
        contractName: null, // 合同名称
        projectName: null, // 项目名称
        customerName: null, // 客户名称
        signingStartTime: null, // 签订日期开始时间
        signingEndTime: null, // 签订日期结束时间
        warrantyStartTime: null, // 质保日期开始时间
        warrantyEndTime: null, // 质保日期结束时间
        refundStartTime: null, // 退质保金日期开始时间
        refundEndTime: null, // 退质保金日期结束时间
        pageNum: 1,
        pageSize: 10
      }
      this.signingTime = null
      this.warrantyTime = null
      this.refundTime = null
      this.getContract_contractList()
    },
    async exportList() {
      const { data } = await contract_exportList(this.queryInfo)
      const headers = {
        合同名称: 'contractName',
        客户名称: 'customerName',
        签订日期: 'signingTime',
        质保日期: 'warrantyTime',
        退质保金日期: 'refundTime',
        实际退质保金日期: 'realityRefundTime'
      }
      const res = this.formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '合同列表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          return item[headers[key]]
        })
      })
    },
    // 添加合同-查询所有客户
    async getSecretCustomerCustomerList() {
      const { data } = await secretCustomeAllCustomer()
      this.allCustomer = data
    },
    // 添加合同- 获取项目列表
    async getProjectList() {
      const { data } = await projectList({ type: 1, pageNum: 1, pageSize: 2000 })
      this.projectList = data.list
      console.log(data)
    },
    // 添加合同- 上传前触发事件
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    // 添加合同- 上传成功事件
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 添加合同- 上传删除事件
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    // 添加/修改合同 - 提交数据
    submitContractData() {
      this.$refs['form'].validate(async (val) => {
        if (!val) return
        const list = []
        if (this.fileList.length > 0) {
          this.fileList.forEach((item) => {
            console.log(item)
            list.push({
              fileName: item.name,
              fileSize: item.size / 1024,
              fileUrl: item.response.data[0]
            })
          })
        }
        this.formInfo.meetingFileReqs = list
        if (this.formInfo.contractId) {
          await contract_updateContract(this.formInfo)
          this.$message.success('修改合同成功!')
          this.getContract_contractList()
          this.addDialog = false
        } else {
          await contract_saveContract(this.formInfo)
          this.$message.success('新增合同成功!')
          this.addContractDialogClose()
          this.getContract_contractList()
        }
      })
    },
    // 添加合同 - dialog关闭事件
    addContractDialogClose() {
      this.formInfo = {
        contractName: null, // 合同名称
        customerId: null, // 客户id
        projectId: null, // 项目id
        remark: null, // 备注
        meetingFileReqs: [], // 附件
        signingTime: null, // 签订日期
        acceptanceTime: null, // 验收日期
        warrantyTime: null, // 质保日期
        refundTime: null, // 退质保金日期
        realityRefundTime: null, // 实际退质保金日期
        dealerName: null // 经销商名称
      }
      this.fileList = []
      this.$refs['form'].resetFields()
    },
    // 表格 编辑按钮操作
    edit(row) {
      this.formInfo = { ...row }
      if (this.formInfo.fileDtos.length > 0) {
        this.formInfo.fileDtos.forEach((item) => {
          this.fileList.push({
            name: item.fileName,
            size: item.fileSize * 1024,
            response: {
              data: [item.fileUrl]
            }
          })
        })
      }
      this.addDialog = true
      this.getSecretCustomerCustomerList()
      this.getProjectList()
    },
    // 查看详情
    lookDeatils(val) {
      this.$router.push(`/contract/details/${val.contractId}`)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-table__row {
    cursor: pointer;
  }
  .el-dialog__body {
    padding: 20px 50px;
    max-height: 750px;
    overflow: auto;
    .el-input__inner {
      width: 220px;
    }
    .aloneLine {
      width: 100%;
      .el-form-item__content {
        width: calc(100% - 140px);

        .el-textarea__inner {
          width: 100%;
        }
      }
    }
    .el-upload-dragger {
      width: 180px;
      height: 90px;
      .el-icon-upload {
        margin: 0;
        margin-top: 5px;
        font-size: 40px;
      }
      .el-upload__text {
        margin-top: -10px;
        font-size: 12px;
      }
    }
  }
}
</style>
