<template>
  <div class="logStatistics">
    <el-card style="width: 187px" class="logStatistics_content_left">
      <div slot="header" class="header">人员列表</div>
      <div>
        <el-tree
          ref="Tree"
          :data="personList"
          node-key="userId"
          :props="defaultProps"
          default-expand-all
          check-on-click-node
          highlight-current
          :current-node-key="currentKey"
          @node-click="handleNodeClick"
        >
          <template v-slot="{ data }">
            <div><img v-if="data.realName === '销售部门'" src="@/assets/sign/user_icon.png" alt="" /> {{ data.realName }}</div>
          </template>
        </el-tree>
      </div>
    </el-card>
    <el-card style="flex: 1" class="logStatistics_content_right">
      <div slot="header">
        <div class="header_left">
          <div class="title">日志统计</div>
          <div class="status"><span class="rounds error"></span> 未提交<span class="rounds success"></span>已提交</div>
        </div>
        <div class="header_center">
          <i class="el-icon-arrow-left" @click="MonthReduce"></i>
          <span>{{ time }}</span>
          <i class="el-icon-arrow-right" @click="MonthAdd"></i>
        </div>
        <div class="header_right">
          本月提交:
          <span>{{ list.length }}次</span>
        </div>
      </div>
      <div>
        <el-calendar v-model="signDate">
          <template v-slot:dateCell="{ data }">
            <div :style="my_status(data.day)" class="day">
              <span>{{ data.day.split('-').slice(2).join('-') }}</span>
              <span class="text">{{ my_Data(data.day) ? '已提交' : '未提交' }}</span>
            </div>
          </template>
        </el-calendar>
      </div>
    </el-card>
  </div>
</template>

<script>
import { sysUserList } from '@/api/sign'
import { secretLog_logStatis } from '@/api/secretLog'
import { mapGetters } from 'vuex'
import { formatDate } from '@/filters'
export default {
  name: '',

  data() {
    return {
      currentKey: null,
      personList: [],
      signPersonInfo: {
        pageNum: 1,
        pageSize: 2000
      },
      defaultProps: {
        children: 'children',
        label: 'realName'
      },
      logStatisInfo: {
        userId: null,
        time: null
      },
      originalTime: null,
      signDate: null,
      time: null,
      list: []
    }
  },
  computed: {
    ...mapGetters(['userId'])
  },
  created() {
    this.getUserList()
    this.time = formatDate(new Date(), 'yyyy年MM月')
    this.originalTime = formatDate(new Date(), 'yyyy-MM')
  },

  methods: {
    // 获取签到统计列表
    getUserList() {
      sysUserList({ ...this.signPersonInfo, organizationId: 56642510 }).then((response) => {
        this.personList = [
          {
            realName: '销售部门',
            children: response.data.list
          }
        ]
        if (response.data.list.length > 0) {
          this.$nextTick(() => {
            this.currentKey = response.data.list[0].userId
            this.$refs.Tree.setCurrentKey(this.currentKey)
            this.logStatisInfo.userId = this.currentKey
            this.logStatisInfo.time = formatDate(new Date(), 'yyyy-MM')
            this.getLogStatis()
          })
        }
      })
    },

    async getLogStatis() {
      const { data } = await secretLog_logStatis(this.logStatisInfo)
      this.list = data
    },
    handleNodeClick(data) {
      if (data.userId) {
        this.logStatisInfo.userId = data.userId
        this.logStatisInfo.time = formatDate(this.originalTime, 'yyyy-MM')
        this.getLogStatis()
      }
    },
    // 点击月份加一
    MonthAdd() {
      let currentDate = this.originalTime
      currentDate = new Date(currentDate)
      let nextDate = currentDate.setMonth(currentDate.getMonth() + 1) // 输出日期格式为毫秒形式1556668800000
      nextDate = new Date(nextDate)
      const nextYear = nextDate.getFullYear()
      const nextMonth = this.checkMonth(nextDate.getMonth() + 1) // 因日期中的月份表示为0-11，所以要显示正确的月份，需要 + 1
      nextDate = nextYear + '-' + nextMonth // "2019-05"
      this.originalTime = nextDate
      this.signDate = this.originalTime
      this.time = formatDate(this.originalTime, 'yyyy年MM月')
      this.logStatisInfo.time = formatDate(this.originalTime, 'yyyy-MM')
      this.getLogStatis()
    },
    // 点击月份减一
    MonthReduce() {
      let currentDate = this.originalTime
      currentDate = new Date(currentDate)
      let lastDate = currentDate.setMonth(currentDate.getMonth() - 1) // 输出日期格式为毫秒形式1551398400000
      lastDate = new Date(lastDate)
      const lastYear = lastDate.getFullYear()
      const lastMonth = this.checkMonth(lastDate.getMonth() + 1) // 因日期中的月份表示为0-11，所以要显示正确的月份，需要 + 1
      lastDate = lastYear + '-' + lastMonth // "2019-03"
      this.originalTime = lastDate
      this.signDate = this.originalTime
      this.time = formatDate(this.originalTime, 'yyyy年MM月')
      this.logStatisInfo.time = formatDate(this.originalTime, 'yyyy-MM')
      this.getLogStatis()
    },
    checkMonth(i) {
      if (i < 10) {
        i = '0' + i
      }
      return i
    },
    my_status(date) {
      const result = this.list.find((item) => item.time === date)
      return result ? 'background:#d3ecd3;color:#1d7d37' : 'background:#ecd8d3;color:#dc6173'
      // return result ? (result.signCount === 1 ? 'background:#d3ecd3;color:#1d7d37' : 'background:#ecd8d3;color:#dc6173') : ' background:'
    },
    my_Data(date) {
      const result = this.list.find((item) => item.time === date)
      return !!result
    }
  }
}
</script>

<style scoped lang="scss">
.logStatistics {
  display: flex;
  margin-top: 15px;
  padding-left: 15px;
  padding-right: 15px;
  .logStatistics_content_left {
    overflow: auto;
    ::v-deep {
      .el-card__header {
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 18px;
      }
      .el-card__body {
        padding-top: 15px;
        padding-left: 0;
        padding-right: 0;
      }
    }
    .header {
      font-size: 14px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
  }
  .logStatistics_content_right {
    border: none;
    ::v-deep {
      .el-card__header {
        border-bottom: none;
        padding-top: 0;
        padding-bottom: 10px;
        & > div {
          display: flex;
          justify-content: space-between;
          .header_left {
            display: flex;
            .title {
              margin-right: 40px;
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            .status {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
              .rounds {
                display: inline-block;
                width: 11px;
                height: 9px;
                margin-right: 6px;
              }
              .error {
                background: #dc6173;
              }
              .success {
                background: #d3ecd3;
                margin-left: 20px;
              }
            }
          }
          .header_center {
            margin-left: -180px;
            span {
              padding: 0 15px;
              font-size: 16px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            i {
              color: #9095a7;
              cursor: pointer;
            }
          }
          .header_right {
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            color: #0b1a44;
            span {
              font-size: 16px;
              font-weight: bold;
              color: #3464e0;
            }
          }
        }
      }
      .el-card__body {
        padding: 8px;
        background: #f5f5f5;
        border-radius: 8px;
        // 日历更改演示 <--
        .el-calendar__body {
          padding: 10px;
          border-radius: 8px;
          max-height: 736px;
          overflow: auto;
        }
        .el-calendar__header {
          display: none;
        }
        .el-calendar-table thead th {
          padding-top: 0;
          &::before {
            content: '周';
          }
        }
        .el-calendar-table:not(.is-range) td.next,
        .el-calendar-table:not(.is-range) td.prev {
          pointer-events: none;
        }
        .el-calendar-table:not(.is-range) td.next {
          // visibility: hidden;
        }
        .el-calendar-day {
          width: 100%;
          height: 100%;
          position: relative;
          padding: 0;
          .day {
            position: relative;
            padding-left: 10px;
            padding-top: 10px;
            width: 100%;
            height: 100%;
            font-size: 16px;
            font-weight: bold;
            .text {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }

        .current {
          width: 217px;
          height: 110px;
          min-height: 110px;
        }
        .el-calendar-table__row {
          .prev,
          .next {
            width: 217px;
            height: 110px;
            .day {
              background: #fff !important;
              color: #b1bac7 !important;
              .text {
                display: none;
              }
            }
          }
        }

        .el-date-editor--year,
        .el-date-editor--month {
          width: 150px;
          .el-input__inner {
            width: 100%;
          }
        }
        // 日历更改演示 -->
      }
    }
  }
}

::v-deep {
  .el-card {
    box-shadow: none;
  }
  .el-tabs__header {
    margin-bottom: 0;
  }
  // <-- 设置tabs样式
  .el-tabs__item {
    font-size: 16px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .el-tabs__item.is-active {
    color: #3464e0;
  }
  .el-tabs__active-bar {
    background-color: #3464e0;
    height: 1px;
  }
  .el-tabs__nav-wrap::after {
    background: #eeeeef;
    height: 1px;
  }
  // 设置tabs样式 -->

  // <--设置树形样式
  .el-tree-node {
    height: 36px;
  }
  .el-tree-node__content {
    height: 100%;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #4a4a4a;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    background: #eff3fd;
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #3464e0;
  }
  // 设置树形样式 -->
}
</style>
