import request from '@/utils/request'
/** 会议预约
 * @param  {string }  conferenceTitle  required 会议主题
 * @param  {string }  conferenceRoom  required 会议室
 * @param  {string }  reservedDate  required 日期
 * @param  {string }  startTime  required 开始时间
 * @param  {string }  endTime  required 结束时间
 * @param  {string }  conferenceUserIds  required 参会人员Id（逗号分隔）
 * @param  {string }  conferenceUserNames  required 参会人员姓名（逗号分隔）
 */
export function conference_add(data) {
  return request({
    url: '/conference/add',
    method: 'post',
    data
  })
}
/** 会议查询
 * @param  {string }  dateStr  required 日期
 */
export function conference_list(params) {
  return request({
    url: '/conference/list',
    method: 'get',
    params
  })
}
/** 空档建议
 * @param  {string }  dateStr  required 日期
 */
export function conference_neutral_list(params) {
  return request({
    url: '/conference/neutral/gear',
    method: 'get',
    params
  })
}
/** 会议修改
 * @param  {string }  conferenceTitle  required 会议主题
 * @param  {string }  conferenceRoom  required 会议室
 * @param  {string }  reservedDate  required 日期
 * @param  {string }  startTime  required 开始时间
 * @param  {string }  endTime  required 结束时间
 * @param  {string }  conferenceUserIds  required 参会人员Id（逗号分隔）
 * @param  {string }  conferenceUserNames  required 参会人员姓名（逗号分隔）
 */
export function conference_update(data) {
  return request({
    url: '/conference/update',
    method: 'post',
    data
  })
}

/** 取消会议
 * @param  conferenceId  required 会议id
 * @param  cancelDescription	  required 取消原因
 */
export function conference_cancel(params) {
  return request({
    url: '/conference/cancel',
    method: 'get',
    params
  })
}
/** 下一个会议
 * @param  {string }  dateStr  required 日期
 */
export function conference_next(params) {
  return request({
    url: '/conference/next',
    method: 'get',
    params
  })
}

/** 未来预告
 * @param  {string }  dateStr  required 日期
 */
export function conference_preview(params) {
  return request({
    url: '/conference/preview',
    method: 'get',
    params
  })
}

/** 获取推荐常用参会人 */
export function conference_common_contact() {
  return request({
    url: '/conference/common/contact',
    method: 'get'
  })
}

/** 获取部门列表 */
export function conference_org_select() {
  return request({
    url: '/conference/org/select',
    method: 'get'
  })
}

/** 查询会议纪要 @param {number} conferenceId 会议id */
export function conference_node(conferenceId) {
  return request({
    url: '/conference/node',
    method: 'get',
    params: {
      conferenceId
    }
  })
}

/** 上传录音
 * @param {string} conferenceId 会议id
 * @param {file} file 音频文件
 * */
export function conference_import_audio(data) {
  return request({
    url: '/conference/import/audio',
    method: 'post',
    data
  })
}
