<template>
  <el-drawer :visible="visible" direction="rtl" size="780px" :show-close="false" :modal="true" class="meeting-detail-drawer" @open="getMettingSummary">
    <template v-slot:title>
      <div class="drawer-title">会议详情</div>
      <div class="close-btn" @click="handleClose">
        <i class="el-icon-close"></i>
      </div>
    </template>
    <div class="drawer-content">
      <!-- 顶部标题 -->
      <div class="drawer-header" :class="roomTypeClass">
        <div class="room-info">
          <svg-icon icon-class="newMeeting" class="room-icon"></svg-icon>
          <div class="room-title">
            {{ roomTitle }}
          </div>
          <div class="time-info" :style="{ color: roomColor }">（{{ meetingData.startTime }}-{{ meetingData.endTime }}）·（{{ durationText }}）</div>
        </div>
      </div>

      <!-- 详情内容 -->
      <div class="detail-content">
        <div class="detail-item">
          <div class="detail-label">会议主题:</div>
          <div class="detail-value">{{ meetingData.conferenceTitle }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">预约日期:</div>
          <div class="detail-value">{{ formatDate(meetingData.reservedDate) }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">会议时间:</div>
          <div class="detail-value">{{ meetingData.startTime }} - {{ meetingData.endTime }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">会议时长:</div>
          <div class="detail-value">{{ durationText }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">会议室:</div>
          <div class="detail-value">{{ roomTitle }}</div>
        </div>

        <div class="detail-item">
          <div class="detail-label">预约人:</div>
          <div class="detail-value">{{ meetingData.createUserName }}</div>
        </div>

        <div class="detail-item status">
          <div class="detail-label">会议状态:</div>
          <div class="detail-value">
            <span class="status-tag" :class="statusClass">{{ statusText }}</span>
          </div>
        </div>

        <div class="detail-item participants">
          <div class="detail-label">参会人员:</div>
          <div class="detail-value">
            <div class="participants-list">
              <div v-for="(participant, index) in participantsList" :key="index" class="participant-item">
                {{ participant }}
              </div>
            </div>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-label">创建时间:</div>
          <div class="detail-value">{{ formatDateTime(meetingData.createTime) }}</div>
        </div>
        <div v-if="meetingSummary" class="detail-item summary">
          <div class="detail-label">会议纪要:</div>
          <div class="detail-value">
            <MarkdownRenderer :content="meetingSummary" :conference-title="meetingData.conferenceTitle" />
          </div>
        </div>

        <!-- 录音上传功能 - 仅小会议室显示 -->
        <div v-if="meetingData.conferenceRoom === 'two' && !meetingSummary" class="detail-item audio-upload">
          <div class="detail-label">录音上传:</div>
          <div class="detail-value">
            <el-upload
              class="audio-uploader"
              :action="uploadUrl"
              :data="{ conferenceId: meetingData.conferenceId }"
              :before-upload="beforeAudioUpload"
              :on-success="onAudioUploadSuccess"
              :show-file-list="false"
              accept="audio/*"
            >
              <el-button size="small" type="primary" icon="el-icon-upload"> 上传会议录音 </el-button>
            </el-upload>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import MarkdownRenderer from '@/components/MarkdownRenderer'
import { conference_node } from '@/api/newMeeting.js'

export default {
  name: 'MeetingDetailDrawer',
  components: {
    MarkdownRenderer
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    meetingData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      meetingSummary: ''
    }
  },
  computed: {
    uploadUrl() {
      return window.config.VUE_APP_UPLOAD_Library_URL + '/conference/import/audio'
    },
    roomTypeClass() {
      return this.meetingData.conferenceRoom === 'one' ? 'large-room' : 'small-room'
    },
    roomColor() {
      return this.meetingData.conferenceRoom === 'one' ? '#3465df' : '#13b755'
    },
    roomTitle() {
      return this.meetingData.conferenceRoom === 'one' ? '大会议室' : '小会议室'
    },
    durationText() {
      if (!this.meetingData.startTime || !this.meetingData.endTime) return ''

      const start = this.parseTime(this.meetingData.startTime)
      const end = this.parseTime(this.meetingData.endTime)
      const duration = Math.round((end - start) * 60)

      if (duration < 60) {
        return `${duration}分钟`
      } else {
        const hours = Math.floor(duration / 60)
        const minutes = duration % 60
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
      }
    },
    participantsList() {
      if (!this.meetingData.conferenceUserNames) return []
      return this.meetingData.conferenceUserNames.split(',').filter((name) => name.trim())
    },
    statusText() {
      if (this.meetingData.conferenceStatus === 0) return '已取消'

      const now = new Date()
      const currentDate = now.toISOString().split('T')[0]
      const currentTime = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`

      if (this.meetingData.reservedDate < currentDate) {
        return '已结束'
      }

      if (this.meetingData.reservedDate === currentDate) {
        const meetingStartTime = this.parseTime(this.meetingData.startTime)
        const meetingEndTime = this.parseTime(this.meetingData.endTime)
        const nowTime = this.parseTime(currentTime)

        if (nowTime < meetingStartTime) {
          return '未开始'
        } else if (nowTime >= meetingStartTime && nowTime < meetingEndTime) {
          return '进行中'
        } else {
          return '已结束'
        }
      }

      return '未开始'
    },
    statusClass() {
      const status = this.statusText
      return {
        'status-cancelled': status === '已取消',
        'status-finished': status === '已结束',
        'status-ongoing': status === '进行中',
        'status-upcoming': status === '未开始'
      }
    }
  },
  methods: {
    async getMettingSummary() {
      const { data } = await conference_node(this.meetingData.conferenceId)
      this.meetingSummary = data
    },
    // 解析时间字符串为小时数
    parseTime(timeStr) {
      const [hour, minute] = timeStr.split(':').map(Number)
      return hour + (minute || 0) / 60
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekDay = weekDays[date.getDay()]
      return `${year}-${month}-${day} ${weekDay}`
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''
      const date = new Date(dateTimeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    },

    // 录音上传前的验证
    beforeAudioUpload(file) {
      const isAudio = file.type.startsWith('audio/')

      if (!isAudio) {
        this.$message.error('只能上传音频文件!')
        return false
      }

      return true
    },

    // 录音上传成功
    async onAudioUploadSuccess(response, file) {
      try {
        if (response && response.code === 200) {
          this.getMettingSummary()
        } else {
          throw new Error('上传响应异常')
        }
      } catch (error) {
        console.error('录音上传失败:', error)
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.meeting-detail-drawer {
  ::v-deep .el-drawer {
    border-radius: 0;
    &__header {
      width: 100%;
      height: 50px;
      padding: 0;
      margin: 0;
      background: #fafafa;
      .drawer-title {
        padding-left: 35px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
      .close-btn {
        position: absolute;
        top: 14px;
        right: 24px;
        cursor: pointer;
        i {
          font-size: 20px;
          font-weight: 600;
          color: #657081;
        }
      }
    }
  }

  ::v-deep .el-drawer__body {
    padding: 0;
  }
}

.drawer-content {
  height: 100%;
  padding: 0 35px;
  overflow-y: auto;
  padding-top: 20px;
}

.drawer-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  border-radius: 8px;
  border-left: 6px solid;
  margin-bottom: 30px;

  &.large-room {
    background: #f7f9fe;
    border-color: #4d7bef;
    .room-info {
      .room-icon {
        color: #4d7bef;
      }
      .time-info {
        color: #3465df;
      }
    }
  }

  &.small-room {
    background: #f7fdf9;
    border-color: #13b755;
    .room-info {
      .room-icon {
        color: #13b755;
      }
      .time-info {
        color: #13b755;
      }
    }
  }

  .room-info {
    display: flex;
    align-items: center;
    justify-content: center;
    .room-icon {
      margin-right: 8px;
      font-size: 28px;
    }
    .room-title {
      margin-left: 15px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
    }

    .time-info {
      width: 235px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.detail-content {
  padding-bottom: 40px;
}

.detail-item {
  display: flex;
  margin-bottom: 24px;

  .detail-label {
    width: 100px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    flex-shrink: 0;
  }

  .detail-value {
    flex: 1;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    // line-height: 1.4;
  }
}
.detail-item.status,
.detail-item.participants {
  display: flex;
  align-items: center;
}

// 会议纪要
.detail-item.summary {
  flex-direction: column;
  .detail-value {
    width: 100%;
    padding: 20px;
    margin-top: 20px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #eaeced;
  }
}
.participants-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.participant-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90px;
  height: 40px;
  background: #f3f5f8;
  border-radius: 8px;
  border: 1px solid #e2e2e2;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 15px;
  color: #333333;
}

.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;

  &.status-cancelled {
    background: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }

  &.status-finished {
    background: #f6f6f6;
    color: #999999;
    border: 1px solid #d9d9d9;
  }

  &.status-ongoing {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }

  &.status-upcoming {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }
}

// 录音上传样式
.detail-item.audio-upload {
  display: flex;
  align-items: center;

  .audio-uploader {
    ::v-deep .el-upload {
      display: inline-block;
    }
  }

  .upload-status {
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 4px;

    &.info {
      background: #e6f7ff;
      color: #1890ff;
      border: 1px solid #91d5ff;
    }

    &.success {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    &.error {
      background: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffccc7;
    }
  }
}
</style>
