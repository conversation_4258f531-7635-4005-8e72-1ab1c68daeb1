<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebView集成测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #0ea5e9;
        }
        .content-section {
            background: #fff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .login-form {
            background: #fef3c7;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .user-info {
            background: #d1fae5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #2563eb;
        }
        .logout {
            background: #ef4444;
        }
        .logout:hover {
            background: #dc2626;
        }
        #detailContentHtml {
            background: #f8fafc;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 WebView集成功能测试页面</h1>
        <p>这是一个专门用于测试WebView一体化预览功能的测试页面</p>
    </div>

    <div class="content-section">
        <h2>测试功能列表</h2>
        <ul>
            <li>✅ 页面加载检测</li>
            <li>🔐 登录状态模拟</li>
            <li>📄 内容提取测试</li>
            <li>🍪 Cookie状态检测</li>
        </ul>
    </div>

    <!-- 模拟登录表单 -->
    <div class="content-section">
        <h3>模拟登录区域</h3>
        <div class="login-form" id="loginForm">
            <h4>请登录以查看完整内容</h4>
            <input type="text" id="username" placeholder="用户名" style="margin: 5px; padding: 8px;">
            <input type="password" id="password" placeholder="密码" style="margin: 5px; padding: 8px;">
            <button class="btn" onclick="simulateLogin()">登录</button>
        </div>
        
        <div class="user-info" id="userInfo">
            <span class="username">👤 已登录用户: 测试用户</span>
            <button class="btn logout" onclick="simulateLogout()">退出登录</button>
        </div>
    </div>

    <!-- 主要内容区域 - 这是内容提取的目标 -->
    <div id="detailContentHtml" class="content-section">
        <h2>📋 主要内容区域 (detailContentHtml)</h2>
        <p><strong>项目名称：</strong>WebView一体化预览功能测试项目</p>
        <p><strong>项目编号：</strong>TEST-2025-09-15-001</p>
        <p><strong>发布时间：</strong>2025年9月15日</p>
        
        <h3>项目详情</h3>
        <p>本项目旨在测试WebView组件在Electron应用中的集成效果，验证以下功能：</p>
        <ol>
            <li>页面加载和渲染能力</li>
            <li>JavaScript执行环境</li>
            <li>登录状态检测机制</li>
            <li>内容提取算法效果</li>
            <li>Session共享机制</li>
        </ol>

        <h3>技术规格</h3>
        <ul>
            <li><strong>框架：</strong>Electron + Vue.js</li>
            <li><strong>组件：</strong>WebView Tag</li>
            <li><strong>分区：</strong>persist:main</li>
            <li><strong>安全策略：</strong>Session共享</li>
        </ul>

        <div id="loginOnlyContent" style="display: none; background: #ecfdf5; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <h4>🔐 登录后可见内容</h4>
            <p>恭喜！您已成功登录，可以查看此隐藏内容。</p>
            <p><strong>特殊信息：</strong>这部分内容只有在登录状态下才能看到，用于测试登录状态检测功能。</p>
            <p><strong>Cookie信息：</strong>login=true; session=test-session-123; token=abc123</p>
        </div>
    </div>

    <!-- 其他测试内容 -->
    <div class="content-section">
        <h3>🔍 测试说明</h3>
        <p><strong>使用方法：</strong></p>
        <ol>
            <li>在主程序中输入此页面的URL</li>
            <li>点击"🌐 加载预览"加载此页面</li>
            <li>点击"🔍 检查登录状态"应显示"未登录"</li>
            <li>在webview中点击"登录"按钮模拟登录</li>
            <li>再次点击"🔍 检查登录状态"应显示"已登录"</li>
            <li>点击"📄 提取内容"应能提取到detailContentHtml中的内容</li>
        </ol>
    </div>

    <script>
        // 模拟登录功能
        function simulateLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username && password) {
                // 设置登录Cookie
                document.cookie = "login=true; path=/";
                document.cookie = "session=test-session-123; path=/";
                document.cookie = "token=abc123; path=/";
                document.cookie = "username=" + username + "; path=/";
                
                // 显示登录后的界面
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('userInfo').style.display = 'block';
                document.getElementById('loginOnlyContent').style.display = 'block';
                
                // 更新用户名显示
                document.querySelector('.username').textContent = '👤 已登录用户: ' + username;
                
                alert('✅ 登录成功！现在可以测试登录状态检测功能了。');
            } else {
                alert('⚠️ 请输入用户名和密码');
            }
        }
        
        // 模拟退出登录
        function simulateLogout() {
            // 清除登录Cookie
            document.cookie = "login=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            document.cookie = "session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            document.cookie = "username=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
            
            // 显示登录表单
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('userInfo').style.display = 'none';
            document.getElementById('loginOnlyContent').style.display = 'none';
            
            // 清空输入框
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            
            alert('✅ 已退出登录！');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 测试页面加载完成');
            console.log('📍 当前URL:', window.location.href);
            console.log('🍪 当前Cookies:', document.cookie);
            
            // 检查是否已有登录Cookie
            if (document.cookie.includes('login=true')) {
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('userInfo').style.display = 'block';
                document.getElementById('loginOnlyContent').style.display = 'block';
                
                const username = getCookie('username') || '测试用户';
                document.querySelector('.username').textContent = '👤 已登录用户: ' + username;
            }
        });
        
        // 获取Cookie值的辅助函数
        function getCookie(name) {
            const value = "; " + document.cookie;
            const parts = value.split("; " + name + "=");
            if (parts.length == 2) return parts.pop().split(";").shift();
        }
    </script>
</body>
</html>
