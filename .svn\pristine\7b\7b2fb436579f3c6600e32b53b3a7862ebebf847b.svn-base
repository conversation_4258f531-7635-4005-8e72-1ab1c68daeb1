<template>
  <div class="app-container">
    <el-form ref="form" class="searchForm" :model="queryInfo" label-width="80px" inline>
      <el-form-item label="bug标题:">
        <el-input v-model="queryInfo.title" maxlength="30" placeholder="请输入bug标题"></el-input>
      </el-form-item>
      <el-form-item label="bug状态:">
        <el-select v-model="queryInfo.states" multiple placeholder="请选择bug状态">
          <el-option label=" 待解决" :value="1"> </el-option>
          <el-option label="解决中" :value="2"> </el-option>
          <el-option label="已解决" :value="3"> </el-option>
          <el-option label="已关闭" :value="4"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈人:">
        <el-input v-model="queryInfo.feedbackUser" maxlength="30" placeholder="请输入反馈人"></el-input>
      </el-form-item>
      <el-form-item label="反馈时间:">
        <el-date-picker v-model="time" type="daterange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyy-MM-dd" @change="timeChange"> </el-date-picker>
      </el-form-item>
      <el-form-item label="所属产品:">
        <el-input v-model="queryInfo.productName" maxlength="30" placeholder="请输入所属产品"></el-input>
      </el-form-item>
      <el-form-item label="所属专业:">
        <el-select v-model="queryInfo.majorIds" multiple placeholder="请选择专业">
          <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实验名称:" label-width="90px">
        <el-input v-model="queryInfo.name" maxlength="30" placeholder="请输入实验名称"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="success" size="small" style="margin-left: 20px" @click="getList">查询</el-button>
        <el-button type="primary" plain size="small" @click="reset">重置</el-button>
        <el-button type="primary" size="small" @click="exportList">导出Excel</el-button>
        <el-button v-show="bug_add_edit_remove" type="primary" size="small" @click="add">新增bug</el-button>
        <!-- <el-button type="primary" size="small" @click="add">新增bug</el-button> -->
      </el-form-item>
    </el-form>
    <el-table :data="list" style="width: 100%" border @sort-change="sortChange">
      <el-table-column prop="prop" label="优先级" width="width" align="center" sortable="custom">
        <template v-slot="{ row }">
          <el-tag v-if="row.priority === 1">低</el-tag>
          <el-tag v-if="row.priority === 2" type="warning">中</el-tag>
          <el-tag v-if="row.priority === 3" type="danger">高</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="prop" label="bug状态" width="width" align="center" sortable="custom">
        <template v-slot="{ row }">
          <span>{{ row.state | bugState }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="bug标题" width="width" align="center">
        <template v-slot="{ row }">
          <div class="title" @click="lookDetails(row)">
            {{ row.title }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="productName" label="所属产品" width="width" align="center" sortable="custom"> </el-table-column>
      <el-table-column prop="majorName" label="所属专业" width="width" align="center"> </el-table-column>
      <el-table-column prop="name" label="实验名称" width="width" align="center"> </el-table-column>
      <el-table-column prop="feedbackUser" label="反馈人" width="width" align="center"> </el-table-column>
      <el-table-column prop="feedbackTime" label="反馈时间" width="width" align="center" sortable="custom"> </el-table-column>
      <el-table-column prop="repairUser" label="解决人" width="width" align="center"> </el-table-column>
      <el-table-column v-if="bug_confirm_close || bug_solve || bug_add_edit_remove" align="center" label="操作" :width="operateWidth">
        <template v-slot="{ row }">
          <el-button v-if="bug_confirm_close" type="primary" :disabled="row.state !== 1" size="small" @click="confirm(row)">确认</el-button>
          <el-button v-if="bug_solve" type="success" :disabled="row.state !== 2" size="small" @click="solve(row)">解决</el-button>
          <el-button v-if="bug_confirm_close" type="info" :disabled="row.state !== 3" size="small" @click="close(row)">关闭</el-button>
          <el-button v-if="bug_add_edit_remove" type="warning" size="small" @click="edit(row)">修改</el-button>
          <el-button v-if="bug_add_edit_remove" type="danger " size="small" @click="del(row)">删除</el-button>
          <el-button v-if="bug_solve" type="info" :disabled="row.state !== 2" size="small" @click="rollback(row)">回退</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :page-sizes="[10, 15, 20, 30]"
      :total="total"
      :page-size.sync="queryInfo.pageSize"
      :current-page.sync="queryInfo.pageNum"
      style="text-align: center; margin-top: 15px"
      @size-change="getList"
      @current-change="getList"
    >
    </el-pagination>
    <!-- bug确认 -->
    <el-dialog title="bug确认" :visible.sync="confirmDialog" width="600px">
      <div>
        <el-form ref="confirmForm" :model="confirmForm" label-width="90px" :rules="confirmFormRules">
          <el-form-item label="解决人:" prop="repairUser">
            <el-select v-model="confirmForm.repairUser" filterable placeholder="请选择解决人" @focus="getUserList">
              <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.realName"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优先级:" prop="priority">
            <el-radio-group v-model="confirmForm.priority">
              <el-radio :label="1">低</el-radio>
              <el-radio :label="2">中</el-radio>
              <el-radio :label="3">高</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input v-model="confirmForm.remark" placeholder="请输入备注" maxlength="300" type="textarea" :autosize="{ minRows: 3, maxRows: 8 }"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="record">
        <div v-for="item in logRecord" :key="item.logId">
          {{ item.createTime }}, 由{{ item.createUserName
          }}{{ item.type === 1 ? '创建' : item.type === 2 ? '确认' : item.type === 3 ? '解决' : item.type === 4 ? '关闭' : item.type === 5 ? '回退' : '' }}
          <span v-if="item.type === 5 && item.remark">回退原因:{{ item.remark }}</span>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="confirmBugClose">取 消</el-button>
        <el-button type="primary" @click="confirmBug">确 定</el-button>
      </div>
    </el-dialog>
    <!-- bug解决 -->
    <el-dialog title="bug解决" :visible.sync="solveDialog" width="1000px">
      <div>
        <el-form ref="solveForm" :model="solveForm" label-width="90px" :rules="solveRules">
          <el-form-item label="解决人:" prop="repairUser">
            <el-select v-model="solveForm.repairUser" filterable placeholder="请选择解决人" disabled @focus="getUserList">
              <el-option v-for="item in userList" :key="item.userId" :label="item.realName" :value="item.realName"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="解决日期:" prop="repairTime">
            <el-date-picker v-model="solveForm.repairTime" type="datetime" placeholder="选择解决日期" value-format="yyyy-MM-dd HH:mm:ss"> </el-date-picker>
          </el-form-item>
          <el-form-item label="解决方案:" prop="repairPlan">
            <vueQuillEditor ref="contentQuill" @change="solveForm.repairPlan = $event.html" />
          </el-form-item>
        </el-form>
      </div>
      <div class="record">
        <div v-for="item in logRecord" :key="item.logId">
          {{ item.createTime }}, 由{{ item.createUserName
          }}{{ item.type === 1 ? '创建' : item.type === 2 ? '确认' : item.type === 3 ? '解决' : item.type === 4 ? '关闭' : item.type === 5 ? '回退' : '' }}
          <span v-if="item.type === 5 && item.remark">回退原因:{{ item.remark }}</span>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="solveDialog = false">取 消</el-button>
        <el-button type="primary" @click="solveBug">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 回退弹窗 -->
    <el-dialog title="回退" :visible.sync="rollbackDialog" width="600px" :close-on-click-modal="false" custom-class="rollback-dialog">
      <el-form ref="rollbackForm" :model="rollbackForm" label-width="90px" :rules="rollbackFormRules">
        <el-form-item label="回退原因:" prop="remark">
          <el-input v-model="rollbackForm.remark" type="textarea" :rows="4" placeholder="请输入回退原因" maxlength="200" show-word-limit class="rollback-input"> </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button round @click="rollbackDialog = false"> 取 消 </el-button>
        <el-button round type="primary" @click="rollbackBug"> 确 定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { tBugList, tBugRemove, tBugBugLogRecord, tBugConfirm, tBugUpdate, tBugDetail, tBugListExport, tBugRollback } from '@/api/bug'
import { allMajor } from '@/api/specialty'
import { getList } from '@/api/systemUser'
import { formatDate } from '@/filters'
import vueQuillEditor from '@/components/vueQuillEditor'
import { mapGetters } from 'vuex'
import { formatJson } from '@/utils'

export default {
  name: 'Bug',
  components: {
    vueQuillEditor
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === 'BugDetails' && to.name === 'Bug') {
      to.meta.keepAlive = true
      next((vm) => {
        vm.getList()
      })
    } else if (from.name === 'addBug' && to.name === 'Bug') {
      to.meta.keepAlive = false
    }
    next()
  },
  data() {
    return {
      queryInfo: {
        title: null, // 标题
        states: [], // 状态 1 待解决 2 解决中 3 已解决 4 已关闭
        feedbackUser: null, // 反馈人
        majorIds: [], // 专业id
        productName: null, // 产品名称
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        orderField: null, // 排序字段 1状态 2产品id 3优先级 4反馈时间
        order: null, // 排序顺序 1正序 2倒序
        name: null, // 实验名称
        pageNum: 1,
        pageSize: 10
      },
      time: null,
      list: [],
      total: 0,
      majorList: [],
      confirmDialog: false,
      confirmForm: {
        bugId: null,
        repairUser: null,
        priority: null,
        remark: null
      },
      confirmFormRules: {
        repairUser: [{ required: true, message: '请选择解决人', trigger: 'change' }],
        priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
      },
      userList: [],
      logRecord: [],
      solveDialog: false,
      solveForm: {},
      solveRules: {
        repairUser: [{ required: true, message: '请选择解决人', trigger: 'change' }],
        repairTime: [{ required: true, message: '请选择反馈时间', trigger: 'change' }],
        repairPlan: [{ required: true, message: '请输入解决方案', trigger: 'blur' }]
      },
      rollbackForm: {
        bugId: null,
        remark: null // 回退原因
      },
      rollbackFormRules: {
        remark: [{ required: true, message: '请输入回退原因', trigger: 'blur' }]
      },
      rollbackDialog: false // 回退弹窗
    }
  },
  computed: {
    ...mapGetters(['keyList', 'organizationId']),
    bug_confirm_close() {
      return this.keyList.includes('bug_confirm_close')
    },
    bug_add_edit_remove() {
      return this.keyList.includes('bug_add_edit_remove')
    },
    bug_solve() {
      return this.keyList.includes('bug_solve')
    },
    operateWidth() {
      let width
      if (this.bug_confirm_close && this.bug_add_edit_remove && this.bug_solve) {
        width = 450
      } else if (this.bug_solve && !this.bug_confirm_close && !this.bug_add_edit_remove) {
        width = 200
      } else {
        width = 250
      }
      return width
    }
  },
  created() {
    this.getList()
    this.getMajorList()
  },
  methods: {
    async getList() {
      const { data } = await tBugList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    sortChange(val) {
      this.queryInfo.orderField = val.prop === 'state' ? 1 : val.prop === 'productName' ? 2 : val.prop === 'priority' ? 3 : 4
      this.queryInfo.order = val.order === 'descending' ? 2 : 1
      this.queryInfo.pageNum = 1
      this.getList()
    },
    timeChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
      this.getList()
    },
    async getMajorList() {
      const { data } = await allMajor()
      this.majorList = data
    },
    reset() {
      this.queryInfo = {
        title: null, // 标题
        states: null, // 状态 1 待解决 2 解决中 3 已解决 4 已关闭
        feedbackUser: null, // 反馈人
        majorIds: null, // 专业id
        productName: null, // 产品名称
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        name: null, // 实验名称
        pageNum: 1,
        pageSize: 10
      }
      this.time = null
      this.selectValue = []
      this.getList()
    },
    add() {
      this.$router.push('/bug/add/0')
    },
    edit(row) {
      this.$router.push(`/bug/add/${row.bugId}`)
    },
    close(row) {
      this.$confirm('确定要关闭该bug吗?', '关闭bug', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const { data: bugDetails } = await tBugDetail({ id: row.bugId })
          await tBugUpdate({ ...bugDetails, state: 4 })
          this.$message({
            type: 'success',
            message: '关闭成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消关闭'
          })
        })
    },
    del(row) {
      this.$confirm('确定要删除该bug吗?', '删除bug', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await tBugRemove({ id: row.bugId })
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async confirm(row) {
      this.confirmForm.bugId = row.bugId
      this.confirmDialog = true
      const { data } = await tBugBugLogRecord({ bugId: row.bugId })
      this.logRecord = data
    },

    async getUserList() {
      const { data } = await getList({ pageNum: 1, pageSize: 2000, organizationId: this.organizationId })
      this.userList = data.list
    },
    confirmBug() {
      this.$refs['confirmForm'].validate(async (val) => {
        if (val) {
          await tBugConfirm(this.confirmForm)
          this.$message.success('bug确认成功')
          this.confirmBugClose()
          this.getList()
        }
      })
    },
    confirmBugClose() {
      this.confirmForm = {
        bugId: null,
        repairUser: null,
        priority: null,
        remark: null
      }
      this.$refs['confirmForm'].resetFields()
      this.confirmDialog = false
    },
    async solve(row) {
      const { data: bugDetails } = await tBugDetail({ id: row.bugId })
      const time = formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss')
      this.solveForm = { ...bugDetails }
      this.solveForm.repairTime = time
      this.$set(this.solveForm, 'repairPlan', null)
      const { data } = await tBugBugLogRecord({ bugId: row.bugId })
      this.logRecord = data
      this.solveDialog = true
      this.$nextTick(() => {
        const quill = document.querySelector('.ql-blank')
        quill.style.minHeight = '400px'
      })
    },
    solveBug() {
      this.$refs['solveForm'].validate((val) => {
        if (val) {
          const loading = this.$loading({
            lock: true,
            text: '操作中,请稍后...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          tBugUpdate({ ...this.solveForm, state: 3 })
            .then(() => {
              this.$message.success('解决成功!')
              this.getList()
              this.solveBugClose()
            })
            .finally(() => {
              loading.close()
            })
        }
      })
    },
    solveBugClose() {
      this.solveForm = {}
      this.$refs['solveForm'].resetFields()
      this.solveDialog = false
    },
    lookDetails(row) {
      this.$router.push(`/bug/details/${row.bugId}`)
    },
    async exportList() {
      const { data } = await tBugListExport(this.queryInfo)
      const headers = {
        bug状态: 'state',
        bug标题: 'title',
        所属产品: 'productName',
        所属专业: 'majorName',
        实验名称: 'name',
        反馈人: 'feedbackUser',
        反馈时间: 'feedbackTime',
        解决人: 'repairUser'
      }
      const res = formatJson(headers, data)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.keys(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: 'bug表' // 非必填
        })
      })
    },
    // 回退
    rollback(row) {
      this.rollbackDialog = true
      this.rollbackForm.remark = null
      this.rollbackForm.bugId = row.bugId
    },
    rollbackBug() {
      this.$refs['rollbackForm'].validate((val) => {
        if (val) {
          this.$confirm('确定要回退吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              tBugRollback({ bugId: this.rollbackForm.bugId, remark: this.rollbackForm.remark })
                .then(() => {
                  this.$message.success('回退成功!')
                  this.getList()
                  this.rollbackDialog = false
                  this.rollbackForm = {
                    bugId: null,
                    remark: null
                  }
                })
                .catch(() => {})
            })
            .catch(() => {})
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .searchForm {
    .el-input {
      .el-input__inner {
        width: 300px;
      }
    }
  }
  .title {
    color: #4783ed;
    cursor: pointer;
  }
  .el-dialog {
    max-height: 750px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 3px;
      background: rgb(167, 167, 167);
    }
    &::-webkit-scrollbar-thumb {
      color: #000;
    }
  }
  .rollback-dialog {
    border-radius: 10px;
    .el-dialog__body {
      padding: 20px 30px;
    }

    .rollback-input {
      .el-textarea__inner {
        padding: 12px 15px;
        border-radius: 4px;
        font-size: 14px;

        &:focus {
          border-color: #409eff;
        }
      }
    }

    .dialog-footer {
      text-align: center;
      padding-top: 10px;
    }
  }
}

.record {
  border-top: 1px solid blue;
  padding-top: 15px;
  & > div {
    font-size: 15px;
    color: #000;
  }
}
</style>
