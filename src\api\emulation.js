// 虚拟仿真
import request from '@/utils/request'

/** 虚拟仿真列表 */
export function emulationList(params) {
  return request({
    url: '/emulation/list',
    method: 'GET',
    params
  })
}
/** 添加虚拟仿真 */
export function emulationAdd(data) {
  return request({
    url: '/emulation/add',
    method: 'POST',
    data
  })
}
/** 修改虚拟仿真 */
export function emulationUpdate(data) {
  return request({
    url: '/emulation/update',
    method: 'POST',
    data
  })
}
/** 删除虚拟仿真 */
export function emulationRemove(params) {
  return request({
    url: '/emulation/remove',
    method: 'DELETE',
    params
  })
}
/** 虚拟仿真详情 */
export function emulationDetail(params) {
  return request({
    url: '/emulation/detail',
    method: 'GET',
    params
  })
}
/** 反馈列表添加 */
export function emulationAddFeedback(data) {
  return request({
    url: '/emulation/addFeedback',
    method: 'POST',
    data
  })
}
/** 反馈列表列表 */
export function emulationFeedbackList(params) {
  return request({
    url: '/emulation/feedbackList',
    method: 'GET',
    params
  })
}
/** 赋分模型添加 */
export function emulationAddModel(data) {
  return request({
    url: '/emulation/addModel',
    method: 'POST',
    data
  })
}
/** 赋分模型修改 */
export function emulationUpdateModel(data) {
  return request({
    url: '/emulation/updateModel',
    method: 'POST',
    data
  })
}
/** 赋分模型列表 */
export function emulationModelList(params) {
  return request({
    url: '/emulation/modelList',
    method: 'GET',
    params
  })
}
/** 根据虚拟仿真id查询全部赋分模型 */
export function emulationGetAllModels(params) {
  return request({
    url: '/emulation/getAllModels',
    method: 'GET',
    params
  })
}
/** 赋分模型删除 */
export function emulationModeRemove(params) {
  return request({
    url: '/emulation/modeRemove',
    method: 'DELETE',
    params
  })
}

/** 查询考试记录 */
export function emulationGetRecord(params) {
  return request({
    url: '/emulation/getRecord',
    method: 'GET',
    params
  })
}

/** 查询所有专业 */
export function allMajor(params) {
  return request({
    url: '/major/allMajor',
    method: 'GET',
    params
  })
}

/** 反馈删除 */
export function emulationFeedbackRemove(params) {
  return request({
    url: '/emulation/feedbackRemove',
    method: 'DELETE',
    params
  })
}

/** 列表导出 */
export function emulationListExport(data) {
  return request({
    url: '/emulation/listExport',
    method: 'POST',
    data
  })
}

/** 虚拟仿真对应的产品发布统计 */
export function emulationGetReleaseProductStatis() {
  return request({
    url: '/emulation/getReleaseProductStatis',
    method: 'GET'
  })
}
/** 转到产品发布测试区 */
export function emulationToReleaseProduct(emulationId) {
  return request({
    url: '/emulation/toReleaseProduct',
    method: 'GET',
    params: {
      emulationId
    }
  })
}

/** 待审核列表 */
export function emulationCheckList(params) {
  return request({
    url: '/emulation/examine/List',
    method: 'GET',
    params
  })
}
/** 审核状态修改 */
export function emulationCheckUpdate(data) {
  return request({
    url: '/emulation/examine/update',
    method: 'post',
    data
  })
}

/** 修改记录列表 */
export function emulationRecordList(params) {
  return request({
    url: '/emulation/record/list',
    method: 'GET',
    params
  })
}

/** 解决 */
export function emulation_solveFeedback(feedbackId) {
  return request({
    url: '/emulation/solveFeedback',
    method: 'GET',
    params: {
      feedbackId
    }
  })
}
