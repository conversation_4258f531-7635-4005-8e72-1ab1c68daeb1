<template>
  <div class="app-container">
    <!-- #region 输入和控制区域 - 2025-09-12 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="16">
        <el-input v-model="url" placeholder="请输入网页地址" clearable></el-input>
      </el-col>
      <el-col :span="8">
        <el-button type="primary" :loading="analysisState === State.Running" @click="startAnalysis">
          {{ analysisState === State.Running ? '解析中...' : '展示' }}
        </el-button>
        <el-button type="success" :disabled="!url" @click="getWebContent">获取内容</el-button>
        <el-button type="warning" @click="openLoginWindow">登录网站</el-button>
        <el-button type="primary" @click="aiAnalysis">AI智能识别</el-button>
      </el-col>
    </el-row>

    <!-- 获取方法说明 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="24">
        <el-alert title="智能内容提取" type="info" show-icon :closable="false">
          <template slot="title">
            <span>🎯 智能内容提取 + 登录状态保持</span>
          </template>
          <div>
            <p><strong>功能说明：</strong></p>
            <ul style="margin: 5px 0; padding-left: 20px">
              <li>自动识别千里马、招标网站等，选择最佳提取规则</li>
              <li><span style="color: #e6a23c">⚠️ 对于需要登录的网站（如千里马）：</span></li>
              <li style="color: #409eff">1️⃣ 先点击"登录网站"按钮，在弹出窗口中完成登录</li>
              <li style="color: #409eff">2️⃣ 登录完成后关闭登录窗口</li>
              <li style="color: #409eff">3️⃣ 点击"展示"可在下方iframe中预览登录状态下的完整页面</li>
              <li style="color: #409eff">4️⃣ 点击"获取内容"可提取登录状态下的完整数据</li>
            </ul>
          </div>
        </el-alert>
      </el-col>
    </el-row>

    <!-- #endregion -->

    <!-- #region 显示区域 - 2025-09-12 -->
    <!-- 网页预览区域 -->
    <div style="margin-bottom: 20px">
      <h3>
        网页预览
        <el-button v-if="isPreviewActive" size="mini" type="danger" @click="closePreview" style="margin-left: 10px"> 关闭预览 </el-button>
      </h3>

      <!-- 预览说明 -->
      <div v-if="!isPreviewActive" class="source empty-preview">
        <div class="empty-text">
          点击"展示"按钮在此区域预览完整的原网页（支持登录状态）<br />
          预览窗口将嵌入在主窗口中，可以正常浏览和操作
        </div>
      </div>

      <!-- 预览活跃时的占位区域 -->
      <div v-else class="source preview-active">
        <div class="preview-info">
          <p><strong>预览地址：</strong>{{ previewUrl }}</p>
          <p><strong>状态：</strong>预览窗口已嵌入到此区域上方</p>
          <p><em>您可以在上方的预览窗口中正常浏览网页，包括登录状态</em></p>
        </div>
      </div>
    </div>

    <!-- 内容分析结果显示 -->
    <div v-if="webContent" style="margin-bottom: 20px">
      <h3>内容分析结果</h3>
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="网页标题">{{ webContent.title || '无' }}</el-descriptions-item>
            <el-descriptions-item label="网页URL">{{ webContent.url || '无' }}</el-descriptions-item>
            <el-descriptions-item label="提取规则">{{ webContent.extractionRule || '默认规则' }}</el-descriptions-item>
            <el-descriptions-item label="匹配选择器">{{ webContent.extractedSelector || '无匹配' }}</el-descriptions-item>
            <el-descriptions-item label="获取时间">{{ webContent.fetchTime || '无' }}</el-descriptions-item>
            <el-descriptions-item label="内容长度">{{ webContent.textContent ? webContent.textContent.length + ' 字符' : '0 字符' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="提取内容" name="specific">
          <div v-if="webContent.specificContent">
            <el-button size="small" @click="copyToClipboard(webContent.specificContent)">复制内容</el-button>
            <div class="content-display" v-html="webContent.specificContent"></div>
          </div>
          <el-empty v-else description="未找到匹配的内容元素"></el-empty>
        </el-tab-pane>
        <el-tab-pane label="纯文本内容" name="textContent">
          <div v-if="webContent.textContent">
            <el-button size="small" @click="copyToClipboard(webContent.textContent)">复制文本</el-button>
            <div class="text-content">{{ webContent.textContent }}</div>
          </div>
          <el-empty v-else description="无文本内容"></el-empty>
        </el-tab-pane>
        <el-tab-pane label="markdown" name="markdown">
          <div v-if="webContent.markDownText">
            <el-button size="small" @click="copyToClipboard(webContent.markDownText)">复制文本</el-button>
            <div class="text-content">{{ webContent.markDownText }}</div>
          </div>
          <el-empty v-else description="无文本内容"></el-empty>
        </el-tab-pane>

        <el-tab-pane label="完整HTML" name="html">
          <el-button size="small" @click="copyToClipboard(webContent.html)">复制HTML</el-button>
          <pre class="html-content">{{ webContent.html }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- #endregion -->
  </div>
</template>
<script>
import TurndownService from 'turndown'
import { callBailianAPI } from '@/utils/aiRequest'
// #region 状态定义和组件配置 - 2025-09-12
const State = {
  NoStart: 'NoStart',
  Running: 'Running',
  End: 'End'
}

export default {
  name: 'BiddingAnalysis',
  data() {
    return {
      url: '',
      analysisState: State.NoStart,
      // 新增数据属性
      webContent: null,
      activeTab: 'basic',
      // #region 预览相关数据 - 2025-09-15
      isPreviewActive: false, // 预览是否激活
      previewUrl: '' // 预览的URL
      // #endregion
    }
  },
  computed: {
    State() {
      return State
    }
  },

  methods: {
    // #region 预览功能 - 在主窗口中嵌入完整的原网页
    async startAnalysis() {
      if (!this.url) {
        this.$message.warning('请输入网页地址')
        return
      }

      this.analysisState = State.Running
      this.$message.info('正在创建预览窗口，请稍候...')

      try {
        // 创建预览BrowserView，嵌入完整的原网页
        const result = await window.webContentAPI.createPreviewBrowserView(this.url)

        if (result.success) {
          this.analysisState = State.End
          this.isPreviewActive = true
          this.previewUrl = this.url

          this.$message.success('预览窗口已创建！您可以在上方看到完整的原网页（包含登录状态）')

          console.log('预览BrowserView创建成功:', result)
        } else {
          this.analysisState = State.NoStart
          this.$message.error('预览窗口创建失败: ' + (result.error || '未知错误'))
        }
      } catch (error) {
        this.analysisState = State.NoStart
        this.$message.error('预览功能出错: ' + error.message)
      }
    },

    // #region 关闭预览功能 - 2025-09-15
    async closePreview() {
      try {
        const result = await window.webContentAPI.destroyPreviewBrowserView()

        if (result.success) {
          this.isPreviewActive = false
          this.previewUrl = ''
          this.$message.success('预览窗口已关闭')
        } else {
          this.$message.error('关闭预览窗口失败: ' + (result.error || '未知错误'))
        }
      } catch (error) {
        this.$message.error('关闭预览功能出错: ' + error.message)
      }
    },
    // #endregion
    // #endregion

    // #region 新增的网页内容获取方法 - 2025-09-12
    async getWebContent() {
      if (!this.url) {
        this.$message.warning('请输入网页地址')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '正在获取网页内容...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        // 使用iframe方式获取内容（最稳定）
        const result = await window.webContentAPI.getWebContentByIframe(this.url)

        loading.close()

        if (result.success) {
          console.log(result.data)

          const turndownService = new TurndownService()
          const markdown = turndownService.turndown(result.data.textContent)
          // 添加获取时间信息
          this.webContent = {
            ...result.data,
            markDownText: markdown,
            fetchTime: new Date().toLocaleString()
          }

          this.$message.success(`网页内容获取成功！使用规则：${result.data.extractionRule || '默认规则'}`)
          this.activeTab = 'basic'
        } else {
          this.$message.error(`获取失败: ${result.error}`)
        }
      } catch (error) {
        loading.close()
        console.error('获取网页内容时发生错误:', error)
        this.$message.error('获取网页内容时发生错误')
      }
    },

    // #region 登录功能 - 2025-09-15
    // 打开登录窗口
    async openLoginWindow() {
      try {
        const result = await window.webContentAPI.openLoginWindow('https://vip.qianlima.com/login/')

        if (result.success) {
          this.$message.success(result.message)
          this.$notify({
            title: '登录提示',
            message: '请在新窗口中完成登录，登录完成后关闭窗口。登录状态将自动保存，之后获取内容时会使用您的登录状态。',
            type: 'info',
            duration: 8000
          })
        } else {
          this.$message.error('打开登录窗口失败: ' + result.error)
        }
      } catch (error) {
        console.error('打开登录窗口时发生错误:', error)
        this.$message.error('打开登录窗口时发生错误')
      }
    },
    // #endregion

    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.$message.success('内容已复制到剪贴板')
          })
          .catch((err) => {
            console.error('复制失败:', err)
            this.fallbackCopyTextToClipboard(text)
          })
      } else {
        this.fallbackCopyTextToClipboard(text)
      }
    },

    // 兼容性复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.top = '0'
      textArea.style.left = '0'
      textArea.style.position = 'fixed'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('内容已复制到剪贴板')
        } else {
          this.$message.error('复制失败')
        }
      } catch (err) {
        console.error('复制失败:', err)
        this.$message.error('复制失败')
      }

      document.body.removeChild(textArea)
    },
    // #endregion

    // #region ai调用分析内容
    async aiAnalysis() {
      const systemPrompt = `你是一个专注于招投标信息提取的助手。请从所给网页文本中识别并提取与采购意向、招标公告或中标结果相关的信息，并严格按照以下JSON结构输出：

json
{
  "queryDate": "当前日期（${new Date().toISOString().split('T')[0]}）",
  "expectedProcurementTime": "预计采购时间",
  "title": "采购项目名称",
  "detailContent": "详细采购内容",
  "province": "采购项目所在省份",
  "procuringEntity": "采购单位名称",
  "amount": "采购金额（数字，单位：万元）",
  "website": ""
}
如遇到文本提示“无法获取页面内容”或“手动输入”，请返回：

json
{
  "queryDate": "当前日期（YYYY-MM-DD）",
  "expectedProcurementTime": "请手动输入预计采购时间",
  "title": "请手动输入采购标题",
  "detailContent": "请手动输入详细采购内容",
  "province": "请手动输入省份",
  "procuringEntity": "请手动输入采购单位",
  "amount": "请手动输入金额（万元）",
  "website": ""
}
提取规则说明：

queryDate：填写当前实际日期，格式为YYYY-MM-DD

expectedProcurementTime（重点优化）：

优先捕捉含以下关键词的字段：

“计划/预计/拟采购时间”

“开标时间/日期”、“投标截止时间”

“招标时间”、“公告时间”

“实施/执行/完成时间”

“采购/招标/投标日期”

兼容多种时间格式，如 YYYY-MM-DD、YYYY年MM月DD日、MM/DD/YYYY 等

若存在多个时间，选取最接近的未来时间

如只有年月，则默认为该月最后一天

如未识别到，填写“未明确”

title：从“项目名称”、“采购项目”、“招标项目”、“工程名称”等字段提取

detailContent：汇总项目描述、采购需求、技术要求等详细信息

province：从地址中提取省份，如“北京市”、“广东省”等

procuringEntity（重点优化）：

搜索关键词：

“采购人/单位/方”

“招标人/单位/方”

“委托方/单位”、“建设单位”

“发包方”、“甲方”、“主办单位”

应为政府机关、事业单位、国企等名称，常见后缀如“局”、“委”、“中心”、“公司”

不提取代理机构、联系人等非采购主体信息

如有多个单位，优先提取最权威的主体

如未识别到，填写“未明确”

amount：提取金额数字并转换为万元单位，保留一位小数（如：100.5）

website：留空，后续由系统填充

如某字段无匹配信息，填写“未明确”

仅返回标准JSON，不包含任何额外文本

确保JSON可被正确解析

对 expectedProcurementTime 和 procuringEntity 进行多重校验

优先提取标记区域内容（如【时间信息】…【/时间信息】）

验证逻辑：

检查时间是否合理，避免使用已过期日期

采购单位须为机构全称，非个人或联系方式

如提取信息存疑，重新分析原文`
      const data = await callBailianAPI(systemPrompt, this.webContent.markDownText)
      console.log(data)
    }
    // #endregion
  }
}
</script>
<style lang="scss">
// #region 原有样式
.source {
  width: 100%;
  height: 600px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
}

// #region 预览样式 - 2025-09-15
.empty-preview {
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-text {
    color: #909399;
    font-size: 14px;
    text-align: center;
    line-height: 1.6;
  }
}

.preview-active {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 2px dashed #409eff;

  .preview-info {
    padding: 20px;
    text-align: center;

    p {
      margin: 10px 0;
      color: #606266;

      strong {
        color: #303133;
      }

      em {
        color: #909399;
        font-size: 13px;
      }
    }
  }
}
// #endregion
// #endregion

// #region 新增样式 - 2025-09-12
.content-display {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #fafafa;

  // 保持HTML格式
  word-wrap: break-word;
  word-break: break-all;
}

.text-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #f9f9f9;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.html-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #f5f5f5;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

// 标签页样式优化
.el-tabs__content {
  padding-top: 20px;
}

// 描述列表样式优化
.el-descriptions {
  margin-bottom: 20px;
}

// 表格样式优化
.el-table {
  margin-top: 10px;
}

// 按钮组样式
.el-button + .el-button {
  margin-left: 10px;
}

// 单选按钮组样式
.el-radio-group {
  .el-radio {
    margin-right: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-display,
  .text-content,
  .html-content {
    font-size: 12px;
    padding: 10px;
  }

  .el-descriptions {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 8px 10px;
        }
      }
    }
  }
}
// #endregion
</style>
