<template>
  <div class="app-container">
    <!-- #region 输入和控制区域 - 2025-09-12 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="16">
        <el-input v-model="url" placeholder="请输入网页地址" clearable></el-input>
      </el-col>
      <el-col :span="8">
        <el-button type="primary" :disabled="!url" @click="loadInWebview">
          {{ webviewLoading ? '加载中...' : '🌐 加载预览' }}
        </el-button>
        <el-button type="success" :disabled="!webviewLoaded" @click="extractContentFromWebview">
          {{ extracting ? '提取中...' : '📄 提取内容' }}
        </el-button>
        <el-button type="info" :disabled="!webviewLoaded" @click="checkLoginStatus"> 🔍 检查登录状态 </el-button>
        <el-button type="warning" @click="refreshWebview" :disabled="!webviewLoaded"> 🔄 刷新 </el-button>
      </el-col>
    </el-row>

    <!-- 获取方法说明 -->
    <el-row :gutter="10" style="margin-bottom: 20px">
      <el-col :span="24">
        <el-alert title="智能内容提取" type="info" show-icon :closable="false">
          <template slot="title">
            <span>🎯 智能内容提取 + 登录状态保持</span>
          </template>
          <div>
            <p><strong>🚀 一体化操作流程：</strong></p>
            <ul style="margin: 5px 0; padding-left: 20px; color: #67c23a">
              <li>✅ 1️⃣ 输入网址，点击"🌐 加载预览"</li>
              <li>✅ 2️⃣ 在下方预览区域中直接浏览和登录网站</li>
              <li>✅ 3️⃣ 登录完成后，点击"📄 提取内容"获取数据</li>
              <li>✅ 4️⃣ 可使用"🔍 检查登录状态"确认登录状态</li>
            </ul>
            <p style="color: #e6a23c; margin: 10px 0">💡 <strong>关键优势</strong>：预览区域与主程序共享登录状态，无需多窗口操作！</p>
          </div>
        </el-alert>
      </el-col>
    </el-row>

    <!-- #endregion -->

    <!-- #region 显示区域 - 2025-09-12 -->
    <!-- 网页预览区域 -->
    <div style="margin-bottom: 20px">
      <h3>
        网页预览
        <span v-if="webviewLoaded" style="margin-left: 10px; color: #67c23a; font-size: 14px">
          ✅ 已加载 |
          <span v-if="loginStatus.isLoggedIn" style="color: #67c23a">🔐 已登录</span>
          <span v-else style="color: #e6a23c">🔓 未登录</span>
        </span>
      </h3>

      <!-- webview预览区域 -->
      <div class="webview-container" style="height: 600px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden">
        <webview v-if="currentUrl" ref="webview" :src="currentUrl" style="width: 100%; height: 100%" partition="persist:main" allowpopups></webview>

        <!-- 空状态提示 -->
        <div v-else class="empty-webview" style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f5f5f5">
          <div style="text-align: center; color: #999">
            <i class="el-icon-monitor" style="font-size: 48px; margin-bottom: 16px"></i>
            <p>请输入网址并点击"🌐 加载预览"开始浏览</p>
            <p style="font-size: 12px">支持登录状态保持，可直接在此区域完成网站登录</p>
          </div>
        </div>

        <!-- 加载状态遮罩 -->
        <div
          v-if="webviewLoading"
          class="loading-overlay"
          style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.8); display: flex; align-items: center; justify-content: center; z-index: 10"
        >
          <div style="text-align: center">
            <i class="el-icon-loading" style="font-size: 24px; color: #409eff"></i>
            <p style="margin-top: 8px; color: #409eff">页面加载中...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容分析结果显示 -->
    <div v-if="webContent" style="margin-bottom: 20px">
      <h3>内容分析结果</h3>
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="网页标题">{{ webContent.title || '无' }}</el-descriptions-item>
            <el-descriptions-item label="网页URL">{{ webContent.url || '无' }}</el-descriptions-item>
            <el-descriptions-item label="提取规则">{{ webContent.extractionRule || '默认规则' }}</el-descriptions-item>
            <el-descriptions-item label="匹配选择器">{{ webContent.extractedSelector || '无匹配' }}</el-descriptions-item>
            <el-descriptions-item label="获取时间">{{ webContent.fetchTime || '无' }}</el-descriptions-item>
            <el-descriptions-item label="内容长度">{{ webContent.textContent ? webContent.textContent.length + ' 字符' : '0 字符' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <el-tab-pane label="提取内容" name="specific">
          <div v-if="webContent.specificContent">
            <el-button size="small" @click="copyToClipboard(webContent.specificContent)">复制内容</el-button>
            <div class="content-display" v-html="webContent.specificContent"></div>
          </div>
          <el-empty v-else description="未找到匹配的内容元素"></el-empty>
        </el-tab-pane>
        <el-tab-pane label="纯文本内容" name="textContent">
          <div v-if="webContent.textContent">
            <el-button size="small" @click="copyToClipboard(webContent.textContent)">复制文本</el-button>
            <div class="text-content">{{ webContent.textContent }}</div>
          </div>
          <el-empty v-else description="无文本内容"></el-empty>
        </el-tab-pane>
        <el-tab-pane label="markdown" name="markdown">
          <div v-if="webContent.markDownText">
            <el-button size="small" @click="copyToClipboard(webContent.markDownText)">复制文本</el-button>
            <div class="text-content">{{ webContent.markDownText }}</div>
          </div>
          <el-empty v-else description="无文本内容"></el-empty>
        </el-tab-pane>

        <el-tab-pane label="完整HTML" name="html">
          <el-button size="small" @click="copyToClipboard(webContent.html)">复制HTML</el-button>
          <pre class="html-content">{{ webContent.html }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- #endregion -->
  </div>
</template>
<script>
import TurndownService from 'turndown'
import { callBailianAPI } from '@/utils/aiRequest'
import websiteUtils from './websiteUtils.js'
export default {
  name: 'BiddingAnalysis',
  data() {
    return {
      url: '',
      // 内容相关
      webContent: null,
      activeTab: 'basic',
      // webview相关状态
      currentUrl: '',
      webviewLoading: false,
      webviewLoaded: false,
      extracting: false,
      loginStatus: {
        isLoggedIn: false,
        hasLoginCookie: false,
        hasLoginElement: false,
        needLogin: false
      }
    }
  },
  computed: {},

  mounted() {
    // 确保webview标签可用
    console.log('组件已挂载，webview支持:', !!window.webview)
    // 初始化事件处理器存储
    this.webviewEventHandlers = null
  },

  beforeDestroy() {
    // 清理webview事件监听器
    this.removeWebviewEvents()
    console.log('组件销毁，已清理webview事件监听器')
  },

  methods: {
    // #region webview一体化预览功能 - 2025-09-15
    // 在webview中加载网页
    loadInWebview() {
      if (!this.url) {
        this.$message.warning('请输入网页地址')
        return
      }

      // 如果URL没有变化且webview已加载，直接刷新
      if (this.currentUrl === this.url && this.webviewLoaded) {
        this.refreshWebview()
        return
      }

      // 清理之前的事件监听器
      this.removeWebviewEvents()

      this.currentUrl = this.url
      this.webviewLoading = true
      this.webviewLoaded = false

      // 重置登录状态
      this.loginStatus = {
        isLoggedIn: false,
        hasLoginCookie: false,
        hasLoginElement: false,
        needLogin: false
      }

      this.$message.info('正在加载网页...')

      // 等待webview创建后绑定事件
      this.$nextTick(() => {
        setTimeout(() => {
          this.bindWebviewEvents()
        }, 100) // 稍微延迟确保webview完全创建
      })
    },

    // 绑定webview事件
    bindWebviewEvents() {
      const webview = this.$refs.webview
      if (!webview) {
        console.log('webview引用不存在')
        return
      }

      console.log('绑定webview事件')

      // 清除之前的事件监听器（如果存在）
      if (this.webviewEventHandlers) {
        this.removeWebviewEvents()
      }

      // 创建事件处理器
      this.webviewEventHandlers = {
        domReady: () => {
          console.log('webview DOM ready')
          this.webviewLoading = false
          this.webviewLoaded = true
          this.$message.success('网页加载完成！可以开始浏览和登录')

          // 判断是否是千里马网站
          if (websiteUtils.getSiteExtractionRule(this.url).name === 'qianlima') {
            // 延迟检查登录状态
            setTimeout(() => {
              this.checkLoginStatus()
            }, 1500)
          }
        },

        startLoading: () => {
          console.log('webview start loading')
          this.webviewLoading = true
        },

        stopLoading: () => {
          console.log('webview stop loading')
          this.webviewLoading = false
        },

        failLoad: (event) => {
          console.error('webview load failed:', event)
          this.webviewLoading = false
          this.webviewLoaded = false
          this.$message.error('网页加载失败，请检查网址是否正确')
        },

        titleUpdated: (event) => {
          console.log('页面标题:', event.title)
        }
      }

      // 绑定事件
      webview.addEventListener('dom-ready', this.webviewEventHandlers.domReady)
      webview.addEventListener('did-start-loading', this.webviewEventHandlers.startLoading)
      webview.addEventListener('did-stop-loading', this.webviewEventHandlers.stopLoading)
      webview.addEventListener('did-fail-load', this.webviewEventHandlers.failLoad)
      webview.addEventListener('page-title-updated', this.webviewEventHandlers.titleUpdated)
      // webview.openDevTools()
    },

    // 移除webview事件监听器
    removeWebviewEvents() {
      const webview = this.$refs.webview
      if (!webview || !this.webviewEventHandlers) return

      console.log('移除webview事件监听器')

      webview.removeEventListener('dom-ready', this.webviewEventHandlers.domReady)
      webview.removeEventListener('did-start-loading', this.webviewEventHandlers.startLoading)
      webview.removeEventListener('did-stop-loading', this.webviewEventHandlers.stopLoading)
      webview.removeEventListener('did-fail-load', this.webviewEventHandlers.failLoad)
      webview.removeEventListener('page-title-updated', this.webviewEventHandlers.titleUpdated)

      this.webviewEventHandlers = null
    },

    // 刷新webview
    refreshWebview() {
      if (!this.$refs.webview) {
        this.$message.warning('webview未准备就绪')
        return
      }

      this.webviewLoading = true
      this.webviewLoaded = false

      // 重置登录状态
      this.loginStatus = {
        isLoggedIn: false,
        hasLoginCookie: false,
        hasLoginElement: false,
        needLogin: false
      }

      try {
        this.$refs.webview.reload()
        this.$message.info('正在刷新页面...')
      } catch (error) {
        console.error('刷新webview失败:', error)
        this.webviewLoading = false
        this.$message.error('刷新失败，请重新加载')
      }
    },

    // 检查登录状态
    async checkLoginStatus() {
      if (!this.$refs.webview || !this.webviewLoaded) {
        this.$message.warning('请先加载网页')
        return
      }

      try {
        // 强制刷新页面状态，避免缓存问题
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            // 清除可能的缓存，重新获取最新状态
            const currentCookies = document.cookie;
            const currentUrl = window.location.href;
            console.log('当前URL:', currentUrl);
            console.log('当前Cookies:', currentCookies);

            // 判断是否为千里马网站
            const isQianlimaWebsite = currentUrl.includes('qianlima.com') || currentUrl.includes('qlm.com');

            // 检查是否有有效的登录Cookie
            const cookiePairs = currentCookies.split(';').map(c => c.trim());
            let hasValidLoginCookie = false;
            let loginMethod = '';
            let userInfo = {};

            for (const pair of cookiePairs) {
              const [key, value] = pair.split('=');
              if (key && value) {
                if (isQianlimaWebsite) {
                  // 千里马网站特殊检测逻辑
                  if (key === 'qlm_username' && value && value !== '' && value !== 'null' && value !== 'undefined') {
                    hasValidLoginCookie = true;
                    loginMethod = '千里马用户名Cookie';
                    userInfo.username = decodeURIComponent(value);
                  } else if (key === 'xAuthToken' && value && value !== '' && value !== 'null' && value !== 'undefined') {
                    hasValidLoginCookie = true;
                    loginMethod = '千里马认证Token';
                    userInfo.token = value;
                  } else if (key === 'userInfo' && value && value !== '' && value !== 'null' && value !== 'undefined') {
                    try {
                      const decodedUserInfo = JSON.parse(decodeURIComponent(value));
                      if (decodedUserInfo.username) {
                        hasValidLoginCookie = true;
                        loginMethod = '千里马用户信息';
                        userInfo = decodedUserInfo;
                      }
                    } catch (e) {
                      console.log('解析userInfo失败:', e);
                    }
                  }
                } else {
                  // 通用网站登录检测
                  if ((key === 'login' && value === 'true') ||
                      (key === 'session' && value && value !== '' && value !== 'null' && value !== 'undefined') ||
                      (key === 'token' && value && value !== '' && value !== 'null' && value !== 'undefined') ||
                      (key === 'auth' && value && value !== '' && value !== 'null' && value !== 'undefined') ||
                      (key.includes('user') && value && value !== '' && value !== 'null' && value !== 'undefined')) {
                    hasValidLoginCookie = true;
                    loginMethod = '通用登录Cookie: ' + key;
                  }
                }
              }
            }

            // 检测登录相关的DOM元素
            const logoutElements = document.querySelectorAll('.logout, [class*="logout"], a[href*="logout"], button[onclick*="logout"]');
            const userElements = document.querySelectorAll('.user-info, .username, .user-name, .user-avatar, [class*="user-info"], [class*="username"]');
            const hasLoginElement = logoutElements.length > 0 || userElements.length > 0;

            // 检测登录表单（表示需要登录）
            const loginForms = document.querySelectorAll('form[action*="login"], .login-form, input[type="password"], [class*="login-form"]');
            const loginButtons = document.querySelectorAll('button[onclick*="login"], a[href*="login"], .login-btn, [class*="login-btn"]');
            const hasLoginForm = loginForms.length > 0 || loginButtons.length > 0;

            // 检测页面文本内容
            const bodyText = document.body.textContent || '';
            const hasLogoutText = bodyText.includes('退出') || bodyText.includes('登出') || bodyText.includes('logout') || bodyText.includes('Logout');
            const hasLoginText = bodyText.includes('登录') || bodyText.includes('login') || bodyText.includes('Login');

            // 检查URL是否包含登录页面标识
            const isLoginPage = currentUrl.includes('login') || currentUrl.includes('signin') || currentUrl.includes('auth');

            // 综合判断登录状态
            let isLoggedIn = false;
            let needLogin = false;

            if (isQianlimaWebsite) {
              // 千里马网站：主要依赖Cookie检测
              isLoggedIn = hasValidLoginCookie;
              needLogin = !isLoggedIn && (hasLoginForm || isLoginPage);
            } else {
              // 其他网站：综合判断，大多数网站无需登录
              isLoggedIn = hasValidLoginCookie || (hasLoginElement && !isLoginPage) || (hasLogoutText && !isLoginPage);
              needLogin = (hasLoginForm || hasLoginText || isLoginPage) && !isLoggedIn;
            }

            return {
              isQianlimaWebsite: isQianlimaWebsite,
              hasLoginCookie: hasValidLoginCookie,
              loginMethod: loginMethod,
              userInfo: userInfo,
              hasLoginElement: hasLoginElement,
              hasLoginForm: hasLoginForm,
              hasLogoutText: hasLogoutText,
              hasLoginText: hasLoginText,
              isLoginPage: isLoginPage,
              isLoggedIn: isLoggedIn,
              needLogin: needLogin,
              currentUrl: currentUrl,
              cookies: currentCookies,
              title: document.title,
              timestamp: new Date().getTime(),
              // 调试信息
              debug: {
                cookiePairs: cookiePairs.length,
                logoutElements: logoutElements.length,
                userElements: userElements.length,
                loginForms: loginForms.length,
                loginButtons: loginButtons.length
              }
            };
          })()
        `)

        // 更新登录状态
        this.loginStatus = result
        console.log('结果:', result)

        // 显示检测结果
        if (result.isLoggedIn) {
          let message = '✅ 检测到已登录状态'
          if (result.isQianlimaWebsite) {
            message += ` (千里马网站)`
            if (result.userInfo.username) {
              message += ` - 用户: ${result.userInfo.username}`
            }
          }
          this.$message.success(message)

          console.log('登录检测详情:', {
            网站类型: result.isQianlimaWebsite ? '千里马网站' : '通用网站',
            检测方法: result.loginMethod,
            用户信息: result.userInfo,
            Cookie检测: result.hasLoginCookie,
            元素检测: result.hasLoginElement,
            文本检测: result.hasLogoutText
          })
        } else if (result.needLogin) {
          let message = '⚠️ 检测到需要登录'
          if (result.isQianlimaWebsite) {
            message += ' (千里马网站需要登录才能获取完整内容)'
          }
          message += '，请在预览区域中完成登录'
          this.$message.warning(message)
        } else {
          let message = 'ℹ️ '
          if (result.isQianlimaWebsite) {
            message += '千里马网站未检测到登录状态'
          } else {
            message += '该网站无需登录或登录状态未知'
          }
          this.$message.info(message)
        }

        console.log('登录状态检查结果:', result)
      } catch (error) {
        console.error('检查登录状态失败:', error)
        this.$message.error('检查登录状态失败: ' + error.message)
      }
    },

    // 从webview提取内容
    async extractContentFromWebview() {
      if (!this.$refs.webview) {
        this.$message.warning('请先加载网页')
        return
      }

      this.extracting = true
      const rule = websiteUtils.getSiteExtractionRule(this.url)
      try {
        const result = await this.$refs.webview.executeJavaScript(`
          (function() {
            const currentUrl = window.location.href;
            const isQianlimaWebsite = ${rule.name === 'qianlima'};
            // 检查登录状态（用于千里马网站）
            let isLoggedIn = false;
            if (isQianlimaWebsite) {
              const cookies = document.cookie;
              isLoggedIn = cookies.includes('qlm_username=') &&
                          cookies.includes('xAuthToken=') &&
                          !cookies.includes('qlm_username=;');
            }

            // 根据网站类型选择不同的提取策略
            let selectors = '${rule.selector}'.split(',').map((s) => s.trim())
            let websiteType = '${rule.description}';
            let content = null;
            let matchedSelector = '无匹配';
            // 尝试匹配选择器
            for (const selector of selectors) {
              const element = document.querySelector(selector);
              if (element && element.innerHTML.trim()) {
                content = element.innerHTML;
                matchedSelector = selector;
                console.log('成功匹配选择器:', selector);
                break;
              }
            }

            // 如果没有匹配到特定内容，使用body内容
            if (!content) {
              content = document.body.innerHTML;
              matchedSelector = 'body (fallback)';
            }

            return {
              title: document.title,
              url: currentUrl,
              html: content,
              specificContent: content,
              textContent: content.replace(/<[^>]*>/g, '').replace(/\\s+/g, ' ').trim(),
              extractedSelector: matchedSelector,
              websiteType: websiteType,
              isQianlimaWebsite: isQianlimaWebsite,
              isLoggedIn: isLoggedIn,
              extractionTime: new Date().toLocaleString(),
              contentLength: content.length,
              // 提取质量评估
              extractionQuality: matchedSelector.includes('fallback') ? '低' : '高'
            };
          })()
        `)

        // 处理提取的内容
        const turndownService = new TurndownService()
        const markdown = turndownService.turndown(result.textContent || result.html || '')

        this.webContent = {
          ...result,
          markDownText: markdown,
          extractionRule: `${result.websiteType}智能提取`,
          fetchTime: new Date().toLocaleString()
        }

        this.activeTab = 'basic'
        this.extracting = false

        // 显示提取结果
        let message = `✅ 内容提取成功！`
        if (result.isQianlimaWebsite) {
          message += ` (${result.websiteType})`
          if (result.isLoggedIn) {
            message += ` - 已登录状态，获取完整内容`
          } else {
            message += ` - 未登录状态，内容可能不完整`
          }
        } else {
          message += ` (${result.websiteType})`
        }

        this.$message.success(message)

        // 显示详细通知
        if (result.isQianlimaWebsite) {
          if (result.isLoggedIn) {
            this.$notify({
              title: '千里马网站 - 登录状态',
              message: '检测到您已登录，提取的内容包含登录后的完整信息',
              type: 'success',
              duration: 5000
            })
          } else {
            this.$notify({
              title: '千里马网站 - 未登录',
              message: '未检测到登录状态，建议先登录以获取完整内容',
              type: 'warning',
              duration: 5000
            })
          }
        }

        console.log('内容提取成功:', {
          网站类型: result.websiteType,
          是否千里马: result.isQianlimaWebsite,
          登录状态: result.isLoggedIn,
          提取选择器: result.extractedSelector,
          内容长度: result.contentLength,
          提取质量: result.extractionQuality
        })
      } catch (error) {
        this.extracting = false
        console.error('内容提取失败:', error)
        this.$message.error('内容提取失败: ' + error.message)
      }
    },
    // #endregion

    // #region 兼容旧版本方法
    async startAnalysis() {
      // 兼容旧版本，现在直接调用webview加载
      this.loadInWebview()
    },

    async getWebContent() {
      // 兼容旧版本，现在直接调用webview提取
      this.extractContentFromWebview()
    },

    async openLoginWindow() {
      this.$message.info('现在可以直接在下方预览区域中登录，无需打开新窗口')
    },
    // #endregion

    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            this.$message.success('内容已复制到剪贴板')
          })
          .catch((err) => {
            console.error('复制失败:', err)
            this.fallbackCopyTextToClipboard(text)
          })
      } else {
        this.fallbackCopyTextToClipboard(text)
      }
    },

    // 兼容性复制方法
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.top = '0'
      textArea.style.left = '0'
      textArea.style.position = 'fixed'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success('内容已复制到剪贴板')
        } else {
          this.$message.error('复制失败')
        }
      } catch (err) {
        console.error('复制失败:', err)
        this.$message.error('复制失败')
      }

      document.body.removeChild(textArea)
    },
    // #endregion

    // #region ai调用分析内容
    async aiAnalysis() {
      const systemPrompt = `你是一个专注于招投标信息提取的助手。请从所给网页文本中识别并提取与采购意向、招标公告或中标结果相关的信息，并严格按照以下JSON结构输出：

json
{
  "queryDate": "当前日期（${new Date().toISOString().split('T')[0]}）",
  "expectedProcurementTime": "预计采购时间",
  "title": "采购项目名称",
  "detailContent": "详细采购内容",
  "province": "采购项目所在省份",
  "procuringEntity": "采购单位名称",
  "amount": "采购金额（数字，单位：万元）",
  "website": ""
}
如遇到文本提示“无法获取页面内容”或“手动输入”，请返回：

json
{
  "queryDate": "当前日期（YYYY-MM-DD）",
  "expectedProcurementTime": "请手动输入预计采购时间",
  "title": "请手动输入采购标题",
  "detailContent": "请手动输入详细采购内容",
  "province": "请手动输入省份",
  "procuringEntity": "请手动输入采购单位",
  "amount": "请手动输入金额（万元）",
  "website": ""
}
提取规则说明：

queryDate：填写当前实际日期，格式为YYYY-MM-DD

expectedProcurementTime（重点优化）：

优先捕捉含以下关键词的字段：

“计划/预计/拟采购时间”

“开标时间/日期”、“投标截止时间”

“招标时间”、“公告时间”

“实施/执行/完成时间”

“采购/招标/投标日期”

兼容多种时间格式，如 YYYY-MM-DD、YYYY年MM月DD日、MM/DD/YYYY 等

若存在多个时间，选取最接近的未来时间

如只有年月，则默认为该月最后一天

如未识别到，填写“未明确”

title：从“项目名称”、“采购项目”、“招标项目”、“工程名称”等字段提取

detailContent：汇总项目描述、采购需求、技术要求等详细信息

province：从地址中提取省份，如“北京市”、“广东省”等

procuringEntity（重点优化）：

搜索关键词：

“采购人/单位/方”

“招标人/单位/方”

“委托方/单位”、“建设单位”

“发包方”、“甲方”、“主办单位”

应为政府机关、事业单位、国企等名称，常见后缀如“局”、“委”、“中心”、“公司”

不提取代理机构、联系人等非采购主体信息

如有多个单位，优先提取最权威的主体

如未识别到，填写“未明确”

amount：提取金额数字并转换为万元单位，保留一位小数（如：100.5）

website：留空，后续由系统填充

如某字段无匹配信息，填写“未明确”

仅返回标准JSON，不包含任何额外文本

确保JSON可被正确解析

对 expectedProcurementTime 和 procuringEntity 进行多重校验

优先提取标记区域内容（如【时间信息】…【/时间信息】）

验证逻辑：

检查时间是否合理，避免使用已过期日期

采购单位须为机构全称，非个人或联系方式

如提取信息存疑，重新分析原文`
      const data = await callBailianAPI(systemPrompt, this.webContent.markDownText)
      console.log(data)
    }
    // #endregion
  }
}
</script>
<style lang="scss">
// #region 原有样式
.source {
  width: 100%;
  height: 600px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
}

// #region 预览样式 - 2025-09-15
.empty-preview {
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-text {
    color: #909399;
    font-size: 14px;
    text-align: center;
    line-height: 1.6;
  }
}

.preview-active {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 2px dashed #409eff;

  .preview-info {
    padding: 20px;
    text-align: center;

    p {
      margin: 10px 0;
      color: #606266;

      strong {
        color: #303133;
      }

      em {
        color: #909399;
        font-size: 13px;
      }
    }
  }
}
// #endregion
// #endregion

// #region 新增样式 - 2025-09-12
.content-display {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #fafafa;

  // 保持HTML格式
  word-wrap: break-word;
  word-break: break-all;
}

.text-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #f9f9f9;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.html-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-top: 10px;
  background-color: #f5f5f5;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

// 标签页样式优化
.el-tabs__content {
  padding-top: 20px;
}

// 描述列表样式优化
.el-descriptions {
  margin-bottom: 20px;
}

// 表格样式优化
.el-table {
  margin-top: 10px;
}

// 按钮组样式
.el-button + .el-button {
  margin-left: 10px;
}

// 单选按钮组样式
.el-radio-group {
  .el-radio {
    margin-right: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-display,
  .text-content,
  .html-content {
    font-size: 12px;
    padding: 10px;
  }

  .el-descriptions {
    :deep(.el-descriptions__body) {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 8px 10px;
        }
      }
    }
  }
}
// #endregion
</style>
