<template>
  <div class="app-container">
    <!-- 顶部 -->
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/meeting/meeting_icon.png" alt="" />
            会议管理
          </span>
          <el-button icon="el-icon-circle-plus-outline" type="primary" size="small" @click="$router.push('/addMeeting/0')">预约会议 </el-button>
        </div>
      </el-col>
      <el-col :span="14">
        <el-radio-group v-model="queryInfo.type" @change="getMeetingList">
          <el-radio-button :label="1">
            <span>我接收的</span>
          </el-radio-button>
          <el-radio-button :label="2">
            <span>我预定的</span>
          </el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <!-- 背景图 搜索部分 -->
    <el-row :gutter="10">
      <el-col :span="24">
        <div class="banner">
          <el-input v-model="queryInfo.meetingName" size="small" placeholder="请输入会议室主题" maxlength="50">
            <template v-slot:suffix>
              <i class="el-icon-search" @click="getMeetingList"></i>
            </template>
          </el-input>
          <el-input v-model="queryInfo.realName" size="small" placeholder="请输入预订人名称" maxlength="50">
            <template v-slot:suffix>
              <i class="el-icon-search" @click="getMeetingList"></i>
            </template>
          </el-input>
          <div class="banner_mask">
            <el-form ref="form" label-width="90px" inline>
              <el-form-item label="会议室:">
                <el-select v-model="queryInfo.meetingRoomName" placeholder="请选择会议室" size="small" clearable @change="getMeetingList" @clear="getMeetingList">
                  <el-option label="第一会议室" value="第一会议室"> </el-option>
                  <el-option label="第二会议室" value="第二会议室"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="会议状态:">
                <el-select v-model="queryInfo.meetingStatus" placeholder="请选择会议状态" size="small" clearable @change="getMeetingList" @clear="getMeetingList">
                  <el-option label="未开始" :value="1"> </el-option>
                  <el-option label="进行中" :value="2"> </el-option>
                  <el-option label="已结束" :value="3"> </el-option>
                  <el-option label="已归档" :value="4"> </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="会议时间:" class="meetingTime">
                <el-date-picker v-model="queryInfo.startTime" type="date" size="small" prefix-icon="" placeholder="选择开始时间" @change="getMeetingList"> </el-date-picker>
                至
                <el-date-picker v-model="queryInfo.endTime" type="date" size="small" prefix-icon="" placeholder="选择结束时间" @change="getMeetingList"> </el-date-picker>
              </el-form-item>
              <!-- <el-form-item>
                <el-button type="success" size="small" @click="getMeetingList"> 查询 </el-button>
                <el-button type="primary" size="small" @click="reset">重置 </el-button>
              </el-form-item> -->
            </el-form>
          </div>
        </div>
      </el-col>
    </el-row>
    <div style="background: #f5f5f5; border-radius: 0 0 4px 4px">
      <el-row class="table-box">
        <el-col v-for="item in list" :key="item.meetingId" :span="6" class="table">
          <div class="table_list">
            <img src="@/assets/meeting/table_top.png" alt="" />
            <div class="table_content">
              <el-row :gutter="10" type="flex" justify="space-between" align="middle">
                <el-col :span="6" class="meetingStatus">
                  <img v-if="item.meetingStatus === 1" src="@/assets/meeting/meetingStatus1.png" alt="" />
                  <img v-else-if="item.meetingStatus === 2" src="@/assets/meeting/meetingStatus2.png" alt="" />
                  <img v-else src="@/assets/meeting/meetingStatus3.png" alt="" />
                </el-col>
                <el-col :span="5" class="meetingCode">
                  <span>{{ item.meetingCode }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="10" class="meetingName">
                <el-col :span="24">
                  {{ item.meetingName }}
                </el-col>
              </el-row>
              <el-row :gutter="10" class="cut-off">
                <el-col :span="24">
                  <span class="first"></span>
                  <span class="second"></span>
                </el-col>
              </el-row>
              <el-row :gutter="10" class="time">
                <el-col :span="24">
                  <i class="el-icon-time"></i>
                  <span>{{ item.startTime }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="10" class="meetingRoomName" type="flex" align="middle" justify="space-between">
                <el-col :span="24">
                  <span>地点：{{ item.meetingRoomName }}</span>
                </el-col>
                <el-col v-if="queryInfo.type === 2" class="operate" :span="7">
                  <img v-if="item.meetingStatus === 1" src="@/assets/meeting/edit.png" alt="" @click="edit(item)" />
                  <img v-if="item.meetingStatus === 1" src="@/assets/meeting/del.png" alt="" @click="del(item)" />
                </el-col>
              </el-row>
              <el-row :gutter="10" class="realName" type="flex" justify="space-between" align="middle">
                <el-col :span="10">
                  <span>预定人：{{ item.realName }}</span>
                </el-col>
                <!-- 未开始的样式 -->
                <el-col v-if="item.meetingStatus === 1" :span="item.isDefine === 1 ? 7 : 14">
                  <template>
                    <el-button v-if="item.meetingStatus === 1 && item.isDefine === 0" type="primary" size="small" @click="comfirmJoin(item)">确认参加</el-button>
                    <el-button v-if="item.meetingStatus === 1 && item.isDefine === 0" type="danger" size="small" @click="refuse(item)">拒绝参加</el-button>
                    <el-tag v-if="item.meetingStatus === 1 && item.isDefine === 1" class="isDefine" color="#d8dbe1" effect="dark" type="info">已确认</el-tag>
                    <el-tag v-if="item.meetingStatus === 1 && item.isDefine === 3" class="isDefine" color="#d8dbe1" effect="dark" type="info">已拒绝</el-tag>
                  </template>
                </el-col>
                <!-- 进行中的样式 -->
                <el-col v-if="item.meetingStatus === 2" :span="7" class="meetingStatus2">
                  <el-button v-if="item.meetingStatus === 2 && item.isSign === 0" class="sign" type="primary" size="small" @click="sign(item)">签到</el-button>
                  <el-tag v-if="item.meetingStatus === 2 && item.isSign === 1" class="isDefine" color="#d8dbe1" effect="dark" type="info">已签到</el-tag>
                </el-col>
                <!-- 已结束的样式 -->
                <el-col v-if="item.meetingStatus === 4 || item.meetingStatus === 3" :span="item.meetingStatus === 3 && item.isSummary === 0 ? 10 : 7" class="meetingStatus3">
                  <template v-if="queryInfo.type === 1">
                    <el-button v-if="item.meetingStatus === 4 || item.meetingStatus === 3" type="primary" size="small" @click="check(item)">查看</el-button>
                  </template>
                  <template v-if="queryInfo.type === 2">
                    <el-button v-if="item.meetingStatus === 4" type="primary" size="small" @click="check(item)">查看</el-button>
                    <el-button v-if="item.meetingStatus === 3 && item.isSummary === 0" class="summary" type="text" size="small" @click="meeting_summary(item)">
                      <i class="el-icon-edit"></i>
                      补充会议纪要</el-button
                    >
                    <el-button v-if="item.meetingStatus === 3 && item.isSummary === 1" class="pigeonhole" type="warning" size="small" @click="archive(item)">归档</el-button>
                  </template>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-col>
        <el-col v-if="list.length >= 1" :span="24">
          <el-pagination background style="text-align: right; margin-top: 15px; margin-bottom: 75px; margin-right: 30px" layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 15, 20, 30]" :total="total" :page-size.sync="queryInfo.pageSize" :current-page.sync="queryInfo.pageNum" @size-change="getMeetingList" @current-change="getMeetingList" />
        </el-col>
      </el-row>
      <el-empty v-if="list.length <= 0" :image="noneImg">
        <template v-slot:description>
          <img src="@/assets/meeting/noMeeting.png" alt="" />
        </template>
        <el-button type="primary" size="small" @click="$router.push('/addMeeting/0')">去添加</el-button>
      </el-empty>
    </div>

    <!--
    <el-card>
      <div>
        <el-table :data="list" style="width: 100%" border>
          <el-table-column prop="meetingCode" label="会议编号" width="width" align="center"> </el-table-column>
          <el-table-column prop="meetingRoomName" label="会议室名称" width="width" align="center"> </el-table-column>
          <el-table-column prop="meetingName" label="会议主题" width="width" align="center"> </el-table-column>
          <el-table-column prop="meetingStatus" label="会议状态" width="width" align="center">
            <template v-slot="{ row }">
              <span>{{ row.meetingStatus | meetingStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="realName" label="预订人" width="width" align="center"> </el-table-column>
          <el-table-column prop="startTime" label="预定开始时间" width="width" align="center"> </el-table-column>
          <el-table-column prop="endTime" label="预定结束时间" width="width" align="center"> </el-table-column>
          <el-table-column label="操作" width="width" align="center">
            <template v-slot="{ row }">
              <template v-if="queryInfo.type === 1">
                <el-button v-if="row.meetingStatus === 1 && row.isDefine === 0" type="primary" size="small" @click="comfirmJoin(row)">确认参加</el-button>
                <el-button v-if="row.meetingStatus === 1 && row.isDefine === 0" type="danger" size="small" @click="refuse(row)">拒绝参加</el-button>
                <el-tag v-if="row.meetingStatus === 1 && row.isDefine === 1">已确认</el-tag>
                <el-button v-if="row.meetingStatus === 2 && row.isSign === 0" type="primary" size="small" @click="sign(row)">签到</el-button>
                <el-tag v-if="row.meetingStatus === 2 && row.isSign === 1">已签到</el-tag>
                <el-button v-if="row.meetingStatus === 4 || row.meetingStatus === 3" type="primary" size="small" @click="check(row)">查看</el-button>
              </template>
              <template v-if="queryInfo.type === 2">
                <el-button v-if="row.meetingStatus === 1" type="warning" size="small" @click="edit(row)">编辑</el-button>
                <el-button v-if="row.meetingStatus === 1" type="danger" size="small" @click="del(row)">删除</el-button>
                <el-button v-if="row.meetingStatus === 2" type="primary" size="small" @click="overMeeting(row)">结束会议</el-button>
                <el-button v-if="row.meetingStatus === 3 && row.isSummary === 0" type="warning" size="small" @click="meeting_summary(row)">补充会议纪要</el-button>
                <el-button v-if="row.meetingStatus === 3 && row.isSummary === 1" type="warning" size="small" @click="archive(row)">归档</el-button>
                <el-button v-if="row.meetingStatus === 4" type="primary" size="small" @click="check(row)">查看</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card> -->
    <el-dialog title="补充会议纪要" :visible.sync="replenishSummaryDialog" width="450px">
      <div style="width: 360px; margin: 0 auto">
        <el-upload class="upload-demo" drag action="http://************:8701/system/upload/file" :headers="header" :file-list="fileList" multiple :before-upload="beforeUpload" :on-success="uploadSuccess" :on-remove="uploadRemove">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip格式的附件，且大小不超过50MB</div>
        </el-upload>
      </div>
      <div slot="footer">
        <el-button @click="replenishSummaryDialog = false">取 消</el-button>
        <el-button type="primary" @click="replenishSummary">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="会议查看" :visible.sync="meetingCheckDialog" width="800px">
      <div>
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="会议信息" name="first">
            <el-descriptions v-if="JSON.stringify(meetingDetails) !== `{}`" :column="3" border style="margin: 0 auto">
              <el-descriptions-item label="预定开始时间">{{ meetingDetails.startTime }} </el-descriptions-item>
              <el-descriptions-item label="预定结束时间">{{ meetingDetails.endTime }} </el-descriptions-item>
              <el-descriptions-item label="会议室">{{ meetingDetails.meetingRoomName }}</el-descriptions-item>
              <el-descriptions-item label="会议主题">{{ meetingDetails.meetingName }}</el-descriptions-item>
              <el-descriptions-item label="会议状态">
                {{ meetingDetails.meetingStatus | meetingStatus }}
              </el-descriptions-item>
              <el-descriptions-item label="预定人">{{ meetingDetails.realName }}</el-descriptions-item>
              <el-descriptions-item label="会议内容">{{ meetingDetails.remark }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="参会人员" name="second">
            <el-table :data="meetingDetails.userDtos" style="width: 100%" border>
              <el-table-column align="center" label="序号" type="index" width="width"> </el-table-column>
              <el-table-column align="center" prop="realName" label="姓名" width="width"> </el-table-column>
              <el-table-column align="center" prop="organizationName" label="部门" width="width"> </el-table-column>
              <el-table-column align="center" prop="jobName" label="岗位" width="width"> </el-table-column>
              <el-table-column align="center" prop="signTime" label="签到时间" width="width"> </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="相关附件" name="third">
            <el-table :data="meetingDetails.fileDtos" style="width: 100%" border>
              <el-table-column align="center" label="序号" type="index" width="width"> </el-table-column>

              <el-table-column align="center" prop="fileName" label="文件名称" width="width"> </el-table-column>
              <el-table-column align="center" prop="fileSize" label="文件大小" width="width">
                <template v-slot="{ row }">
                  <span>{{ row.fileSize }}KB</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="realName" label="上传人" width="width"> </el-table-column>
              <el-table-column align="center" prop="createTime" label="上传时间" width="width"> </el-table-column>
              <el-table-column align="center" label="操作" width="width">
                <template v-slot="{ row }">
                  <el-button type="primary" size="mini" @click="download(row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="meetingCheckDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { meetingList, meetingDetails, meetingSign, meetingDefine, meetingUpdateStatus, meetingSummary } from '@/api/meeting.js'
import { mapGetters } from 'vuex'
import noneImg from '@/assets/meeting/none.png'
export default {
  name: 'Meeting',
  data() {
    return {
      queryInfo: {
        meetingRoomName: null,
        meetingName: null,
        meetingStatus: null,
        realName: null,
        startTime: null,
        endTime: null,
        type: 1,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      showDialog: false,
      replenishSummaryDialog: false,
      fileList: [],
      header: {
        Authorization: null
      },
      meetingId: null,
      meetingCheckDialog: false, // 会议查看dialog
      meetingDetails: {},
      activeName: 'first',
      noneImg
    }
  },
  computed: {
    ...mapGetters(['token'])
  },
  created() {
    this.getMeetingList()
  },

  methods: {
    async getMeetingList() {
      const { data } = await meetingList(this.queryInfo)
      this.total = data.total
      this.list = data.list
    },
    reset() {
      const type = this.queryInfo.type
      this.queryInfo = {
        meetingRoomName: null,
        meetingName: null,
        meetingStatus: null,
        realName: null,
        startTime: null,
        endTime: null,
        type,
        pageNum: 1,
        pageSize: 10
      }
      this.getMeetingList()
    },
    async edit(row) {
      const { data } = await meetingDetails({ meetingId: row.meetingId, belongType: 1 })
      // this.$refs['addOrModify'].edit(data)
      // this.showDialog = true
      window.localStorage.setItem('meetingData', JSON.stringify(data))
      this.$router.push('/addMeeting/1')
      console.log(data)
    },
    // 参加
    comfirmJoin(row) {
      this.$confirm('您确认要参加该会议吗, 是否继续?', '参加会议', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingDefine({ meetingId: row.meetingId, type: 1 })
          this.$message.success('参加成功')
          this.getMeetingList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 拒绝参加
    refuse(row) {
      this.$prompt('请输入拒绝参加的理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async ({ value }) => {
          await meetingDefine({ meetingId: row.meetingId, type: 2, refuseReason: value })
          this.$message.success('拒绝参加成功')
          this.getMeetingList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 签到
    async sign(row) {
      await meetingSign({ meetingId: row.meetingId })
      this.$message.success('签到成功')
      this.getMeetingList()
    },
    // 结束会议
    overMeeting(row) {
      this.$confirm('您确认要结束该会议吗, 是否继续?', '结束会议', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingUpdateStatus({ status: 2, meetingId: row.meetingId })
          this.$message.success('结束会议成功')
          this.getMeetingList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    // 删除
    del(row) {
      this.$confirm('您确认要删除该会议吗, 是否继续?', '删除会议', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await meetingUpdateStatus({ status: 1, meetingId: row.meetingId })
          this.$message.success('删除会议成功')
          this.getMeetingList()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
    },
    /** 上传会议纪要 */
    meeting_summary(row) {
      this.meetingId = row.meetingId
      this.replenishSummaryDialog = true
    },
    beforeUpload(file) {
      this.header.Authorization = `Bearer ${this.token}`
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isDOC = file.type === 'application/msword'
      const isDOCX = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      const isXLS = file.type === 'application/vnd.ms-excel'
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isPDF = file.type === 'application/pdf'
      const isZIP = file.type === 'application/x-zip-compressed'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 50MB!')
      }
      if (!isJPG && !isPNG && !isDOC && !isDOCX && !isXLS && !isXLSX && !isPDF && !isZIP) {
        this.$message.warning('只能上传.doc .docx .xls .xlsx .pdf .jpg .png .zip 格式的附件')
      }
      return (isJPG || isPNG || isDOC || isDOCX || isXLS || isXLSX || isPDF || isZIP) && isLt2M
    },
    uploadSuccess(response, file, fileList) {
      this.fileList = fileList
    },
    // 文件列表移除文件时的钩子
    uploadRemove(file, fileList) {
      this.fileList = fileList
    },
    async replenishSummary() {
      const list = []
      this.fileList.forEach((item) => {
        list.push({
          fileName: item.name,
          fileSize: item.size / 1024,
          fileUrl: item.response.data[0],
          meetingId: this.meetingId
        })
      })
      await meetingSummary(list)
      this.$message.success('补充会议纪要成功')
      this.getMeetingList()
      this.replenishSummaryDialog = false
      this.fileList = []
    },
    // 归档
    async archive(row) {
      await meetingUpdateStatus({ status: 3, meetingId: row.meetingId })
      this.$message.success('归档成功')
      this.getMeetingList()
    },
    // 查看
    check(row) {
      this.$router.push(`/meetingDetails/${row.meetingId}`)
    },
    // 查看中附件下载
    download(row) {
      window.open(row.fileUrl, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  min-height: 100%;
}
.top {
  .top_left {
    display: flex;
    align-items: center;
    .el-button {
      width: 114px;
      height: 38px;
      font-size: 16px;
      font-weight: bold;
      background-color: #3464e0;
      font-family: Microsoft YaHei;
      padding: 9px 0px;
    }

    .el-button--primary {
      background-color: #3464e0;
      border: 1px solid #3464e0;

      &:active {
        background: #3464e0 !important;
        border: 1px solid #3464e0 !important;
      }
    }
    .el-button--primary:focus,
    .el-button--primary:hover {
      background-color: #3464e0;
      border: 1px solid #3464e0;
      color: #fff;
    }

    .meeting_icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      img {
        margin-right: 8px;
      }
    }
  }
  ::v-deep {
    .el-radio-button {
      .el-radio-button__inner {
        border: none;
        background: none;
        font-size: 18px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        span {
          position: relative;
          z-index: 999;
        }
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        font-weight: bold;
        color: #0b1a44;
        -webkit-box-shadow: none;
        &::after {
          content: '';
          position: absolute;
          left: 50%;
          bottom: 10px;
          z-index: 1;
          width: 72px;
          height: 5px;
          background-color: #f3c057;
          transform: translateX(-50%);
        }
      }
    }
    .el-radio-button__inner:hover {
      color: #0b1a44;
    }
  }
}
// 背景图搜索部分
.banner {
  position: relative;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  height: 212px;
  padding-top: 30px;
  background: url('../../assets/meeting/banner.png') no-repeat;
  background-size: cover;
  .banner_mask {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 40px;
    background: #723c28;
    border-radius: 8px 8px 0px 0px;
    background-color: adjust-color($color: #723c28, $alpha: 0.7);
  }
  ::v-deep {
    .el-input {
      width: 577px;
      height: 38px;
      margin-bottom: 9px;
      .el-input__inner {
        height: 100%;
        border-radius: 7px;
        opacity: 0.8;
      }
      .el-input__inner:focus {
        border-color: #dcdfe6;
        opacity: 1;
      }
      .el-input__inner:focus + .el-input__suffix {
        opacity: 1;
      }
      .el-input__suffix {
        display: flex;
        align-items: center;
        margin-right: 10px;
        opacity: 0.3;

        .el-icon-search {
          font-size: 20px;
          font-weight: bold;
          color: #0b1a44;
          cursor: pointer;
        }
      }
    }
    .banner_mask {
      display: flex;
      justify-content: center;
      .el-form-item__label {
        font-weight: 400;
        color: #fff;
        opacity: 1;
      }
      .el-form-item__content {
        width: 180px;
      }
      .meetingTime {
        width: 500px;
        color: #fff;

        .el-form-item__content {
          width: 400px;
        }
        .el-input {
          width: 150px;
        }
        .el-input__prefix {
          display: none;
        }
      }
      .el-input {
        width: 100%;
      }
      .el-input__inner {
        width: 100%;
        background: none;
        border: none;
        opacity: 1 !important;
        color: #fff;
      }
    }
  }
}
.table-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding-top: 56px;
  padding-left: 60px;
  padding-right: 10px;
  margin: 0 !important;

  // 表格渲染部分
  .table {
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    margin-bottom: 32px;
    .table_list {
      margin-right: 32px;
      min-width: 366px;
      background: #fff;
      border-radius: 0 0 5px 5px;
      box-shadow: 1px 3px 2px #eddfc6;
      &:hover {
        background: #f9f9f9;
      }
      img {
        display: block;
        width: 100%;
        height: 38px;
      }
      .table_content {
        min-height: 200px;
        .meetingStatus {
          img {
            margin-top: -1px;
            width: 70px;
            height: 32px;
          }
        }
        .meetingCode {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
        }
        .meetingName {
          padding-left: 24px;
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        // 红黄色样式分割线
        .cut-off {
          padding-left: 24px;
          margin-top: 15px;
          .el-col {
            display: flex;
            align-items: center;
          }
          .first {
            display: inline-block;
            width: 30px;
            height: 0px;
            opacity: 1;
            border: 3px solid #b63342;
          }
          .second {
            display: inline-block;
            width: 58px;
            height: 0px;
            opacity: 1;
            border: 2px solid #ebb749;
          }
        }
        .time {
          padding-left: 24px;
          margin-top: 15px;
          .el-icon-time {
            color: #0b1a44;
            margin-right: 8px;
          }
        }
        .meetingRoomName {
          position: relative;
          padding-left: 24px;
          margin-top: 15px;
          span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #868b9f;
          }
          .operate {
            position: absolute;
            right: 4px;
            bottom: 5px;
            display: flex;
            img {
              width: 44px;
              height: 44px;
              cursor: pointer;
            }
          }
        }
        .realName {
          padding-left: 24px;
          margin-top: 10px;
          padding-bottom: 24px;
          span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #868b9f;
          }
          .isDefine {
            width: 68px;
            height: 28px;
            line-height: 28px;
            border: none;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            border-radius: 4px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          }

          .el-button--primary {
            padding: 0;
            width: 80px;
            height: 28px;
            line-height: 28px;
            border-radius: 4px 4px 4px 4px;
            background-color: #3464e0;
            border: 1px solid #3464e0;
            font-size: 14px;
            font-weight: bold;

            &:active {
              background: #355fce !important;
              border: 1px solid #355fce !important;
            }
          }
          .el-button--primary:focus,
          .el-button--primary:hover {
            background: #355fce;
            border-color: #355fce;
            color: #fff;
          }
          .el-button--danger {
            padding: 0;
            width: 80px;
            height: 28px;
            border-radius: 4px 4px 4px 4px;
            background-color: #ff0000;
            border: 1px solid #ff0000;
            font-size: 14px;
            font-weight: bold;

            &:active {
              background: #e40101 !important;
              border: 1px solid #e40101 !important;
            }
          }
          .el-button--danger:focus,
          .el-button--danger:hover {
            background: #e40101;
            border-color: #e40101;
            color: #fff;
          }

          .meetingStatus2 {
            .el-button--primary {
              padding: 0;
              width: 80px;
              height: 28px;
              line-height: 28px;
              border-radius: 4px 4px 4px 4px;
              background-color: #ff7e26;
              border: 1px solid #ff7e26;
              font-size: 14px;
              &:active {
                background: #ee7726 !important;
                border: 1px solid #ee7726 !important;
              }
            }
            .el-button--primary:focus,
            .el-button--primary:hover {
              background: #ee7726;
              border-color: #ee7726;
              color: #fff;
            }
          }
          .meetingStatus3 {
            .el-button--primary {
              padding: 0;
              width: 80px;
              height: 28px;
              line-height: 28px;
              border-radius: 4px 4px 4px 4px;
              background-color: #f2f4ff;
              border: 1px solid #f2f4ff;
              color: #3464e0;
              font-size: 14px;
              &:active {
                background: #e7e9f2 !important;
                border: 1px solid #e7e9f2 !important;
              }
            }
            .el-button--primary:focus,
            .el-button--primary:hover {
              background: #e7e9f2;
              border-color: #e7e9f2;
              color: #3464e0;
            }
            .summary {
              padding: 0 15px;
              &:hover {
                ::v-deep {
                  span {
                    color: #355fce;
                  }
                }
              }
              ::v-deep {
                span {
                  font-size: 14px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  color: #3464e0;
                  i {
                    font-size: 18px;
                  }
                }
              }
            }
            .pigeonhole {
              width: 68px;
              height: 28px;
              background: #ffe9da;
              border: none;
              border-radius: 4px 4px 4px 4px;
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #ff7e26;

              &:hover {
                ::v-deep {
                  background: #f7ebe4;
                  span {
                    font-size: 14px;
                    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                    font-weight: bold;
                    color: #ff7e26;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  // 分页样式
  ::v-deep {
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #3464e0 !important;
    }
    .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: #3464e0 !important;
    }
    .el-select .el-input.is-focus .el-input__inner {
      border-color: #3464e0;
    }
    .el-select-dropdown__item .selected {
      color: #3464e0;
    }
    .el-input.is-active .el-input__inner,
    .el-input__inner:focus {
      border-color: #3464e0;
    }
  }
}

::v-deep {
  .el-empty {
    width: 316px;
    margin: 0 auto;
    .el-empty__image {
      width: 316px;
    }
    .el-empty__image img {
      width: 316px;
      height: 202px;
    }
    .el-empty__description {
      img {
        width: 74px;
        height: 21px;
      }
    }
    .el-empty__bottom {
      .el-button--primary {
        padding: 0;
        width: 114px;
        height: 38px;
        line-height: 38px;
        border-radius: 4px 4px 4px 4px;
        background-color: #3464e0;
        border: 1px solid #3464e0;
        font-size: 15px;
        font-family: Microsoft YaHei-Bold;
        &:active {
          background: #355fce !important;
          border: 1px solid #355fce !important;
        }
      }
      .el-button--primary:focus,
      .el-button--primary:hover {
        background: #355fce;
        border-color: #355fce;
        color: #fff;
      }
    }
  }
}
</style>
