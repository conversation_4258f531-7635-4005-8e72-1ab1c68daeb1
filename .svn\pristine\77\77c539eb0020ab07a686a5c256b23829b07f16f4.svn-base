<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Unity WebGL Player | UnityWebGLCallTest</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico" />
    <link rel="stylesheet" href="TemplateData/newStyle.css" />
    <script src="../ipconfig.js" type="text/javascript" charset="utf-8"></script>
  </head>

  <body>
    <div id="unity-container" class="unity-desktop">
      <canvas id="unity-canvas" width="960" height="600" tabindex="-1"></canvas>
      <div class="webgl_container_before webgl_infohidden webgl_container" id="webgl_container_before">
        <div id="unity-loading-bar">
          <div id="unity-logo"></div>
          <div id="unity-progress-bar-empty">
            <div id="unity-progress-bar-full"></div>
          </div>
        </div>
        <div class="webgl_infobox">
          <div class="webgl_infoP1"><img src="TemplateData/webgl_infologo.png" /></div>
          <div class="webgl_infoP3">实验正在加载，请耐心等待...</div>
          <div class="webgl_infoP4"><img src="TemplateData/webgl_infoname.png" /></div>
        </div>
      </div>
      <div id="unity-warning"></div>
      <div id="unity-footer">
        <div id="unity-webgl-logo"></div>
        <div id="unity-fullscreen-button"></div>
        <div id="unity-build-title">UnityWebGLCallTest</div>
      </div>
    </div>
    <script>
      // var baseUrl = window.config.VUE_APP_BASE_API
      // var islocal = window.config.VUE_IS_LOCAL
      // var labUrlPath = islocal ? window.config.VUE_FICTITIOUS_URL : window.config.VUE_FILE_OSS_PATH
      // var realName = getQueryVariable('realName')
      // var userName = getQueryVariable('userName')
      // var userId = getQueryVariable('userId')
      // var resourceId = getQueryVariable('resourceId')
      // var examId = getQueryVariable('examId')
      // var time = +getQueryVariable('time')
      // var experimentName = getQueryVariable('experimentName')
      // var experimentUrl = getQueryVariable('experimentUrl')
      // experimentUrl = labUrlPath + experimentUrl
      // var serverURL = experimentUrl.split('/Build')[0]

      window.onload = function () {
        window.addEventListener('message', function (event) {
          console.log(event)

          const parentData = JSON.parse(event.data)
          init(parentData)
          // try {
          //   if (event.data) {
          //     if (event.data.type == 'serialPort') {
          //       sendData(event.data.datainfo)
          //     }
          //   }
          // } catch (err) {
          //   console.log(err)
          // }
        })
      }

      // 加载完成
      function loadComplete() {}

      // function getQueryVariable(variable) {
      //   if (window.location.href.indexOf('?') > 0) {
      //     var query = window.location.href.split('?')[1]
      //     var vars = query.split('&')
      //     for (var i = 0; i < vars.length; i++) {
      //       var pair = vars[i].split('=')
      //       if (pair[0] == variable) {
      //         return decodeURIComponent(pair[1])
      //       }
      //     }
      //   }
      //   return ''
      // }

      function screenFull() {
        var height = document.documentElement.clientHeight - 5
        var width = document.documentElement.clientWidth - 5
        document.getElementById('webgl_container_before').style.height = height + 'px'
        document.getElementById('webgl_container_before').style.width = width + 'px'
        return {
          width: width + 'px',
          height: height + 'px'
        }
      }

      var container = document.querySelector('#unity-container')
      var canvas = document.querySelector('#unity-canvas')
      var loadingBar = document.querySelector('#unity-loading-bar')
      var progressBarFull = document.querySelector('#unity-progress-bar-full')
      var fullscreenButton = document.querySelector('#unity-fullscreen-button')
      var warningBanner = document.querySelector('#unity-warning')
      var webgl_infohidden = document.querySelector('.webgl_infohidden')

      // Shows a temporary message banner/ribbon for a few seconds, or
      // a permanent error message on top of the canvas if type=='error'.
      // If type=='warning', a yellow highlight color is used.
      // Modify or remove this function to customize the visually presented
      // way that non-critical warnings and error messages are presented to the
      // user.
      function unityShowBanner(msg, type) {
        function updateBannerVisibility() {
          warningBanner.style.display = warningBanner.children.length ? 'block' : 'none'
        }
        console.log(msg)

        var div = document.createElement('div')
        div.innerHTML = msg
        // warningBanner.appendChild(div)
        if (type == 'error') div.style = 'background: red; padding: 10px;'
        else {
          if (type == 'warning') div.style = 'background: yellow; padding: 10px;'
          setTimeout(function () {
            // warningBanner.removeChild(div)
            updateBannerVisibility()
          }, 5000)
        }
        updateBannerVisibility()
      }
      function close() {
        window.parent.postMessage('close', '*')
      }

      // By default, Unity keeps WebGL canvas render target size matched with
      // the DOM size of the canvas element (scaled by window.devicePixelRatio)
      // Set this to false if you want to decouple this synchronization from
      // happening inside the engine, and you would instead like to size up
      // the canvas DOM size and WebGL render target sizes yourself.
      // config.matchWebGLToCanvasSize = false;
      function setResDir() {}
      function init(parentData) {
        // var buildUrl = experimentUrl
        var loaderUrl = parentData.loaderUrl

        var config = {
          // dataUrl: buildUrl + '.data.unityweb',
          // frameworkUrl: buildUrl + '.framework.js.unityweb',
          // codeUrl: buildUrl + '.wasm.unityweb',
          ...parentData.config,
          streamingAssetsUrl: 'StreamingAssets',
          companyName: 'DefaultCompany',
          productName: 'UnityWebGLCallTest',
          productVersion: '0.1',
          showBanner: unityShowBanner
        }
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
          // Mobile device style: fill the whole browser client area with the game canvas:

          var meta = document.createElement('meta')
          meta.name = 'viewport'
          meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes'
          document.getElementsByTagName('head')[0].appendChild(meta)
          container.className = 'unity-mobile'
          canvas.className = 'unity-mobile'

          // To lower canvas resolution on mobile devices to gain some
          // performance, uncomment the following line:
          // config.devicePixelRatio = 1;
        } else {
          // Desktop style: Render the game canvas in a window that can be maximized to fullscreen:
          var widthHeight = screenFull()
          canvas.style.width = widthHeight.width
          canvas.style.height = widthHeight.height
        }

        loadingBar.style.display = 'block'

        var script = document.createElement('script')
        script.src = loaderUrl
        script.onload = () => {
          createUnityInstance(canvas, config, (progress) => {
            progressBarFull.style.width = 100 * progress + '%'
          })
            .then((unityInstance) => {
              const msgData = JSON.stringify({ resBaseUrl: parentData.directoryUrl })
              var msgObject = {
                // msgId: msgId, // 消息唯一编号 时间戳即可
                msgCmdCode: '10001', //消息码，消息扩展码
                msgConnector: 'ZFFramework', // 消息连接物体名
                msgCallMethod: 'OnReceiveWebMsg', // 消息调用函数名
                msgNeedCallBack: '0', // 固定值 0或 1
                msgData // 真正的消息 根据不同的消息码，传递不同的json数据
              }
              var msg = JSON.stringify(msgObject)
              unityInstance.SendMessage('ZFFramework', 'OnReceiveWebMsg', msg)

              loadingBar.style.display = 'none'
              webgl_infohidden.style.display = 'none'
              fullscreenButton.onclick = () => {
                unityInstance.SetFullscreen(1)
              }
            })
            .catch((message) => {
              alert(message)
            })
        }

        document.body.appendChild(script)
      }
      function OnReceiveUnityMsg(val) {}
    </script>
  </body>
</html>
