<template>
  <el-drawer :visible="visible" direction="rtl" size="780px" :show-close="false" :modal="true" class="booking-drawer">
    <template v-slot:title>
      <div class="drawer-title">{{ isEditMode ? '修改预约' : '新建预约' }}</div>
      <div class="close-btn" @click="handleClose">
        <i class="el-icon-close"></i>
      </div>
    </template>
    <div ref="drawerContentRef" class="drawer-content">
      <!-- 顶部标题 -->
      <div class="drawer-header" :class="roomTypeClass">
        <div class="room-info">
          <svg-icon icon-class="newMeeting" class="room-icon"></svg-icon>
          <div class="room-title">
            {{ roomTitle }}
          </div>
          <div class="time-info" :style="{ color: roomColor }">（{{ startTime }}-{{ endTime }}）·（{{ durationText }}）</div>
        </div>
      </div>

      <!-- 表单内容 -->
      <div class="form-content">
        <el-form ref="form" :model="formData" label-position="top">
          <el-form-item label="会议主题:" prop="conferenceTitle">
            <el-input v-model="formData.conferenceTitle" placeholder="请输入会议主题" class="form-input" />
          </el-form-item>

          <el-form-item label="会议时长（分钟）：">
            <div class="duration-slider">
              <el-slider v-model="duration" :min="30" :max="maxDuration" :step="30" :show-tooltip="false" />
              <span class="duration-text">{{ durationText }}</span>
            </div>
          </el-form-item>
          <el-form-item label="参会人员：">
            <div class="participants-section">
              <div class="recommended-title">推荐常用参会人</div>
              <div class="participants-list">
                <div v-for="person in recommendedParticipants" :key="person.userId" class="participant-item" :class="{ selected: isParticipantSelected(person) }" @click="toggleParticipant(person)">
                  {{ person.userName }}
                </div>
              </div>
            </div>
            <div v-if="departments" class="department-section">
              <div class="department-title">按部门选择</div>
              <div v-for="dept in departments" :key="dept.organizationId" class="department-item">
                <div class="dept-header" @click="toggleSpread(dept)">
                  <span class="spread-btn">
                    {{ spreadDepartments.includes(dept.organizationId) ? '收起' : '展开' }}
                    <i class="el-icon-arrow-up" :style="{ transform: `rotate(${spreadDepartments.includes(dept.organizationId) ? 0 : 180}deg)` }"></i>
                  </span>
                  <span class="dept-name">{{ dept.organizationName }}</span>
                  <el-button type="text" class="select-all-btn" :class="{ cancel: getDeptSelectText(dept) === '取消选择' }" @click.stop="selectAllDept(dept)">
                    {{ getDeptSelectText(dept) }}
                  </el-button>
                </div>
                <el-collapse-transition>
                  <div v-show="spreadDepartments.includes(dept.organizationId)" class="dept-members">
                    <div v-for="member in dept.users" :key="member.userId" class="member-item" :class="{ selected: isParticipantSelected(member) }" @click="toggleParticipant(member)">
                      {{ member.realName }}
                    </div>
                  </div>
                </el-collapse-transition>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 底部按钮 -->
      <div class="drawer-footer">
        <el-button v-if="isEditMode" @click="handleCancelMeeting">取消预约</el-button>
        <el-button v-else @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleConfirm"> {{ isEditMode ? '确认修改' : '确认预约' }} </el-button>
      </div>
    </div>

    <!-- 取消预约弹窗 -->
    <el-dialog title="取消预约" custom-class="cancelMeetingDialog" :visible.sync="cancelDialogVisible" width="500px" center :close-on-click-modal="false" :append-to-body="true">
      <el-form ref="cancelForm" :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" prop="reason" :rules="[{ required: true, message: '请输入取消原因', trigger: 'blur' }]">
          <el-input v-model="cancelForm.reason" type="textarea" :rows="3" placeholder="请输入取消原因" maxlength="50" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="cancelSubmitting" @click="confirmCancel">确定</el-button>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
import { conference_add, conference_update, conference_cancel, conference_common_contact, conference_org_select } from '@/api/newMeeting'
import { mapGetters } from 'vuex'
export default {
  name: 'BookingDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    bookingInfo: {
      type: Object,
      default: () => ({})
    },
    selectedDate: {
      type: String,
      default: ''
    },
    meetingData: {
      type: Object,
      default: null
    },
    allMeetingData: {
      type: Object,
      default: () => ({ one: [], two: [] })
    }
  },
  data() {
    return {
      duration: 30, // 默认30分钟
      submitting: false,
      formData: {
        conferenceTitle: '',
        conferenceUserIds: '',
        conferenceUserNames: ''
      },
      selectedParticipants: [], // 选中的参会人员
      recommendedParticipants: [], // 推荐常用参会人
      spreadDepartments: [], // 展开的部门id
      departments: [],
      isEditMode: false, // 是否为编辑模式
      editingMeetingId: null, // 正在编辑的会议ID
      cancelDialogVisible: false, // 取消预约弹窗显示状态
      cancelSubmitting: false, // 取消预约提交状态
      cancelForm: {
        reason: '' // 取消原因
      }
    }
  },
  computed: {
    ...mapGetters(['userId', 'organizationId']),
    roomTypeClass() {
      return this.bookingInfo.roomType === 'large' ? 'large-room' : 'small-room'
    },
    roomColor() {
      return this.bookingInfo.roomType === 'large' ? '#3465df' : '#13b755'
    },
    roomTitle() {
      return this.bookingInfo.roomType === 'large' ? '大会议室' : '小会议室'
    },

    startTime() {
      return this.bookingInfo.startTime || '8:00'
    },
    endTime() {
      if (!this.bookingInfo.startTime) return '8:30'

      const [hour, minute] = this.bookingInfo.startTime.split(':').map(Number)
      const totalMinutes = hour * 60 + minute + this.duration

      // 限制最晚到18:00
      const maxEndTime = 18 * 60 // 18:00对应的分钟数
      const actualEndMinutes = Math.min(totalMinutes, maxEndTime)
      const actualEndHour = Math.floor(actualEndMinutes / 60)
      const actualEndMinute = actualEndMinutes % 60

      return `${actualEndHour}:${actualEndMinute.toString().padStart(2, '0')}`
    },
    // 计算最大可选时长（限制结束时间不超过18:00）
    maxDuration() {
      if (!this.bookingInfo.startTime) return 480

      const [hour, minute] = this.bookingInfo.startTime.split(':').map(Number)
      const startMinutes = hour * 60 + minute
      const maxEndMinutes = 18 * 60 // 18:00
      const maxDurationMinutes = maxEndMinutes - startMinutes

      return Math.max(30, maxDurationMinutes)
      // return Math.max(30, Math.min(480, maxDurationMinutes))
    },
    durationText() {
      if (this.duration < 60) {
        return `${this.duration}分钟`
      } else {
        const hours = Math.floor(this.duration / 60)
        const minutes = this.duration % 60
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData()
        this.loadRecommendedParticipants()
        this.loadDepartments()

        // 如果有会议数据，说明是编辑模式
        if (this.meetingData) {
          this.initEditMode()
        }
        // 让滚动条回到顶部
        this.$nextTick(() => {
          this.$refs['drawerContentRef'].scrollTop = 0
        })
      } else {
        // 关闭时清除数据
        this.clearData()
      }
    },
    meetingData: {
      handler(val) {
        if (val && this.visible) {
          this.initEditMode()
        }
      },
      immediate: true
    },
    // 监听时长变化，确保不超过最大限制
    duration(val) {
      if (val > this.maxDuration) {
        this.duration = this.maxDuration
      }
    }
  },
  methods: {
    // 初始化数据
    initData() {
      this.duration = 30
      this.formData = {
        conferenceTitle: '',
        conferenceUserIds: '',
        conferenceUserNames: ''
      }
      this.selectedParticipants = []
      this.isEditMode = false
      this.editingMeetingId = null
    },

    // 清除数据（关闭时调用）
    clearData() {
      this.duration = 30
      this.formData = {
        conferenceTitle: '',
        conferenceUserIds: '',
        conferenceUserNames: ''
      }
      this.selectedParticipants = []
      this.isEditMode = false
      this.editingMeetingId = null
      this.spreadDepartments = []
      this.cancelDialogVisible = false
      this.cancelForm.reason = ''
    },

    // 初始化编辑模式
    initEditMode() {
      if (!this.meetingData) return

      this.isEditMode = true
      this.editingMeetingId = this.meetingData.conferenceId
      this.formData.conferenceTitle = this.meetingData.conferenceTitle

      // 计算会议时长
      const startTime = this.parseTime(this.meetingData.startTime)
      const endTime = this.parseTime(this.meetingData.endTime)
      this.duration = Math.round((endTime - startTime) * 60)

      // 设置参会人员
      if (this.meetingData.conferenceUserIds && this.meetingData.conferenceUserNames) {
        const userIds = this.meetingData.conferenceUserIds.split(',')
        const userNames = this.meetingData.conferenceUserNames.split(',')

        this.selectedParticipants = userIds.map((id, index) => ({
          userId: id,
          userName: userNames[index] || '',
          realName: userNames[index] || ''
        }))

        this.formData.conferenceUserIds = this.meetingData.conferenceUserIds
        this.formData.conferenceUserNames = this.meetingData.conferenceUserNames
      }
    },

    // 解析时间字符串为小时数
    parseTime(timeStr) {
      const [hour, minute] = timeStr.split(':').map(Number)
      return hour + minute / 60
    },
    // 是否选中该参会人员
    isParticipantSelected(person) {
      return this.selectedParticipants.some((p) => p.userId === person.userId)
    },
    // 切换参会人员
    toggleParticipant(person) {
      const index = this.selectedParticipants.findIndex((p) => p.userId === person.userId)
      if (index > -1) {
        this.selectedParticipants.splice(index, 1)
      } else {
        this.selectedParticipants.push(person)
      }
      this.updateParticipantsData()
    },
    // 选择该部门所有人员
    selectAllDept(dept) {
      const allSelected = dept.users.every((member) => this.isParticipantSelected(member))
      if (allSelected) {
        // 取消选择该部门所有人员
        dept.users.forEach((member) => {
          const index = this.selectedParticipants.findIndex((p) => p.userId === member.userId)
          if (index > -1) {
            this.selectedParticipants.splice(index, 1)
          }
        })
      } else {
        // 选择该部门所有人员
        dept.users.forEach((member) => {
          if (!this.isParticipantSelected(member)) {
            this.selectedParticipants.push(member)
          }
        })
      }
      this.updateParticipantsData()
    },
    // 获取选择部门的文本
    getDeptSelectText(dept) {
      const selectedCount = dept.users.filter((member) => this.isParticipantSelected(member)).length
      const totalCount = dept.users.length
      return selectedCount === totalCount ? '取消选择' : '选择该部门'
    },
    updateParticipantsData() {
      this.formData.conferenceUserIds = this.selectedParticipants.map((p) => p.userId).join(',')
      this.formData.conferenceUserNames = this.selectedParticipants.map((p) => p.realName || p.userName).join(',')
      console.log(this.formData.conferenceUserNames)
    },
    // #region 推荐常用参会人

    // 加载推荐常用参会人
    async loadRecommendedParticipants() {
      try {
        const response = await conference_common_contact()
        this.recommendedParticipants = response.data || []
      } catch (error) {
        console.error('加载推荐参会人失败:', error)
      }
    },
    // #endregion

    // #region 按部门选择

    /** 加载部门 */
    async loadDepartments() {
      try {
        const response = await conference_org_select()
        this.departments = response.data || []
        // 去除登录用户本身数据
        this.departments.forEach((dept) => {
          dept.users = dept.users.filter((user) => user.userId !== this.userId)
        })
        this.departments.sort((a, b) => {
          if (a.organizationId === this.organizationId) {
            return -1
          }
          return a.organizationId - b.organizationId
        })
      } catch (error) {
        console.error('加载部门失败:', error)
      }
    },
    /** 展开/收起部门 */
    toggleSpread(dept) {
      if (this.spreadDepartments.includes(dept.organizationId)) {
        this.spreadDepartments = this.spreadDepartments.filter((d) => d !== dept.organizationId)
      } else {
        this.spreadDepartments.push(dept.organizationId)
      }
    },

    // #endregion

    async handleConfirm() {
      if (!this.formData.conferenceTitle) {
        this.$message.warning('请输入会议主题')
        return
      }
      // if (this.selectedParticipants.length === 0) {
      //   this.$message.warning('请选择参会人员')
      //   return
      // }

      // 检查时间冲突
      if (!this.checkTimeConflict()) {
        return
      }

      this.submitting = true
      try {
        const data = {
          ...this.formData,
          conferenceRoom: this.bookingInfo.roomType === 'large' ? 'one' : 'two',
          reservedDate: this.selectedDate,
          startTime: this.startTime,
          endTime: this.endTime
        }

        if (this.isEditMode) {
          // 修改会议
          data.conferenceId = this.editingMeetingId
          await conference_update(data)
          this.$message.success('修改成功')
        } else {
          // 新建会议
          await conference_add(data)
          this.$message.success('预约成功')
        }

        this.$emit('booking-success')
        this.handleClose()
      } catch (error) {
        const errorMsg = this.isEditMode ? '修改失败：' : '预约失败：'
        this.$message.error(errorMsg + (error.message || '未知错误'))
      } finally {
        this.submitting = false
      }
    },

    // 检查时间冲突
    checkTimeConflict() {
      // 获取当前会议室的所有会议
      const currentRoomMeetings = this.bookingInfo.roomType === 'large' ? this.allMeetingData.one || [] : this.allMeetingData.two || []

      const startTime = this.parseTime(this.startTime)
      const endTime = this.parseTime(this.endTime)

      // 检查是否与其他会议时间冲突
      const hasConflict = currentRoomMeetings.some((meeting) => {
        // 如果是编辑模式，排除当前编辑的会议
        if (this.isEditMode && meeting.conferenceId === this.editingMeetingId) {
          return false
        }

        const meetingStart = this.parseTime(meeting.startTime)
        const meetingEnd = this.parseTime(meeting.endTime)

        // 检查时间段是否重叠
        return startTime < meetingEnd && endTime > meetingStart
      })

      if (hasConflict) {
        this.$message.warning('选择的时间段与其他会议冲突，请重新选择时间')
        return false
      }

      return true
    },

    // 处理取消预约
    handleCancelMeeting() {
      this.cancelForm.reason = ''
      this.cancelDialogVisible = true
    },

    // 确认取消预约
    async confirmCancel() {
      // 验证表单
      try {
        await this.$refs.cancelForm.validate()
      } catch (error) {
        return
      }

      this.cancelSubmitting = true
      try {
        await conference_cancel({
          conferenceId: this.editingMeetingId,
          cancelDescription: this.cancelForm.reason
        })

        this.$message.success('取消预约成功')
        this.cancelDialogVisible = false
        this.$emit('booking-success') // 刷新数据
        this.handleClose()
      } catch (error) {
        this.$message.error('取消预约失败：' + (error.message || '未知错误'))
      } finally {
        this.cancelSubmitting = false
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.booking-drawer {
  ::v-deep .el-drawer {
    border-radius: 0;
    &__header {
      width: 100%;
      height: 50px;
      padding: 0;
      margin: 0;
      background: #fafafa;
      .drawer-title {
        padding-left: 35px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
      .close-btn {
        position: absolute;
        top: 14px;
        right: 24px;
        cursor: pointer;
        i {
          font-size: 20px;
          font-weight: 600;
          color: #657081;
        }
      }
    }
  }

  ::v-deep .el-drawer__body {
    padding: 0;
  }
}

.drawer-content {
  height: 100%;
  padding: 0 35px;
  overflow-y: auto;
  padding-top: 20px;
}

.drawer-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  border-radius: 8px;
  border-left: 6px solid;
  &.large-room {
    background: #f7f9fe;
    border-color: #4d7bef;
    .room-info {
      .room-icon {
        color: #4d7bef;
      }
      .time-info {
        color: #3465df;
      }
    }
  }

  &.small-room {
    background: #f7fdf9;
    border-color: #13b755;
    .room-info {
      .room-icon {
        color: #13b755;
      }
      .time-info {
        color: #13b755;
      }
    }
  }

  .room-info {
    display: flex;
    align-items: center;
    justify-content: center;
    .room-icon {
      margin-right: 8px;
      font-size: 28px;
    }
    .room-title {
      margin-left: 6px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
    }

    .time-info {
      width: 235px;
      margin-left: 20px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.form-content {
  padding-top: 14px;
  ::v-deep {
    .el-form {
      .el-form-item {
        margin-bottom: 10px;
        .el-form-item__label {
          padding-bottom: 0;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #333333;
        }
        .el-input__inner {
          height: 48px;
          border-radius: 8px;
          border: 1px solid #eaeced;
          font-size: 14px;

          &::placeholder {
            color: #b0b0b0;
          }
        }
        .duration-slider {
          display: flex;
          align-items: center;
          width: 100%;

          .el-slider {
            flex: 1;
            margin-right: 30px;

            .el-slider__runway {
              margin: 0;
              height: 16px;
              border-radius: 40px;
              background: #f0f0f0;
            }

            .el-slider__bar {
              height: 16px;
              border-radius: 40px 0 0 40px;

              background: #3465df;
            }

            .el-slider__button-wrapper {
              top: 50%;
              transform: translate(-20%, -50%);
            }
            .el-slider__button {
              width: 24px;
              height: 24px;
              background: #5580eb;
              border: 2px solid #fff;
              box-shadow: -2px 4px 4px 0px rgba(0, 0, 0, 0.15);
            }
          }

          .duration-text {
            width: 116px;
            text-align: right;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #333333;
          }
        }
        .participants-section {
          .participants-list {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
          }
        }
        .recommended-title,
        .department-title {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          margin-bottom: 5px;
        }
        .participant-item,
        .member-item {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 44px;
          background: #fff;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #e2e2e2;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 15px;
          color: #333333;
          cursor: pointer;
          &.selected {
            background: #4d7bef;
            color: #fff;
            border-color: #4d7bef;
          }
        }

        .department-section {
          margin-top: 10px;
          .department-item {
            border: 1px solid #eaeced;
            border-radius: 8px;
            margin-bottom: 16px;

            .dept-header {
              display: flex;
              align-items: center;
              height: 48px;
              padding: 0 20px;
              cursor: pointer;
              .spread-btn {
                width: 60px;
                height: 32px;
                line-height: 32px;
                background: #f3f5f8;
                border-radius: 8px 8px 8px 8px;
                border: 1px solid #e2e2e2;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                text-align: center;
                cursor: pointer;
                i {
                  transition: all 0.2s ease;
                }
              }
              .dept-name {
                margin-left: 20px;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 16px;
                color: #333333;
              }

              .select-all-btn {
                margin-left: auto;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #3465df;
                &.cancel {
                  color: #ff1f1f;
                }
              }
            }

            .dept-members {
              display: flex;
              flex-wrap: wrap;
              gap: 20px;
              row-gap: 10px;
              padding: 10px 20px;
              border-top: 1px solid #eaeced;
            }
          }
        }
      }
    }
  }
}

.drawer-footer {
  padding: 16px 24px;
  padding-right: 0;
  border-top: 1px solid #e9e9e9;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    padding: 10px 24px;
    border-radius: 8px 8px 8px 8px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 18px;
    &--default {
      background: #f3f5f8;
      border-color: #e2e2e2;
      color: #333333;
    }
    &--primary {
      background: #3465df;
      border-color: #3465df;
      color: #ffffff;
    }
  }
}
</style>

<style lang="scss">
.cancelMeetingDialog {
  border-radius: 30px;
  .el-dialog__header {
    padding: 20px 30px;
    border-bottom: 1px solid #d9d9d9;
    .el-dialog__title {
      font-family: PingFang SC, PingFang SC;
      font-size: 20px;
      color: #333333;
    }
    .el-dialog__headerbtn {
      i {
        font-size: 32px;
      }
    }
  }
  .el-dialog__body {
    padding: 30px;
    padding-bottom: 0;
    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #333333;
        }
        .el-textarea__inner {
          border: 1px solid #eaeced;
          border-radius: 8px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #333333;
        }
      }
    }
  }
}
</style>
