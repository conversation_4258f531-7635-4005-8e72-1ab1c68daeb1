<template>
  <div>
    <el-dialog :title="dialogTitle" :visible="showDialog" width="500px" @close="close">
      <el-form ref="form" :model="formInfo" :rules="rules" label-width="85px">
        <el-form-item label="方案标题:" prop="name">
          <el-input v-model="formInfo.name" placeholder="请输入方案标题"></el-input>
        </el-form-item>
        <el-form-item label="附件:" prop="fileReqs">
          <el-upload class="upload-demo" :action="action" :headers="headers" :on-remove="handleRemove" :on-success="handleSuccess" :before-upload="handleBeforeUpload" :file-list="fileList" multiple>
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传pdf、docx文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注:">
          <el-input v-model="formInfo.remark" placeholder="请输入备注" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="confrim">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { tPlanShareAdd, tPlanShareUpdate } from '@/api/tPlanShare'
export default {
  name: 'AddtPlanShare',
  props: {
    showDialog: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      formInfo: {
        name: null,
        remark: null,
        fileReqs: []
      },
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      headers: {
        Authorization: null
      },
      fileList: [],
      rules: {
        name: [{ required: true, message: '请输入方案标题', trigger: 'blur' }],
        fileReqs: [{ type: 'array', required: true, message: '附件不能为空', trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapGetters(['token']),
    dialogTitle() {
      return this.formInfo.planShareId ? '编辑方案' : '新增方案'
    }
  },
  created() {},
  methods: {
    handleRemove(file, fileList) {
      this.fileList = fileList
      const list = []
      fileList.forEach((item) => {
        if ((item.response && item.response.code === 200) || item.belongType) {
          list.push({
            fileName: item.name,
            fileSize: item.fileSize ? item.fileSize : parseInt(item.size / 1024),
            fileUrl: item.fileUrl ? item.fileUrl : item.response.data[0],
            belongType: 16
          })
        }
      })
      this.formInfo.fileReqs = list
      this.$refs['form'].validateField('fileReqs')
    },
    handleSuccess(res, file, fileList) {
      this.fileList = fileList
      const list = []
      fileList.forEach((item) => {
        if ((item.response && item.response.code === 200) || item.belongType) {
          list.push({
            fileName: item.name,
            fileSize: item.fileSize ? item.fileSize : parseInt(item.size / 1024),
            fileUrl: item.fileUrl ? item.fileUrl : item.response.data[0],
            belongType: 16
          })
        }
      })
      this.formInfo.fileReqs = list
      this.$refs['form'].validateField('fileReqs')
    },
    handleBeforeUpload(file) {
      this.headers.Authorization = 'Bearer ' + this.token
      const ispdf = file.type === 'application/pdf'
      const isword = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      if (!ispdf && !isword) this.$message.warning('只能上传pdf、doc、docx文件')
      return ispdf || isword
    },
    confrim() {
      this.$refs['form'].validate((val) => {
        if (val) {
          console.log(this.formInfo)
          const loading = this.$loading({
            text: '数据保存中，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          if (this.formInfo.planShareId) {
            tPlanShareUpdate(this.formInfo)
              .then((res) => {
                loading.close()
                this.$message.success('数据保存成功!')
                this.close()
                this.$emit('success')
              })
              .catch(() => {
                loading.close()
              })
          } else {
            tPlanShareAdd(this.formInfo)
              .then((res) => {
                loading.close()
                this.$message.success('数据保存成功!')
                this.close()
                this.$emit('success')
              })
              .catch(() => {
                loading.close()
              })
          }
        }
      })
    },
    close() {
      this.formInfo = {
        name: null,
        remark: null,
        fileReqs: []
      }
      this.fileList = []
      this.$refs['form'].resetFields()
      this.$emit('update:showDialog', false)
    },
    showEdit(row) {
      this.formInfo = {
        name: row.name,
        remark: row.remark,
        fileReqs: row.fileDtos,
        planShareId: row.planShareId
      }
      if (this.formInfo.fileReqs.length) {
        this.formInfo.fileReqs.forEach((item) => {
          this.fileList.push({
            ...item,
            name: item.fileName
          })
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog {
    .el-dialog__body {
      .el-upload__tip {
        margin-top: 0;
        font-size: 14px;
      }
    }
  }
}
</style>
