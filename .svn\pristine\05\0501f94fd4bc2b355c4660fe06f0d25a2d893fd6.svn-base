<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no" />
    <link rel="icon" href="../favicon.ico" />
    <title>公司内部系统</title>
    <link rel="shortcut icon" href="./TemplateData/favicon.ico" />
    <link rel="stylesheet" href="./TemplateData/style.css" />
    <link rel="stylesheet" href="./TemplateData/animate.min.css" />
    <script src="./jquery-3.7.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./TemplateData/UnityLoader.js"></script>
    <script src="./TemplateData/UnityProgress.js"></script>
    <script type="text/javascript">
      let un, vrSign, webGlUrl, index, jurl, serverURL
      // window.setData = function (data) {
      //   un = data.userId
      //   webGlUrl = data.emulationUrl
      //   vrSign = data.emulationId
      //   index = webGlUrl.lastIndexOf('Build')
      //   jurl = webGlUrl.substring(index, webGlUrl.length)
      //   serverURL = webGlUrl.replace(jurl, '')
      //   getLib()
      // }
      // window.fullScreen = function () {
      //   setFullScreen()
      // }
      let source
      window.addEventListener(
        'message',
        function (e) {
          source = e.sources
          console.log(e, '这是上级的数据')
          if (e.data === 'fullScreen') {
            setFullScreen()
          } else {
            const data = JSON.parse(e.data)
            un = data.userId
            webGlUrl = data.emulationUrl
            vrSign = data.emulationId
            index = webGlUrl.lastIndexOf('Build')
            jurl = webGlUrl.substring(index, webGlUrl.length)
            serverURL = webGlUrl.replace(jurl, '')
            getLib()
          }
        },
        false
      )
      // window.addEventListener('resize', screenFull())
      function getLib() {
        screenFull()
        var jsonUrl = webGlUrl
        gameInstance = UnityLoader.instantiate('gameContainer', jsonUrl, {
          onProgress: UnityProgress
        })
      }
      function setFullScreen() {
        if (document.querySelector('.webgl_footfullscreen')) {
          gameInstance.SetFullscreen(1)
        }
      }
      function loadComplete() {
        if (un) {
          var usedata = {
            username: un,
            appid: '',
            vrSign: vrSign
          }
          usedata = JSON.stringify(usedata)
          gameInstance.SendMessage('NetworkInterface', 'init', usedata)
        }
      }

      function close(flag) {
        window.parent.postMessage('close', '*')
        // source.postMessage('Nice to see you!', '*');
        // window.evt = document.createEvent('Event');
        // window.evt.initEvent('myEvent', true, true);
        // window.dispatchEvent(window.evt); //让自定义事件触发
      }

      function closeWindow() {
        // $.ajax({
        // 	type: "put",
        // 	url: baseUrl + "/simulation/record/" + recordId,
        // 	headers: {
        // 		'token': token
        // 	}
        // });
        // window.evt = document.createEvent('Event');
        // window.evt.initEvent('myEvent', true, true);
        // window.dispatchEvent(window.evt); //让自定义事件触发
        // window.parent.postMessage('close', '*')
        // source.postMessage('Nice to see you!', '*');
      }

      function netError() {
        var netConfirm = confirm('网络连接中断，是否刷新？')
        if (netConfirm == true) {
          location.reload()
        } else {
          closeWindow()
        }
      }

      function setResDir() {
        console.log('164 ===================================')
        $('.webgl_infobox').addClass('webgl_infohidden')
        $('.webgl_infotips').addClass('webgl_infohidden')
        gameInstance.SendMessage('ServerURL', 'SetServerURL', serverURL)
      }

      function screenFull() {
        var height = document.documentElement.clientHeight - 50
        var width = document.documentElement.clientWidth - 10
        var w = (height / 9) * 16
        if (w > width) {
          height = (width / 16) * 9
          w = width
        }
        document.getElementById('gameContainer').style.height = height + 'px'
        document.getElementById('gameContainer').style.width = w + 'px'
      }

      function getQueryVariable(variable) {
        if (window.location.href.indexOf('?') > 0) {
          var query = window.location.href.split('?')[1]
          var vars = query.split('&')
          for (var i = 0; i < vars.length; i++) {
            var pair = vars[i].split('=')
            if (pair[0] == variable) {
              return pair[1]
            }
          }
        }
        return false
      }

      function HideProgress() {
        OnUnityInit(gameInstance)

        document.querySelector('.webgl_infohidden').style.display = 'none'
      }
      function OnUnityInit(unityInstance) {
        console.log('-------------------------------------------------------')
        // var isHash = window.config.isHash
        // var waterMark = window.config.waterMark
        // var url = window.config.url
        // var hurl = url + 'api/vrEncrypt'

        var msgObject = {
          waterMark: 1,
          isHash: 1,
          hashUrl: 'http://yun.51-x.cn/api/vrEncrypt',
          resBaseUrl: serverURL
        }
        var msgData = JSON.stringify(msgObject)
        var msgId = new Date().getTime()
        SendWebMsgToUnity('1', '10001', 'ZFFramework', 'OnReceiveWebMsg', msgData, '0')
      }

      // 接收unity消息
      function OnReceiveUnityMsg(msg) {
        var msgObject = JSON.parse(msg)
        var msgId = msgObject.msgId
        var msgCmdCode = msgObject.msgCmdCode
        var msgConnector = msgObject.msgConnector
        var msgCallMethod = msgObject.msgCallMethod
        var msgNeedCallBack = msgObject.msgNeedCallBack
        var msgData = msgObject.msgData
        switch (msgCmdCode) {
          case '0':
            OnReceiveUnityLogMsg(msgData)
            break
          case '10001':
            OnReceiveUnityStartMsg(msgData)
            break
          case '10002':
            OnReceiveUnityCloseMsg(msgData)
            break
          case '10003':
            OnReceiveUnitySubmitMsg(msgData)
            break
        }
      }
      function OnReceiveUnityLogMsg(msgData) {
        console.log('web端接收到unity消息' + msgData)
      }
      function OnReceiveUnityStartMsg(msgData) {
        console.log('web端接收到unity启动消息' + msgData)
      }
      function OnReceiveUnityCloseMsg(msgData) {
        console.log('web端接收到unity关闭消息' + msgData)
        close('close')
      }
      function OnReceiveUnitySubmitMsg(msgData) {
        console.log('web端接收到unity提交消息' + msgData)
      }

      function SendWebMsgToUnity(msgId, msgCmdCode, msgConnector, msgCallMethod, msgData, msgNeedCallBack) {
        var msgObject = {
          msgId: msgId, // 消息唯一编号 时间戳即可
          msgCmdCode: msgCmdCode, //消息码，消息扩展码
          msgConnector: msgConnector, // 消息连接物体名
          msgCallMethod: msgCallMethod, // 消息调用函数名
          msgNeedCallBack: msgNeedCallBack, // 固定值 0或 1
          msgData: msgData // 真正的消息 根据不同的消息码，传递不同的json数据
        }
        var msg = JSON.stringify(msgObject)
        gameInstance.SendMessage(msgConnector, msgCallMethod, msg)
      }
    </script>
  </head>

  <body>
    <!-- <div class="webgl_content">
      <div id="gameContainer" class="webgl_container"></div>
      <div class="webgl_virtual">
        <div class="webgl_infobox">
          <img class="webgl_logo animate__animated animate__zoomIn" src="./TemplateData/logo.png" alt="" />
          <img class="webgl_light" src="./TemplateData/light.png" alt="" />
          <div class="webgl_loading_bg">
            <div class="progress_bar"></div>
          </div>
          <div class="webgl_loading_text"></div>
        </div>
      </div>
      <div class="webgl_footfullscreen" onclick="gameInstance.SetFullscreen(1)"></div>
    </div> -->

    <div class="webgl-content">
      <div id="gameContainer" class="webgl_container"></div>
      <div class="webgl_infobox webgl_infohidden">
        <div class="webgl_infoP1"><img src="TemplateData/webgl_infologo.png" /></div>
        <div class="webgl_infoP2 webgl_infoP2Text"></div>
        <div class="webgl_infoP3">实验正在加载，请耐心等待...</div>
        <div class="webgl_infoP4"><img src="TemplateData/webgl_infoname.png" /></div>
      </div>
      <div class="webgl_footer">
        <div class="webgl_footlogo"></div>
        <div class="webgl_footuser"></div>
        <div class="webgl_footfullscreen" onclick="gameInstance.SetFullscreen(1)"></div>
        <div class="webgl_foottitle"></div>
      </div>
    </div>
  </body>
</html>
