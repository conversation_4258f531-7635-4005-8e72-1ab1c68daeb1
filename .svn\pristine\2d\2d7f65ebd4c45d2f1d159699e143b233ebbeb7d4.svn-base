<template>
  <div class="productRelease-details">
    <el-drawer :visible.sync="drawer" :wrapper-closable="false" direction="rtl" size="700px" @close="drawerClose">
      <template v-slot:title>
        <span class="header">{{ title }}</span>
      </template>
      <el-tabs v-model="activeName" @tab-click="tabClick">
        <el-tab-pane label="产品动态" name="Dynamic">
          <div class="timeLine">
            <div v-for="item in logList" :key="item.logId" class="lineItem">
              <span class="time">{{ item.createTime }}</span>
              <span class="user">{{ item.createUserName }}</span>
              <span class="content">{{ item.content }}</span>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="测试bug" name="Bug">
          <div ref="bugBoxRef" class="bugBox">
            <div v-for="(item, itemIndex) in bugList" :key="item.bugId" ref="bugItemRef" class="bugItem">
              <div class="bugShow">
                <el-avatar :src="item.headurl" shape="circle" :size="40" fit="cover" class="user-avatar" @error="true">
                  <img src="@/assets/login/logo.png" />
                </el-avatar>
                <div class="bugContent">
                  <div class="bugUserInfo">
                    <span class="userName">{{ item.createUserName }}</span>
                    <span class="createText">创建了</span>
                    <span class="bug">BUG</span>
                    <span class="bugName">{{ item.name }}</span>
                  </div>
                  <div class="bugMessage" @click="handleImageClick" v-html="item.message"></div>
                  <div class="bugOperate">
                    <span>{{ item.createTime }}</span>
                    <span @click="reply(itemIndex, item)">回复</span>
                  </div>
                  <div v-if="item.replyList.length" class="bugReplyList">
                    <div v-for="li in item.replyList" :key="li.replyId" class="bugReplyItem">
                      <el-avatar :src="li.targetUserHeadurl" shape="circle" :size="40" fit="cover" class="user-avatar" @error="true">
                        <img src="@/assets/login/logo.png" />
                      </el-avatar>
                      <div class="replyContent">
                        <span>{{ li.targetUserName }} <i>回复了: </i> {{ li.replyTargetName }}</span>
                        <div @click="handleImageClick" v-html="li.content"></div>
                        <div class="bugOperate">
                          <span>{{ li.createTime }}</span>
                          <span @click="reply(itemIndex, item, li)">回复</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="replyBugId === item.bugId" class="bugReply">
                <el-avatar :src="avatar" shape="circle" :size="40" fit="cover" class="user-avatar" @error="true">
                  <img src="@/assets/login/logo.png" />
                </el-avatar>
                <VueQuillEditor ref="contentQuill" :placeholder="`回复${replyUser}:`" @change="quillChange" />
                <div class="confimReply">
                  <el-button type="primary" plain size="small" @click="restReply">取消回复</el-button>

                  <el-button type="primary" size="small" @click="confimReply">确认回复</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane v-if="record_permission" label="查看记录" name="Record">
          <div class="record-box">
            <el-table :data="recordList" border style="width: 100%">
              <el-table-column align="center" prop="createUserName" label="操作人" width="width"> </el-table-column>
              <el-table-column align="center" prop="createTime" label="操作时间" width="width"> </el-table-column>
            </el-table>
          </div>
          <!-- 分页 -->
          <el-pagination
            layout="total, sizes, prev, pager, next"
            :page-sizes="[10, 15, 20, 30]"
            :total="total"
            :page-size.sync="queryInfo.pageSize"
            :current-page.sync="queryInfo.pageNum"
            style="text-align: center; margin-top: 15px"
            @size-change="getRecordList"
            @current-change="getRecordList"
          >
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <ElImageViewer v-if="isViewerVisible" :url-list="urlList" :on-close="viewerClose" :z-index="999999" />
  </div>
</template>
<script>
// 导入组件
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import VueQuillEditor from '@/components/vueQuillEditor'
import { releaseProductLogList, releaseProductBugList, releaseProductBugAddReply, releaseProduct_log_viewList } from '@/api/productRelease'
import { mapGetters } from 'vuex'
import { removeSpaces } from '@/utils'
export default {
  name: '',
  components: {
    ElImageViewer,
    VueQuillEditor
  },
  data() {
    return {
      drawer: false,
      title: null,
      productId: null,
      activeName: 'Dynamic',
      logList: [],
      bugList: [],
      // bug回复字段
      replyBugId: null,
      replyUser: null,
      replyContent: null,
      replyTargetId: null,
      isViewerVisible: false,
      urlList: [],
      initialIndex: 0,
      // 查看记录
      queryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      recordList: [],
      total: 0
    }
  },
  computed: {
    ...mapGetters(['avatar', 'userId', 'keyList']),
    record_permission() {
      return this.keyList.includes('productRelease_record')
    }
  },
  created() {},

  methods: {
    open(item) {
      this.drawer = true
      this.title = item.name
      this.productId = item.productId
      this.getLogList()
      this.getBugList()
    },
    tabClick(data) {
      if (data.name === 'Bug') {
        this.handleImageClick()
      }
      if (data.name === 'Record') {
        this.getRecordList()
      }
    },
    drawerClose() {
      this.activeName = 'Dynamic'
    },

    async getLogList() {
      const { data } = await releaseProductLogList({ pageNum: 1, pageSize: 2000, productId: this.productId })
      this.logList = data.list
    },
    async getBugList() {
      const { data } = await releaseProductBugList({ pageNum: 1, pageSize: 2000, productId: this.productId })
      this.bugList = data.list
    },
    async getRecordList() {
      const { data } = await releaseProduct_log_viewList({ ...this.queryInfo, productId: this.productId })
      this.recordList = data.list
      this.total = data.total
      console.log(data)
    },
    reply(itemIndex, item, replyData = null) {
      if (replyData) {
        this.replyUser = replyData.targetUserName
        this.replyTargetId = replyData.targetUserId
      } else {
        this.replyUser = item.createUserName
        this.replyTargetId = item.createUser
      }
      this.replyBugId = item.bugId
      //  页面自动定位到回复评论那
      this.$nextTick(() => {
        const target = this.$refs['bugItemRef'][itemIndex]
        const scrollBarBox = this.$refs['bugBoxRef']
        const targetRect = target.getBoundingClientRect()
        if (target) {
          scrollBarBox.scrollTo({
            top: target.offsetTop + targetRect.height - 400,
            behavior: 'smooth' // 平滑滚动
          })
        }
      })

      // console.log(target.getBoundingClientRect(), scrollBarBox.getBoundingClientRect())
    },
    quillChange(event) {
      this.replyContent = event.html
    },
    restReply() {
      this.replyBugId = null
      this.replyContent = null
    },
    confimReply() {
      if (!this.replyContent.trim() || removeSpaces(this.replyContent.trim()) === '<p></p>') {
        this.$message.warning('回复内容不能为空')
        return false
      }
      const info = {
        bugId: this.replyBugId,
        productId: this.productId,
        replyTargetId: this.replyTargetId,
        content: this.replyContent,
        targetUserId: this.userId
      }
      const loading = this.$loading({
        text: '保存中，请稍后...',
        background: 'rgba(0,0,0,0.7)'
      })
      releaseProductBugAddReply(info)
        .then(() => {
          loading.close()
          this.$message.success('添加回复成功!')
          this.replyBugId = null
          this.getBugList()
          this.getLogList()
        })
        .catch(() => {
          loading.close()
        })
      console.log(info)
    },
    viewerClose() {
      this.isViewerVisible = false
      this.urlList = []
    },
    handleImageClick(event) {
      if (event && event.target) {
        const target = event.target
        if (target.tagName === 'IMG') {
          // 获取所有图片的 URL
          const image = target
          this.urlList = [image.src]
          this.isViewerVisible = true
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.productRelease-details {
  ::v-deep {
    .el-drawer {
      .el-drawer__header {
        padding-bottom: 12px;
        margin-bottom: 10px;
        border-bottom: 1px solid #d9d9d9;
        .header {
          font-family: PingFang SC, PingFang SC;
          font-size: 20px;
          color: #333333;
        }
        .el-drawer__close-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0;
          width: 30px;
          height: 30px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d9d9d9;
          &:hover {
            background: #d9d9d9;
          }
          .el-dialog__close {
            font-size: 24px;
            color: #a3a3a3;
          }
        }
      }
      .el-tabs__item {
        padding-left: 30px;
      }
      .el-tabs__item.is-active {
        color: #3465df;
      }
      .el-tabs__item {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
      .el-tabs__active-bar {
        background: #3465df;
      }
      .timeLine {
        padding-left: 20px;

        .lineItem {
          position: relative;
          display: flex;
          // align-items: center;
          margin-bottom: 16px;
          &:last-of-type {
            &::before {
              display: none;
            }
          }
          &::after {
            content: '';
            position: absolute;
            left: 178px;
            top: 6px;
            width: 10px;
            height: 10px;
            background: #c4c4c4;
            border-radius: 10px;
          }
          &::before {
            content: '';
            position: absolute;
            left: 182px;
            top: 6px;
            width: 2px;
            height: calc(100% + 16px);
            background: #c4c4c4;
          }
          & > span {
            font-family: PingFang SC, PingFang SC;
            font-size: 18px;
          }
          .time {
            width: 170px;
            margin-right: 43px;
            color: #9096a7;
          }
          .user {
            width: 80px;
            margin-right: 18px;
            color: #333333;
          }
          .content {
            flex: 1;
            color: #3464e0;
          }
        }
      }
      .bugBox {
        padding: 30px 20px;
        height: 780px;
        overflow: auto;
        &::-webkit-scrollbar {
          width: 6px;
        }
        &::-webkit-scrollbar-thumb {
          width: 6px;
          background: #3464e0;
          border-radius: 6px;
        }
        .bugItem {
          padding-bottom: 20px;
          margin-bottom: 20px;
          border-bottom: 2px solid #cccccc;
          &:last-of-type {
            border-bottom: none;
          }
          .bugShow {
            display: flex;

            .avatar {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              object-fit: cover;
            }
            .bugContent {
              width: calc(100% - 40px);
              margin-left: 6px;
              .bugUserInfo {
                & > span {
                  margin-right: 6px;
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 500;
                  font-size: 18px;
                }
                .userName,
                .bug {
                  color: #333333;
                }
                .createText {
                  color: #9096a7;
                }
                .bugName {
                  color: #3465df;
                }
              }
              .bugMessage {
                width: 100%;
                // min-height: 118px;
                padding: 14px 12px;
                margin-top: 10px;
                background: #f5f6f6;
                border-radius: 10px 10px 10px 10px;

                font-size: 16px;
                color: #333333;
                img {
                  max-width: 100%;
                  cursor: pointer;
                }
              }
              .bugOperate {
                margin-top: 10px;
                & > span {
                  font-family: PingFang SC, PingFang SC;
                  font-weight: 500;
                  font-size: 14px;
                  color: #9096a7;
                }
                & > span:last-of-type {
                  margin-left: 20px;
                  &:hover {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    color: #3465df;
                    cursor: pointer;
                  }
                }
              }
              .bugReplyList {
                width: 100%;
                background: #f5f6f6;
                padding: 18px 14px;
                margin-top: 15px;
                border-radius: 10px 10px 10px 10px;
                .bugReplyItem {
                  display: flex;
                  margin-bottom: 15px;
                  padding-bottom: 10px;
                  border-bottom: 1px solid #c4c4c4;
                  &:last-of-type {
                    margin-bottom: 0;
                    padding-bottom: 0;
                    border-bottom: none;
                  }
                  .replyContent {
                    flex: 1;
                    margin-left: 7px;
                    & > span:first-of-type {
                      margin-right: 10px;
                      font-family: PingFang SC, PingFang SC;
                      font-weight: 500;
                      font-size: 18px;
                      color: #333333;
                      line-height: 18px;
                      i {
                        font-style: normal;
                        color: #9096a7;
                      }
                    }
                    div {
                      margin-top: 5px;
                      font-size: 16px;
                      color: #333333;
                      img {
                        max-width: 100%;
                        cursor: pointer;
                      }
                    }
                  }
                }
              }
            }
          }
          .bugReply {
            display: flex;
            padding-left: 40px;
            margin-top: 15px;
            flex-wrap: wrap;
            .vueQuillEditor {
              flex: 1;
              margin-left: 10px;
              height: 350px;
              .myQuillEditor {
                height: 100%;
              }
              .ql-container {
                height: calc(100% - 125px);
              }
              .ql-blank,
              .ql-editor {
                max-height: 350px !important;
                min-height: 200px !important;
              }
            }
            .confimReply {
              display: flex;
              justify-content: flex-end;
              width: 100%;
            }
          }
        }
      }
      .record-box {
        height: 730px;
        padding: 0 20px;
        overflow: auto;
      }
    }
  }
}
</style>
