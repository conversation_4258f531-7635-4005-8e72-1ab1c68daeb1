/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分'
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result
  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp
    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        console.log(args)

        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}
export function formatJson(headers, rows) {
  return rows.map((item) => {
    return Object.keys(headers).map((key) => {
      if (item[headers[key]] === 1) {
        return '待解决'
      } else if (item[headers[key]] === 2) {
        return '解决中'
      } else if (item[headers[key]] === 3) {
        return '已解决'
      } else if (item[headers[key]] === 4) {
        return '已关闭'
      } else {
        return item[headers[key]]
      }
    })
  })
}

export function downUrl(fileName, url) {
  return new Promise((res) => {
    const x = new XMLHttpRequest()
    x.open('GET', url, true)
    x.responseType = 'blob'
    x.onload = function (e) {
      const blob = x.response
      if ('msSaveOrOpenBlob' in navigator) {
        // IE导出
        window.navigator.msSaveOrOpenBlob(blob, fileName)
      } else {
        const a = document.createElement('a')
        a.download = fileName
        a.href = URL.createObjectURL(blob)
        document.querySelector('body').appendChild(a)
        a.click()
        document.querySelector('body').removeChild(a)
      }
      res()
    }
    x.send()
  })
}

export function debounce1(fn, time) {
  let timer = null
  let isFirstCall = true // 添加一个标志来判断是否是第一次调用
  return function (...args) {
    const _this = this
    console.log(isFirstCall)

    if (isFirstCall) {
      fn.apply(_this, args) // 如果是第一次调用，则立即执行
      isFirstCall = false // 更新标志状态
    } else {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        fn.apply(_this, args)
        isFirstCall = true
      }, time)
    }
  }
}
export function debounce2(func, wait, immediate) {
  let timeout, result

  const later = (context, args) => {
    const last = +new Date() - context.timestamp

    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last, context, args)
    } else {
      timeout = null
      if (!immediate) {
        result = func.apply(context.context, args)
        if (!timeout) context.args = null
      }
    }
  }

  return function (...args) {
    const context = {
      timestamp: +new Date(),
      context: this,
      args
    }

    const callNow = immediate && !timeout

    if (!timeout) {
      timeout = setTimeout(later, wait, context, args)
    }
    if (callNow) {
      result = func.apply(this, args)
      context.args = null
    }

    return result
  }
}
// 清除所有空格
export function removeSpaces(str) {
  return str.replace(/\s+/g, '')
}
