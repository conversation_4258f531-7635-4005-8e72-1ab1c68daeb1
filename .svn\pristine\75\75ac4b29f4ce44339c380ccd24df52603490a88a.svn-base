<template>
  <div class="virtual">
    <template v-if="webglUrl && version">
      <iframe v-if="version === 'Unity2018'" ref="iframe" :src="webglUrl" allowfullscreen frameborder="0" class="iframe" @load="iframeLoad"></iframe>
      <iframe v-else ref="iframe" :src="newWebglUrl" allowfullscreen frameborder="0" class="iframe" @load="iframeLoad"></iframe>

      <!-- <div class="info">
        <img class="logo2" src="@/assets/virtual/logo2.png" alt="" />
        <span>当前用户: {{ realName }}</span>
        <img class="fullscreen" src="@/assets/virtual/fullscreen.png" alt="" @click="fullScreen" />
      </div> -->
    </template>
    <div class="feedback" :style="{ width: !feedbackDrawer ? '0%' : '100vw' }" @click="close">
      <div :class="[{ openDrawer: type && feedbackDrawer }, 'feedbackButton']">
        <div :class="[{ currentChecked: type === 1 }, 'defaultStatus']" @click.stop="openDrawer(1)">
          <i class="el-icon-edit"></i>
          <span>实验反馈</span>
        </div>
        <div :class="[{ currentChecked: type === 2 }, 'defaultStatus']" @click.stop="openDrawer(2)">
          <i class="el-icon-time"></i>
          <span>反馈记录</span>
        </div>
      </div>
      <transition name="flexible">
        <div v-if="feedbackDrawer" class="feedbackBox" @click.stop>
          <div class="feedbackBox_header">
            <span>{{ feedbackTitle }}</span>
          </div>
          <div class="feedbackBox_body" :style="{ paddingLeft: type === 1 ? '26px' : '18px' }">
            <!-- 实验反馈 -->
            <template v-if="type === 1">
              <VueQuillEditor ref="contentQuill" :folder="'emulation'" @change="remark = $event.html" />
              <el-row type="flex" justify="end" style="margin-top: 24px">
                <el-button type="primary" @click="confirmFeedback">确认反馈</el-button>
              </el-row>
            </template>
            <!-- 反馈记录 -->
            <template v-else>
              <ul class="feedbackList">
                <li v-for="item in feedbackList" :key="item.feedbackId">
                  <div>
                    <span>{{ item.createTime }}</span>
                    <span style="margin-left: 15px">{{ item.realName }}</span>
                    <!-- <span v-if="item.isOneself" style="color: red; cursor: pointer" @click="remove(item)">删除</span> -->
                  </div>
                  <div v-html="item.remark"></div>
                </li>
              </ul>
            </template>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { emulationDetail, emulationAddFeedback, emulationFeedbackList, emulationFeedbackRemove } from '@/api/emulation'
import { mapGetters } from 'vuex'
import VueQuillEditor from '@/components/vueQuillEditor'
export default {
  name: '',
  components: {
    VueQuillEditor
  },
  data() {
    return {
      // webglUrl: './webgl/index.html',
      webglUrl: window.config.VUE_APP_WEBGLURL + 'index.html',
      // newWebglUrl: window.config.VUE_APP_WEBGLURL + 'newData.html',
      newWebglUrl: './webgl/newData.html',
      fileDatas: null,
      feedbackDrawer: false,
      remark: null,
      type: null, // 1添加反馈 2反馈列表
      feedbackList: [],
      iframe: null,
      info: null
    }
  },
  computed: {
    ...mapGetters(['realName', 'userId']),
    version() {
      return this.$route.params.version
    },
    feedbackTitle() {
      return this.type === 1 ? '实验反馈' : '反馈记录'
    }
  },

  mounted() {
    window.addEventListener('message', function (e) {
      if (e.data === 'close') {
        window.close()
      }
    })
  },
  methods: {
    async iframeLoad() {
      const { data } = await emulationDetail({ id: this.$route.params.id })
      this.fileDatas = data.testReqs
      if (data.version === 'Unity2018') {
        const jsonData = data.testReqs.find((item) => {
          return item.fileName.split('.')[item.fileName.split('.').length - 1] === 'json'
        })
        this.info = {
          emulationUrl: jsonData.fileUrl,
          userId: this.userId,
          emulationId: this.$route.params.id
        }
        // this.$refs['iframe'].contentWindow.postMessage(JSON.stringify(this.info), this.webglUrl)
        this.$refs['iframe'].contentWindow.postMessage(JSON.stringify(this.info), '*')
      } else {
        const relativePath = this.fileDatas
          .find((item) => {
            if (item.fileUrl.includes('/Build')) {
              return item
            }
          })
          .fileUrl.split('/Build')[0]

        const info = {
          directoryUrl: relativePath + '/',
          loaderUrl: this.getUrl('.loader.js'),
          config: {
            dataUrl: this.getUrl('.data'),
            frameworkUrl: this.getUrl('.framework.js'),
            codeUrl: this.getUrl('.wasm')
          }
        }

        this.$refs['iframe'].contentWindow.postMessage(JSON.stringify(info), '*')
      }
    },
    getUrl(target) {
      const targetData = this.fileDatas.find((item) => {
        return item.fileName.includes(target)
      })
      return targetData.fileUrl
    },
    fullScreen() {
      if (this.webglUrl) {
        this.$refs['iframe'].contentWindow.postMessage('fullScreen', this.webglUrl)
        // this.iframe.fullScreen()
      }
    },
    async openDrawer(type) {
      this.type = type
      if (this.type === 2) {
        const { data } = await emulationFeedbackList({ emulationId: this.$route.params.id })
        this.feedbackList = data
      }
      this.feedbackDrawer = true
    },
    async confirmFeedback() {
      await emulationAddFeedback({ emulationId: this.$route.params.id, remark: this.remark })
      this.$message.success('反馈成功')
      this.close()
    },
    close() {
      this.feedbackDrawer = false
      this.type = null
      this.remark = null
    },
    // 删除反馈
    remove(item) {
      console.log(item)
      this.$confirm('确定要删除该反馈吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await emulationFeedbackRemove({ id: item.feedbackId })
          const { data } = await emulationFeedbackList({ emulationId: this.$route.params.id })
          this.feedbackList = data
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.virtual {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .iframe {
    width: 100%;
    // height: calc(100% - 64px);
    height: 100%;
    // min-height: 868px;
  }
  .info {
    display: flex;
    align-items: center;
    height: 64px;
    padding-left: 232px;
    .logo2 {
      width: 202px;
      height: 34px;
    }
    span {
      margin-left: 38px;
      padding-top: 2px;
      font-size: 13px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .fullscreen {
      margin-left: 40px;
      cursor: pointer;
    }
  }

  .feedback {
    position: absolute;
    right: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    .feedbackButton {
      position: absolute;
      right: 0;
      top: 236px;
      transform: translateX(85px);
      .defaultStatus {
        display: flex;
        align-items: center;
        width: 140px;
        height: 56px;
        padding-left: 16px;
        background: #2d3337;
        cursor: pointer;
        // transform: translateX(85px);
        i {
          font-size: 24px;
          color: #b1bac7;
        }
        &:last-of-type {
          margin-top: 16px;
        }
        span {
          margin-left: 20px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .currentChecked {
        width: 140px;
        background: #3464e0;
        transform: translateX(-85px);
        transition: all 0.3s;
        span {
          margin-left: 10px;
        }
      }
    }
    .openDrawer {
      right: 832px;
      transition: all 0.3s;
    }
    .feedbackBox {
      position: absolute;
      right: 0;
      top: 0;
      width: 832px;
      height: 100%;
      background: #fff;
      box-shadow: 0 0 8px 1px #616060;
      .feedbackBox_header {
        padding: 32px 26px;
        margin-bottom: 0;
        span {
          font-size: 18px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
      }
      .feedbackBox_body {
        padding: 0 24px 0 26px;
        height: calc(100% - 85px);
        overflow: auto;

        ::v-deep {
          .el-textarea {
            .el-textarea__inner {
              width: 382px;
              height: 268px;
              background: #f5f5f5;
              border: 1px solid #d4d4d4;
              border-radius: 10px;
            }
          }
        }

        .feedbackList {
          border-left: 1px solid #eeeeef;
          li {
            position: relative;
            padding-left: 11px;
            margin-bottom: 28px;
            & > div:first-of-type {
              &::before {
                content: '';
                position: absolute;
                left: -4px;
                top: 3px;
                width: 8px;
                height: 8px;
                background: #b1bac7;
                border-radius: 8px;
              }
              span {
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #a3a8bb;
              }
              & > span:last-of-type {
                margin-left: 14px;
              }
            }
            & > div:last-of-type {
              padding: 16px;
              margin-top: 6px;
              width: 774px;
              background: #eef1f3;
              border-radius: 6px;
              font-size: 15px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
              line-height: 23px;
              ::v-deep {
                img {
                  max-width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
  .flexible-enter-active {
    animation: slideInRight 0.3s;
  }
  .flexible-leave-active {
    animation: slideOutRight 0.3s;
  }
}
</style>
