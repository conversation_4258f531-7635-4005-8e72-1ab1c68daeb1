<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="登录日志" name="log">
        <!-- 日志 -->
        <!-- 搜索查询 -->
        <el-row style="min-width: 1200px; margin-bottom: 20px">
          <el-col :span="24">
            <el-form ref="form" :model="logInfo" label-width="80px" inline>
              <el-form-item label="用户名:" label-width="60px">
                <el-input v-model="logInfo.userName" size="small" placeholder="用户名" clearable></el-input>
              </el-form-item>
              <el-form-item label="开始时间:">
                <el-date-picker v-model="stateTime" size="small" align="right" :picker-options="pickerOptions0" placeholder="选择日期"> </el-date-picker>
              </el-form-item>
              <el-form-item label="结束时间:">
                <el-date-picker v-model="endTime" size="small" align="right" :picker-options="pickerOptions0" placeholder="选择日期"> </el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button type="success" size="small" @click="serachClick">查询</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-table :data="logList" style="width: 100%" border>
          <el-table-column align="center" label="序号" width="width" type="index"> </el-table-column>
          <el-table-column align="center" prop="username" label="用户名" width="width"> </el-table-column>
          <el-table-column align="center" prop="loginTime" label="操作时间" width="width">
            <template v-slot="{ row }">
              <span>{{ row.loginTime | formatDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="remark" label="操作内容" width="width"> </el-table-column>
          <el-table-column align="center" prop="ipAddress" label="ip地址" width="width"> </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 15, 20, 30]"
          :total="total"
          :page-size.sync="logInfo.pageSize"
          :current-page.sync="logInfo.pageNum"
          style="text-align: center; margin-top: 15px"
          @size-change="getLogList"
          @current-change="getLogList"
        >
        </el-pagination>
      </el-tab-pane>
      <el-tab-pane label="操作日志" name="modeLog"><modeLog /></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getLogList } from '@/api/log.js'
import { formatDate } from '@/filters'
import modeLog from '@/views/log/modeLog'
export default {
  name: 'Log',
  components: {
    modeLog
  },
  data() {
    return {
      logInfo: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        startDate: null,
        endDate: null
      },
      stateTime: null,
      endTime: null,
      logList: [],
      total: 0,
      activeName: 'log',
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e6
        }
      }
    }
  },
  created() {
    this.getLogList()
  },
  methods: {
    async getLogList() {
      const { data } = await getLogList(this.logInfo)
      this.logList = data.list
      this.total = data.total
    },
    serachClick() {
      this.stateTime ? (this.logInfo.startDate = formatDate(this.stateTime, 'yyyy-MM-dd')) : (this.logInfo.startDate = null)
      this.endTime ? (this.logInfo.endDate = formatDate(this.endTime, 'yyyy-MM-dd')) : (this.logInfo.endDate = null)
      this.getLogList()
      // const time = new Date(this.logInfo.startDate)
      // const currentData = new Date()
      // if (time.getTime() < currentData.getTime()) {
      // } else {
      //   this.$message.warning('您选择的开始时间不能是今天以后的时间')
      // }
      console.log(this.time)
      console.log(this.logInfo)
    }
  }
}
</script>

<style scoped lang="sass"></style>
