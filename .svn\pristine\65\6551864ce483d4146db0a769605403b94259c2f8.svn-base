<template>
  <div class="app-container">
    <el-form ref="searchForm" class="searchForm" :model="queryInfo" label-width="100px" inline>
      <el-form-item label="合同名称:">
        <el-input v-model="queryInfo.contractName" size="small" placeholder="请输入合同名称" class="name" maxlength="50" clearable></el-input>
      </el-form-item>
      <el-form-item label="合同编号:">
        <el-input v-model="queryInfo.contractCode" size="small" placeholder="请输入合同编号" maxlength="50" clearable></el-input>
      </el-form-item>

      <el-form-item label="客户名称:">
        <el-input v-model="queryInfo.customerName" size="small" placeholder="请输入客户名称" maxlength="50" clearable></el-input>
      </el-form-item>
      <el-form-item label="销售员:">
        <el-input v-model="queryInfo.realName" size="small" placeholder="请输入销售员名称" maxlength="50" clearable></el-input>
      </el-form-item>
      <el-form-item label="质保到期时间:">
        <el-date-picker
          v-model="time"
          size="small"
          type="daterange"
          range-separator="→"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          class="dateTime"
          @change="datePickerChange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item style="margin-left: 15px">
        <el-button size="small" type="primary" @click="getList">查询</el-button>
        <el-button size="small" type="primary" plain @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="tableBox">
      <el-row type="flex" justify="end">
        <el-button v-if="keyList.includes('maintenanceInformat_look')" type="primary" size="small" @click="openAfterSaleList">售后维护信息</el-button>
        <el-button v-if="keyList.includes('batchAmendUser')" type="primary" size="small" @click="updateSellUser">批量修改销售负责人</el-button>
        <el-button v-if="keyList.includes('contractBase')" type="primary" icon="el-icon-plus" size="small" @click="crateContract">创建合同</el-button>
      </el-row>
      <el-table
        ref="elTableRef"
        v-loading="loading"
        :data="list"
        style="width: 100%"
        border
        header-cell-class-name="tableHeaderCell"
        cell-class-name="tableCell"
        element-loading-text="拼命加载中"
        @sort-change="listSortChange"
      >
        <el-table-column align="center" label="序号" width="80" type="index"> </el-table-column>
        <el-table-column align="center" label="合同名称" width="width" prop="contractName" class-name="contractName"> </el-table-column>
        <el-table-column align="center" label="合同编号" width="width" prop="contractCode"> </el-table-column>
        <el-table-column align="center" label="客户名称" width="width" prop="customerName"> </el-table-column>
        <el-table-column align="center" label="销售员" width="100" prop="headUserName"> </el-table-column>
        <el-table-column align="center" label="货款(万元)" width="120" prop="money" :formatter="formatterRemainMoney" sortable="custom"></el-table-column>
        <el-table-column align="center" label="质保日期" width="110" prop="expireTime" sortable="custom"></el-table-column>
        <el-table-column align="center" label="剩余货款(万元)" width="width" prop="remainMoney" :formatter="formatterRemainMoney">
          <template v-slot="{ row }">
            <span>{{ row.remainMoney ? parseFloat(row.remainMoney / 10000).toFixed(4) : '暂无' }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" label="剩余质保金(万元)" width="width" prop="remainQuality" :formatter="formatterRemainMoney"> </el-table-column>
        <el-table-column align="center" label="合同状态" width="width" prop="type">
          <template v-slot="{ row }">
            <section class="contractType" :style="{ background: row.type == 1 ? '#12b257' : '#999999' }">{{ row.type == 1 ? '进行中' : '已关闭' }}</section>
          </template>
        </el-table-column>
        <el-table-column align="center" label="施工时间" width="width" prop="constructionTime"> </el-table-column>
        <el-table-column align="center" label="操作" :width="keyList.includes('contractBase') ? 280 : 150">
          <template v-slot="{ row }">
            <div class="operateBox">
              <section @click="openDetails(row)">
                <img src="@/assets/performance/details_list.png" alt="" />
                <span>详情</span>
              </section>
              <section v-if="keyList.includes('contractBase') && row.type == 1" @click="edit(row)">
                <img src="@/assets/meeting/edit.png" alt="" />
                <span>编辑</span>
              </section>
              <section v-if="keyList.includes('contractBase') && row.type == 1" @click="del(row)">
                <img src="@/assets/meeting/del.png" alt="" />
                <span>删除</span>
              </section>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="list.length > 0"
        layout="total,sizes,prev, pager, next"
        style="margin-top: 40px; text-align: center"
        :page-sizes="[6, 12, 18, 24]"
        background
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @current-change="getList"
        @size-change="getList"
      />
    </div>

    <!-- 创建合同 -->
    <CrateContract ref="CrateContract" :show-dialog.sync="crateContractDialog" @addSuccess="getList" />
    <!--  批量修改销售负责人 -->
    <UpdateUser ref="UpdateUserRef" />
    <!-- 售后信息列表 -->
    <AfterSaleList ref="AfterSaleListRef" />
  </div>
</template>
<script>
import { contractList, contractDetail, contractRemove } from '@/api/contractNew'
import CrateContract from '@/views/contractNew/components/CrateContract'
import UpdateUser from './components/UpdateUser.vue'
import AfterSaleList from './components/AfterSaleList.vue'
import { mapGetters } from 'vuex'
export default {
  name: '',
  components: {
    CrateContract,
    UpdateUser,
    AfterSaleList
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === 'contractNew_details' && to.name === 'newContract') {
      to.meta.keepAlive = true
      next((vm) => {
        vm.getList()
      })
    }
    next()
  },
  data() {
    return {
      loading: false,
      queryInfo: {
        contractName: null, // 合同名称
        contractCode: null, // 合同编号
        customerName: null, // 客户名称
        realName: null, // 销售员
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        sort: null, // 1 货款
        orderby: null, // 1 倒叙 2 正序
        pageNum: 1,
        pageSize: 6
      },
      time: null,
      list: [],
      total: 0,
      crateContractDialog: false,
      isJump: true // 是否能跳转详情，用于控制鼠标点击过快导致页面丢失问题
    }
  },
  computed: {
    ...mapGetters(['keyList'])
  },
  created() {
    this.getList()
  },

  methods: {
    async getList() {
      this.loading = true
      const { data } = await contractList(this.queryInfo)
      this.list = data.list
      this.total = data.total
      this.loading = false
      this.isJump = true
    },
    listSortChange({ prop, order }) {
      if (!order) {
        this.queryInfo.sort = null
        this.queryInfo.orderby = null
      } else {
        this.queryInfo.sort = prop === 'money' ? 1 : 2
        this.queryInfo.orderby = order === 'ascending' ? 2 : 1
        this.queryInfo.pageNum = 1
      }
      this.getList()
    },

    reset() {
      this.queryInfo = {
        contractName: null, // 合同名称
        contractCode: null, // 合同编号
        customerName: null, // 客户名称
        realName: null, // 销售员
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        pageNum: 1,
        pageSize: 6
      }
      this.time = null
      this.getList()
    },
    datePickerChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
    },
    formatterRemainMoney(row, column, cellValue, index) {
      if (!cellValue) return 0
      const formattedValue = cellValue / 10000

      // 检查小数点后的位数
      const decimalPlaces = formattedValue.toString().split('.')[1]?.length || 0

      // 如果小数点后的位数超过6位，则固定为6位，否则直接返回原值
      return parseFloat(decimalPlaces > 6 ? formattedValue.toFixed(6) : formattedValue)
    },
    updateSellUser() {
      this.$refs['UpdateUserRef'].openDialog()
    },
    crateContract() {
      this.crateContractDialog = true
    },
    edit(row) {
      contractDetail({ id: row.contractId }).then((res) => {
        this.$refs['CrateContract'].showEditData(res.data)
        this.crateContractDialog = true
      })
    },
    del(row) {
      this.$confirm('确定要删除该合同吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await contractRemove({ id: row.contractId })
          this.getList()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    openDetails(row) {
      console.log(this.isJump)

      if (this.isJump) {
        this.isJump = false
        this.$router.push(`newContract/details/${row.contractId}`)
      }
    },
    openAfterSaleList(row) {
      this.$refs['AfterSaleListRef'].openDialog()
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #e8eaed;
  .tableBox {
    width: 100%;
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 20px;
    padding-bottom: 40px;
    background: #fff;
    border-radius: 8px;
    .el-table {
      margin-top: 36px;
      ::v-deep {
        .tableHeaderCell {
          padding: 0;
          height: 50px;
          background: #d5dff1;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: bold;
          font-size: 14px;
          color: #0b1a44;
        }
        .tableCell {
          padding: 0;
          height: 67px;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #0b1a44;
        }
        .el-table__row:hover {
          .tableCell {
            background: #f5f5f5;
          }
        }
        .contractName {
          font-weight: bold;
          color: #0b1a44;
        }
      }
      .contractType {
        width: 64px;
        height: 24px;
        margin-left: auto;
        margin-right: auto;
        border-radius: 2px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #ffffff;
      }
      .operateBox {
        display: flex;
        align-items: center;
        justify-content: center;
        section {
          display: flex;
          align-items: center;
          margin-left: 15px;
          cursor: pointer;
          &:first-of-type {
            margin-left: 0;
            &:hover {
              span {
                color: #3464e0;
              }
            }
          }
          &:nth-of-type(2):hover {
            span {
              color: #ff7e26;
            }
          }
          &:last-of-type:hover {
            span {
              color: #eb6557;
            }
          }
          span {
            font-family: Microsoft YaHei, Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 22px;
          }
        }
      }
    }
  }
}
::v-deep {
  .searchForm {
    position: relative;
    width: 100%;
    padding-left: 74px;
    padding-right: 275px;
    .el-form-item__label {
      font-size: 14px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-input__inner {
      width: 220px;
      height: 36px;
      background: #ffffff;
      border: 1px solid #d8dbe1;

      color: #657081;
      &::placeholder {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #b1bac7;
      }
    }
    .name {
      .el-input__inner {
        width: 284px !important;
      }
    }
    .dateTime {
      width: 284px !important;
      padding-top: 0;
      padding-bottom: 0;
      .el-range-separator {
        line-height: 34px;
      }
      .el-range__icon {
        line-height: 34px;
        color: #a3a8bb;
      }
    }

    .el-range-input {
      background: #fff;
    }
  }
}
</style>
