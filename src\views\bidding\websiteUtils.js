// #region 网页内容获取相关依赖 - 2025-09-12
// 网站内容提取规则配置
const SITE_EXTRACTION_RULES = {
  qianlima: {
    selector: '#detailContentHtml, #detailInfoContainer, .title-module',
    description: '千里马招标网',
    name: 'qianlima'
  },
  // 可以继续添加其他网站的规则
  // 示例：其他招标网站
  ccgp: {
    selector: '.vF_detail_content_container, .vF_detail_content',
    description: '中国政府采购网'
  },
  ggzy: {
    selector: '.detail_con, .detail-content, .content',
    description: '公共资源交易网站内容'
  },
  default: {
    selector: '.vF_detail_main, .content, .detail, .main-content, article, .post-content, .entry-content',
    description: '默认内容选择器'
  }
}

// 根据URL判断网站类型并获取对应的提取规则
function getSiteExtractionRule(url) {
  try {
    const hostname = new URL(url).hostname.toLowerCase()
    // 检查是否包含特定网站标识
    for (const [siteKey, rule] of Object.entries(SITE_EXTRACTION_RULES)) {
      if (siteKey !== 'default' && hostname.includes(siteKey)) {
        console.log(`检测到${rule.description}，使用选择器: ${rule.selector}`)
        return rule
      }
    }

    // 返回默认规则
    console.log('使用默认内容选择器')
    return SITE_EXTRACTION_RULES.default
  } catch (error) {
    console.error('URL解析失败:', error)
    return SITE_EXTRACTION_RULES.default
  }
}

// 简单的HTML解析函数，支持智能内容提取
// function parseHtmlContent(html, url) {
//   // 提取title
//   const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i)
//   const title = titleMatch ? titleMatch[1].trim() : ''

//   // 根据网站获取提取规则
//   const rule = getSiteExtractionRule(url)

//   // 尝试提取特定内容
//   let specificContent = null
//   const selectors = rule.selector.split(',').map((s) => s.trim())

//   for (const selector of selectors) {
//     let regex
//     if (selector.startsWith('#')) {
//       // ID选择器
//       const id = selector.substring(1)
//       regex = new RegExp(`<[^>]*id=['""]${id}['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
//     } else if (selector.startsWith('.')) {
//       // Class选择器
//       const className = selector.substring(1)
//       regex = new RegExp(`<[^>]*class=['""][^'""]*${className}[^'""]*['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
//     } else {
//       // 标签选择器
//       regex = new RegExp(`<${selector}[^>]*>([\\s\\S]*?)<\/${selector}>`, 'i')
//     }

//     const match = html.match(regex)
//     if (match && match[1]) {
//       specificContent = match[1].trim()
//       console.log(`成功使用选择器 ${selector} 提取到内容`)
//       break
//     }
//   }

//   // 如果没有找到特定内容，提取纯文本作为备选
//   const fullTextContent = html
//     .replace(/<script[\s\S]*?<\/script>/gi, '')
//     .replace(/<style[\s\S]*?<\/style>/gi, '')
//     .replace(/<[^>]*>/g, '')
//     .replace(/\s+/g, ' ')
//     .trim()

//   return {
//     title,
//     html,
//     specificContent,
//     textContent: specificContent || fullTextContent, // 优先返回特定内容
//     extractionRule: rule.description,
//     extractedSelector: specificContent
//       ? selectors.find((s) => {
//           // 找到成功的选择器
//           let regex
//           if (s.startsWith('#')) {
//             const id = s.substring(1)
//             regex = new RegExp(`<[^>]*id=['""]${id}['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
//           } else if (s.startsWith('.')) {
//             const className = s.substring(1)
//             regex = new RegExp(`<[^>]*class=['""][^'""]*${className}[^'""]*['""][^>]*>([\\s\\S]*?)<\/[^>]*>`, 'i')
//           } else {
//             regex = new RegExp(`<${s}[^>]*>([\\s\\S]*?)<\/${s}>`, 'i')
//           }
//           return html.match(regex)
//         })
//       : '无匹配'
//   }
// }
// #endregion

export default {
  getSiteExtractionRule
}
