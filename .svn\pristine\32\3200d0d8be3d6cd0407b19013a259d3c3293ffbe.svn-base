<template>
  <div class="agent">
    <el-dialog custom-class="agent-dialog" title="AI助手" center :visible.sync="dialogVisible" top="67px" :show-close="false" width="width">
      <div ref="contentViewRef" class="content-view">
        <div v-if="!invoiceList.length" class="empty-view">
          <img class="feifei-logo" src="@/assets/agent/feifei_icon.png" alt="" />
          <span>HI！我是飞飞AI</span>
        </div>
        <!-- 发票 -->
        <div class="invoice-view">
          <div class="user-invoice">
            <ul class="file-list">
              <li v-for="item in invoiceList" :key="item.name">
                <img src="@/assets/agent/pdf_icon.png" alt="" />
                <div class="item-info">
                  <el-tooltip effect="dark" :content="item.name" placement="top">
                    <p>{{ item.name }}</p>
                  </el-tooltip>
                  <p>{{ formattingFileSize(item.size) }}</p>
                </div>
              </li>
            </ul>
          </div>
          <div v-if="excelData.length" class="agent-answer">
            <div class="agent-answer_tip">
              <img src="@/assets/agent/finish_icon.png" alt="" />
              <span>解析完成</span>
            </div>
            <div class="agent-answer_excel">
              <img src="@/assets/agent/excel_icon.png" alt="" />
              <span>电子发票交接表.xlsx</span>
              <el-button plain class="download-button" @click="exportExcel">下载</el-button>
            </div>
          </div>
        </div>
        <div v-show="loading" class="loading-view">
          <Loading />
          <span>正在解析</span>
        </div>
      </div>
      <div class="feature-view">
        <div class="agent-modules">
          <span v-for="(item, index) in moduleList" :key="index" class="module-item" :class="{ active: activeModule === item }">{{ item }}</span>
        </div>
        <div class="upload-box">
          <div class="file-box">
            <div v-show="showLeftArrow" class="arrow direction_left" @click="togglePage('last')"><i class="el-icon-arrow-left"></i></div>
            <div class="file-cut">
              <ul class="file-list horizontal" :style="{ transform: fileHorizontalTransformStyle }">
                <li v-for="item in fileList" :key="item.name">
                  <img src="@/assets/agent/pdf_icon.png" alt="" />
                  <div class="item-info">
                    <el-tooltip effect="dark" :content="item.name" placement="top">
                      <p>{{ item.name }}</p>
                    </el-tooltip>
                    <p>{{ formattingFileSize(item.size) }}</p>
                  </div>
                  <i class="el-icon-error" @click="removeFile(item)"></i>
                </li>
              </ul>
            </div>
            <div v-show="showRightArrow" class="arrow direction_right" @click="togglePage('next')"><i class="el-icon-arrow-right"></i></div>
          </div>

          <el-upload
            ref="uploadRef"
            multiple
            accept=".pdf,.png,.jpg"
            :limit="8"
            :disabled="fileList.length >= 8 || loading"
            :show-file-list="false"
            :auto-upload="false"
            action="#"
            :file-list="fileList"
            :on-exceed="handleExceed"
            :on-change="handleChange"
          >
            <span slot="trigger" class="upload_button" :class="{ disabled: fileList.length >= 8 }">
              <img src="@/assets/agent/upload_icon.png" alt="" />
              上传文件
            </span>
            <img class="send-icon" :class="{ disabled: loading }" src="@/assets/agent/send_icon.png" alt="" @click="sendData" />
          </el-upload>
        </div>
      </div>

      <!-- <el-button type="primary" @click="exportExcel">导出</el-button> -->
    </el-dialog>
  </div>
</template>
<script>
import Loading from '@/components/Animation/Loading'
import { put } from '@/utils/oss'
import { formattingFileSize } from '@/filters'
import { ai_invoice_extract } from '@/api/agent'
export default {
  name: '',
  components: {
    Loading
  },
  data() {
    return {
      dialogVisible: false,
      moduleList: ['发票助手', '文档检索'],
      activeModule: '发票助手',
      fileList: [],
      filePageNum: 1,
      invoiceList: [],
      excelData: [],
      loading: false
    }
  },
  computed: {
    fileHorizontalTransformStyle() {
      return `translateX(-${(this.filePageNum - 1) * 510}px)`
    },
    showLeftArrow() {
      return this.filePageNum > 1 && this.fileList.length > 2
    },
    showRightArrow() {
      return this.fileList.length > 2 && this.fileList.length / 2 > this.filePageNum
    }
  },
  created() {},

  methods: {
    formattingFileSize,

    handleExceed() {
      this.$message.warning('最多只能上传8个文件')
    },
    handleChange(file) {
      console.log(file)

      // 限制大小<10Mb
      if (file.size > 1024 * 1024 * 10) {
        this.$message.error('文件大小不能超过10Mb')
        return false
      }
      // 如果文件是图片，需要进行oss上传

      if (file.raw.type.includes('image')) {
        put(`newMeeting/${file.name}`, file.raw).then((res) => {
          console.log(res)

          file.ossUrl = res.url
          // this.fileList.push(file)
        })
      }
      const findData = this.fileList.find((item) => item.name === file.name)
      if (!findData || findData.name !== file.name) {
        this.fileList.push(file)
      }

      return true
    },
    togglePage(type) {
      if (type === 'next') {
        this.filePageNum++
      } else {
        this.filePageNum--
      }
    },
    removeFile(item) {
      this.fileList = this.fileList.filter((file) => file.uid !== item.uid)
      if (this.filePageNum > 1) {
        if (this.fileList.length % 2 === 0 && this.filePageNum * 2 > this.fileList.length) {
          this.filePageNum--
        }
      }
    },
    async sendData() {
      if (this.loading) return ''
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择文件')
        return
      }

      this.restData()
      this.loading = true
      this.invoiceList = JSON.parse(JSON.stringify(this.fileList))

      try {
        // 创建 FormData 对象
        const formData = new FormData()

        // 将所有文件添加到 FormData
        const allOSS = this.fileList.filter((item) => item.ossUrl).map((item) => item.ossUrl)
        if (allOSS) {
          formData.append('urls', allOSS.join(','))
        }

        this.fileList.forEach((file, index) => {
          if (!file.ossUrl) {
            formData.append('files', file.raw || file)
          }
        })

        // 清空文件列表
        this.fileList = []
        // 调用 API 接口
        const response = await ai_invoice_extract(formData)

        // 处理返回的数据
        if (response && response.data) {
          // 这里可以根据实际返回的数据结构来处理结果
          this.excelData = response.data
          if (this.excelData.length === 0) {
            this.$message.warning('不是发票文件')
            return
          }
          this.scrollToBottom()
        }
      } catch (error) {
        console.error('上传失败:', error)
        this.$message.error('文件上传失败，请重试')
        this.invoiceList = []
      } finally {
        this.loading = false
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const content = this.$refs['contentViewRef']
        content.scrollTop = content.scrollHeight
      })
    },
    restData() {
      this.invoiceList = []
      this.excelData = []
      this.filePageNum = 1
    },
    exportExcel() {
      const headers = {
        num: '序号',
        type: '发票类型',
        invoiceNum: '发票号码',
        date: '开票日期',
        servicesName: '货物、劳务或服务名称',
        saleName: '销售方',
        saleNum: '销售方纳税人识别号',
        price: '价款',
        tax: '税额',
        countNum: '价税合计',
        userName: '移交人'
      }
      const res = this.formatJson(headers, this.excelData)
      import('@/vendor/Export2Excel').then((excel) => {
        excel.export_json_to_excel({
          header: Object.values(headers), // 表头 必填
          data: res, // 具体数据 必填
          filename: '电子发票交接表' // 非必填
        })
      })
    },
    formatJson(headers, rows) {
      return rows.map((item) => {
        return Object.keys(headers).map((key) => {
          return item[key]
        })
      })
    }
  }
}
</script>
<style lang="scss">
.agent-dialog {
  position: relative;
  width: 620px;
  height: 800px;
  margin-right: 213px;
  background: url('~@/assets/agent/agent_bg.png') no-repeat;
  background-size: cover;
  border-radius: 30px;

  .el-dialog__header {
    padding: 0;
    padding-top: 25px;
    padding-bottom: 20px;
    .el-dialog__title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 28px;
      color: #333333;
    }
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 70px);
  }
  .el-dialog__footer {
    display: none;
  }
  .content-view {
    max-height: 510px;
    overflow: auto;
    padding: 0 40px;
    &::-webkit-scrollbar {
      background: transparent;
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      width: 6px;
      background: #7368fd;
      border-radius: 10px;
    }
    .empty-view {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 54px;
      .feifei-logo {
        margin-right: 18px;
        width: 101px;
        height: 130px;
      }
      span {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 30px;
        color: #333333;
      }
    }
    .invoice-view {
      width: 100%;
      .agent-answer {
        margin-top: 34px;
        .agent-answer_tip {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 120px;
          height: 40px;
          margin-bottom: 10px;
          background: #eaf3ff;
          border-radius: 8px 8px 8px 8px;

          img {
            width: 24px;
            height: 24px;
          }
          span {
            margin-left: 4px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 16px;
            background: linear-gradient(0deg, #486afe 0%, #bb7cff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .agent-answer_excel {
          position: relative;
          display: flex;
          align-items: center;
          width: 359px;
          height: 76px;
          padding-left: 12px;
          background: #fcfcfc;
          border-radius: 14px 14px 14px 14px;
          border: 1px solid #e1e1e1;
          img {
            width: 40px;
            height: 40px;
          }
          & > span {
            margin-left: 10px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #000000;
          }
          .download-button {
            position: absolute;
            right: 23px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 68px;
            height: 36px;
            padding: 0;
            background: #ffffff;
            border-radius: 8px 8px 8px 8px;
            border: 1px solid #e6e6e6;

            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #000000;
          }
        }
      }
      .user-invoice {
        width: 264px;
        margin-left: auto;
        .file-list {
          display: block;
          li {
            margin-bottom: 10px;
            margin-right: 0;
          }
        }
      }
    }
    .loading-view {
      position: absolute;
      bottom: 231px;
      left: 33px;
      display: flex;
      align-items: center;

      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      span {
        margin-left: 12px;
      }
    }
  }
  .feature-view {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    .agent-modules {
      display: flex;
      align-items: center;
      .module-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 40px;
        margin-right: 20px;
        background: #efefef;
        border-radius: 80px 80px 80px 80px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #bdbdbd;
      }
      .module-item.active {
        color: #2093fe;
        background: #ffffff;
        cursor: pointer;
      }
    }
    .upload-box {
      position: relative;
      width: 560px;
      height: 130px;
      margin-top: 10px;
      padding: 20px 0 89px;
      background: #ffffff;
      border-radius: 20px 20px 20px 20px;
      border: 1px solid #dfdfdf;

      .file-box {
        position: relative;
        width: 100%;
        height: 56px;

        .file-cut {
          width: calc(100% - 50px);
          margin: 0 auto;
          overflow: auto;
          &::-webkit-scrollbar {
            display: none;
          }
          .file-list.horizontal {
            max-width: 800%;
            flex-wrap: nowrap;
            transition: all 0.3s ease;

            // mask-image: linear-gradient(180deg, transparent 5px, #000 20px, #000 calc(100% - 20px), transparent calc(100% - 5px));

            li {
              margin-right: 6px;
            }
          }
        }
        .arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
          justify-content: center;
          width: 14px;
          height: 18px;
          background: #f3f5f8;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #e2e2e2;
          color: #55575b;
          font-size: 12px;
          cursor: pointer;
          z-index: 999;
        }
        .arrow.direction_left {
          left: 6px;
        }
        .arrow.direction_right {
          right: 6px;
        }
      }
      .upload_button {
        position: absolute;
        left: 16px;
        bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 90px;
        height: 30px;
        background: #f5f5f5;
        border-radius: 14px 14px 14px 14px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #000000;
        img {
          margin-right: 3px;
          width: 20px;
          height: 20px;
        }
      }
      .upload_button.disabled {
        background: #f5f5f5;
        color: #999999;
        cursor: not-allowed;
      }
      .send-icon {
        position: absolute;
        right: 14px;
        bottom: 11px;
        width: 32px;
        height: 32px;
        cursor: pointer;
      }
      .send-icon.disabled {
        cursor: not-allowed;
      }
    }
  }
}
</style>

<style lang="scss">
// 通用的样式
.agent {
  .file-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    li {
      flex-shrink: 0;
      position: relative;
      display: flex;
      align-items: center;
      width: 250px;
      height: 56px;
      margin-bottom: 4px;
      background: #fcfcfc;
      border-radius: 14px 14px 14px 14px;
      border: 1px solid #e1e1e1;
      &:nth-of-type(even) {
        margin-right: 0;
      }

      img {
        width: 30px;
        height: 30px;
        margin-right: 5px;
        margin-left: 10px;
      }
      .item-info {
        & > p:first-of-type {
          max-width: 175px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 13px;
          color: #000000;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        & > p:last-of-type {
          margin-top: 4px;
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
        }
      }
      .el-icon-error {
        position: absolute;
        top: 3px;
        right: 5px;
        font-size: 18px;
        color: #333333;
        cursor: pointer;
      }
    }
  }
}
</style>
