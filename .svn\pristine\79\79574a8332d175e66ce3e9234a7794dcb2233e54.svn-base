<template>
  <div class="">
    <el-dialog :visible="showDialog" width="597px" center custom-class="sendBackDialog">
      <el-input v-model="remark" type="textarea" placeholder="请输入退回原因" maxlength="150" resize="none"></el-input>
      <div class="sendBackDialog_footer">
        <div @click="close">关闭</div>
        <div @click="submit">提交</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { tAchievementReturnAchievement } from '@/api/tAchievement'
export default {
  name: '',
  props: {
    showDialog: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      achievementId: null,
      remark: null
    }
  },
  methods: {
    close() {
      this.$emit('update:showDialog', false)
      this.remark = null
    },
    submit() {
      const loading = this.$loading({
        text: '正在提交中，请稍后',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      tAchievementReturnAchievement({ achievementId: this.achievementId, remark: this.remark })
        .then(() => {
          loading.close()
          this.$message.success('退回成功!')
          this.$emit('update:showDialog', false)
          this.$router.push('/performance')
        })
        .catch(() => {
          loading.close()
        })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .sendBackDialog {
    border-radius: 4px;
    overflow: hidden;
    box-shadow: none;
    background: none;
    .el-dialog__header {
      height: 57px;
      background: url('~@/assets/performance/sendBack_header.png') no-repeat;
      background-size: cover;
      .el-dialog__close {
        color: #fff;
      }
    }
    .el-dialog__body {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 326px;
      padding: 0;
      padding-top: 24px;
      background: #f5f5f5;
      .el-textarea {
        width: 532px;
        height: 216px;
        .el-textarea__inner {
          width: 532px;
          height: 216px;
          background: #fff;
          padding: 16px;
          padding-top: 13px;
          border: 1px solid #d8dbe1;
          border-radius: 10px;
          &::placeholder {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #868b9f;
          }
        }
      }
      .sendBackDialog_footer {
        display: flex;
        margin-bottom: 14px;
        div {
          width: 114px;
          height: 38px;
          line-height: 38px;
          margin-top: 24px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;
        }
        & > div:first-of-type {
          margin-right: 32px;
          background: #fff;
          color: #868b9f;
          &:hover {
            background: #f2f4ff;
          }
        }
        & > div:last-of-type {
          background: #3464e0;
          color: #fff;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
