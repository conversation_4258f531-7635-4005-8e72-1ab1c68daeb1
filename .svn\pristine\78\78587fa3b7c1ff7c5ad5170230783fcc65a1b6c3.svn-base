// 培训管理
import request from '@/utils/request'
/** 列表 */
export function trainList(params) {
  return request({
    url: '/train/trainList',
    method: 'GET',
    params
  })
}
/** 添加培训 */
export function trainSave(data) {
  return request({
    url: '/train/save',
    method: 'POST',
    data
  })
}
/** 修改培训 */
export function trainUdpate(data) {
  return request({
    url: '/train/udpate',
    method: 'POST',
    data
  })
}

/** 报名 */
export function trainSignUp(params) {
  return request({
    url: '/train/signUp',
    method: 'GET',
    params
  })
}
/** 人员签到 */
export function trainSign(params) {
  return request({
    url: '/train/sign',
    method: 'GET',
    params
  })
}
/** 详情 */
export function trainDetails(params) {
  return request({
    url: '/train/details',
    method: 'GET',
    params
  })
}
/** 评分 */
export function trainGrade(params) {
  return request({
    url: '/train/grade',
    method: 'GET',
    params
  })
}
/** 反馈 */
export function trainFeedback(params) {
  return request({
    url: '/train/feedback',
    method: 'GET',
    params
  })
}
/** 更新状态 */
export function trainpdateStatus(params) {
  return request({
    url: '/train/updateStatus',
    method: 'GET',
    params
  })
}
