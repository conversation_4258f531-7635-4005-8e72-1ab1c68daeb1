<template>
  <div class="date-time-selector">
    <!-- 选中的日期 -->
    <div class="checkedDate">{{ checkedDate }}</div>
    <!-- 日期选择器 -->
    <div class="date-selector">
      <div class="arrow-btn left" @click="changeWeek(-1)">
        <i class="el-icon-arrow-left"></i>
      </div>

      <div class="week-container">
        <div v-for="(day, index) in currentWeek" :key="index" class="day-item" :class="{ active: day.date === checkedDate }" @click="handleDayClick(day.date)">
          <div class="week-day">{{ day.weekDay }}</div>
          <div class="day-number">{{ day.day }}</div>
        </div>
      </div>

      <div class="arrow-btn right" @click="changeWeek(1)">
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>

    <!-- 时间轴 -->
    <div class="time-axis">
      <div class="time-label">
        <svg-icon icon-class="time"></svg-icon>
        <span>时间</span>
      </div>

      <div class="time-slots">
        <div v-for="time in timeSlots" :key="time" class="time-slot">
          {{ time }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DateTimeSelector',
  props: {
    currentWeek: {
      type: Array,
      required: true
    },
    checkedDate: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      timeSlots: ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00']
    }
  },
  methods: {
    changeWeek(direction) {
      this.$emit('week-change', direction)
    },
    handleDayClick(date) {
      this.$emit('date-click', date)
    }
  }
}
</script>

<style scoped lang="scss">
.checkedDate {
  margin-bottom: 15px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  text-align: center;
}
.date-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;

  .arrow-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border-radius: 8px;
    color: #55575b;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background: linear-gradient(352deg, #afc5fe 0%, #d8e6ff 100%);
      box-shadow: 1px 1px 10px 0px rgba(51, 51, 51, 0.08);
    }

    i {
      font-size: 16px;
      color: #666;
    }
  }

  .week-container {
    display: flex;
    margin: 0 20px;
    gap: 8px;
  }

  .day-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 70px;
    height: 60px;
    padding-top: 10px;
    background: linear-gradient(352deg, #ffffff 0%, #eaf2ff 100%);
    border: 1px solid #ffffff;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 1px 1px 10px 0px rgba(51, 51, 51, 0.08);
    overflow: hidden;

    &.active {
      background: linear-gradient(180deg, #b3c9ff 0%, #3465df 100%);
      .day-number,
      .week-day {
        color: #fff;
      }
    }

    &:hover:not(.active) {
      background: #f0f0f0;
    }

    .week-day {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #666666;
    }

    .day-number {
      margin-top: 4px;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 16px;
      color: #333333;
    }
  }
}

.time-axis {
  display: flex;
  align-items: center;
  height: 70px;
  padding-left: 37px;
  background: #ffffff;
  border-radius: 14px 14px 14px 14px;
  border: 1px solid #e9e9e9;
  .time-label {
    display: flex;
    align-items: center;
    margin-right: 78px;

    .svg-icon {
      color: #f2c513;
      font-size: 34px;
    }
    span {
      margin-left: 39px;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 22px;
      color: #333333;
    }
  }

  .time-slots {
    display: flex;
    flex: 1;
  }

  .time-slot {
    width: 60px;
    height: 40px;
    margin-right: 74px;
    line-height: 40px;
    background: #f3f5f8;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e2e2e2;

    font-family: PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    text-align: center;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
