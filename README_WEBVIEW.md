# WebView一体化预览功能说明

## 功能概述

新的WebView一体化预览功能完全在当前页面内实现，无需打开额外窗口。

## 使用步骤

### 1. 加载网页
- 输入网址
- 点击"🌐 加载预览"按钮
- 等待网页在下方预览区域加载完成

### 2. 浏览和登录
- 在预览区域中正常浏览网站
- 如需登录，直接在预览区域中完成登录操作
- 登录状态会自动保持

### 3. 检查登录状态
- 点击"🔍 检查登录状态"按钮
- 系统会检测当前登录状态并显示结果

### 4. 提取内容
- 点击"📄 提取内容"按钮
- 系统会智能提取页面内容
- 提取结果显示在下方分析区域

## 主要优势

- ✅ **单页面操作**：无需切换多个窗口
- ✅ **登录状态保持**：一次登录，持续有效
- ✅ **智能内容提取**：支持多种网站类型
- ✅ **实时状态检测**：准确检测登录状态

## 支持的网站

- 千里马网站
- 政府采购网
- 公共资源交易网站
- 其他招标平台
- 通用网站内容

## 问题修复

### 已修复的问题

1. **登录状态检测不准确**
   - 改进了Cookie检测逻辑
   - 增加了DOM元素检测
   - 添加了页面文本检测

2. **首次加载卡住**
   - 优化了webview事件监听
   - 改进了初始化流程
   - 增加了错误处理

## 技术实现

- 使用Electron的webview标签
- 通过`partition="persist:main"`实现session共享
- JavaScript注入实现内容提取和状态检测
- Vue.js响应式状态管理

## 注意事项

- 确保网络连接正常
- 某些网站可能有反爬虫机制
- 复杂的单页应用可能需要等待加载完成
- 登录状态检测基于常见模式，特殊网站可能需要手动确认
