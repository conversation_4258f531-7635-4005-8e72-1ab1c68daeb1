# WebView 一体化预览功能说明

## 功能概述

新的 WebView 一体化预览功能完全在当前页面内实现，无需打开额外窗口。特别针对千里马网站进行了优化。

## 使用步骤

### 1. 加载网页

- 输入网址
- 点击"🌐 加载预览"按钮
- 等待网页在下方预览区域加载完成

### 2. 浏览和登录（千里马网站）

- 在预览区域中正常浏览网站
- 如需登录，直接在预览区域中完成登录操作
- 登录状态会自动保持，支持 session 共享

### 3. 检查登录状态

- 点击"🔍 检查登录状态"按钮
- 系统会智能检测当前登录状态并显示结果
- 千里马网站：检测`qlm_username`、`xAuthToken`等特定 Cookie
- 其他网站：检测通用登录标识

### 4. 提取内容

- 点击"📄 提取内容"按钮
- 系统会根据网站类型智能提取页面内容
- 提取结果显示在下方分析区域

## 网站类型支持

### 🎯 千里马网站（特殊优化）

- **登录检测**：基于`qlm_username`、`xAuthToken`、`userInfo`等 Cookie
- **内容提取**：专用选择器`#detailContentHtml`、`.vF_detail_content_container`等
- **登录提醒**：未登录时会提示内容可能不完整

### 🌐 通用网站

- **登录检测**：基于常见 Cookie 和 DOM 元素
- **内容提取**：通用选择器`.content`、`article`、`.main-content`等
- **无需登录**：大多数网站可直接提取内容

## 主要优势

- ✅ **单页面操作**：无需切换多个窗口
- ✅ **智能网站识别**：自动识别千里马网站并应用专用逻辑
- ✅ **精确登录检测**：基于实际 Cookie 值而非存在性
- ✅ **登录状态保持**：一次登录，持续有效
- ✅ **智能内容提取**：根据网站类型选择最佳提取策略

## 千里马网站 Cookie 说明

### 登录状态 Cookie

- `qlm_username`: 用户名
- `xAuthToken`: 认证令牌
- `userInfo`: 用户详细信息（JSON 格式）
- `login_time`: 登录时间

### 检测逻辑

```javascript
// 检测千里马登录状态
const isLoggedIn = cookies.includes('qlm_username=') && cookies.includes('xAuthToken=') && !cookies.includes('qlm_username=;')
```

## 问题修复

### ✅ 已修复的问题

1. **登录状态检测不准确**

   - 使用千里马专用 Cookie 检测
   - 检查 Cookie 实际值而非仅存在性
   - 区分千里马网站和通用网站

2. **webview 事件监听器错误**

   - 修复`removeAllListeners`不存在的问题
   - 使用正确的`removeEventListener`方法
   - 手动管理事件处理器

3. **重复加载卡住**
   - 添加 URL 变化检测
   - 改进事件监听器绑定机制
   - 增强错误处理

## 技术实现

- **Electron webview 标签**：嵌入式浏览器
- **Session 共享**：`partition="persist:main"`
- **JavaScript 注入**：动态检测和提取
- **网站类型识别**：URL 模式匹配
- **Vue.js 状态管理**：响应式 UI 更新

## 使用建议

### 千里马网站

1. 先加载任意千里马页面
2. 在预览区域中登录
3. 检查登录状态确认成功
4. 访问需要的具体页面进行内容提取

### 其他网站

1. 直接加载目标页面
2. 无需登录即可提取内容
3. 如有登录需求，在预览区域中完成

## 注意事项

- 千里马网站需要登录才能获取完整内容
- 登录状态在应用重启后会保持
- 某些网站可能有反爬虫机制
- 复杂的单页应用需要等待完全加载
