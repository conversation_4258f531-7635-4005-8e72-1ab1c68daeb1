<template>
  <div class="vueQuillEditor">
    <quill-editor ref="myQuillEditor" placeholder="请输入内容" class="myQuillEditor ql-editor" :options="editorOption" @ready="onEditorReady" @change="onEditorChange($event)" />
    <el-dialog title="上传视频" :visible.sync="videoFlag" width="500px" append-to-body>
      <div style="width: 360px; margin: 0 auto">
        <el-upload
          class="upload-demo"
          drag
          :action="action"
          :headers="header"
          :file-list="videoFileList"
          :on-remove="videoUploadRemove"
          :before-upload="videoBeforeUpload"
          :on-success="videoUploadSuccess"
          :on-progress="videoUploadProgress"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip" style="color: red; line-height: 15px">只能上传.mp4格式的附件，且大小不超过5000MB</div>
        </el-upload>
        <div class="quillUploadVideo">
          <el-input v-model.number="width" size="small" placeholder="宽度" style="margin-right: 40px" oninput="value=value.replace(/[^\d]/g,'')"></el-input>
          <el-input v-model.number="height" size="small" placeholder="高度" oninput="value=value.replace(/[^\d]/g,'')"></el-input>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="videoFlag = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddVideo">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadFile } from '@/api/systemUser'
import { Quill, quillEditor } from 'vue-quill-editor'
import Delta from 'quill-delta' // 添加这行
// 自定义字体大小
const Size = Quill.import('attributors/style/size')
Size.whitelist = ['10px', '12px', '16px', '18px', '20px', '30px', '32px']
Quill.register(Size, true)
// 自定义字体类型
var fonts = ['SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial', 'sans-serif']
var Font = Quill.import('formats/font')
Font.whitelist = fonts
Quill.register(Font, true)
// 修改上传视频
import Video from '@/utils/video'
Quill.register(Video, true)

/* 富文本编辑图片上传配置*/
const uploadConfig = {
  action: window.config.VUE_APP_BASE_API + '/system/upload/file', // 必填参数 图片上传地址
  methods: 'post', // 必填参数 图片上传方式
  name: 'file', // 必填参数 文件的参数名
  folder: 'common',
  accept: 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon' // 可选 可上传的图片格式
}
export default {
  name: 'VueQuillEditor',

  components: {
    quillEditor
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入内容'
    },
    folder: {
      type: String,
      default: 'common'
    }
  },

  data() {
    return {
      editorOption: {
        placeholder: this.placeholder,
        modules: {
          toolbar: {
            container: [
              ['bold', 'italic', 'underline', 'strike'], // 加粗，斜体，下划线，删除线
              ['blockquote', 'code-block'], // 引用，代码块
              [{ header: 1 }, { header: 2 }], // 几级标题
              [{ list: 'ordered' }, { list: 'bullet' }], // 有序列表，无序列表
              [{ script: 'sub' }, { script: 'super' }], // 下角标，上角标
              [{ indent: '-1' }, { indent: '+1' }], // 缩进
              [{ direction: 'rtl' }], // 文字输入方向
              [{ size: ['10px', '12px', false, '16px', '18px', '20px', '30px', '32px'] }], // 字体大小
              [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
              [{ color: [] }, { background: [] }], // 颜色选择
              [{ font: [false, 'SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial', 'sans-serif'] }], // 字体
              [{ align: [] }], // 居中
              ['clean'], // 清除样式,
              ['image', 'video'] // 上传图片、上传视频
            ],
            handlers: {
              video: () => {
                // 覆盖默认的上传视频
                this.onVideo()
              },
              image: function image() {
                var self = this
                var fileInput = this.container.querySelector('input.ql-image[type=file]')
                if (fileInput === null) {
                  fileInput = document.createElement('input')
                  fileInput.setAttribute('type', 'file')
                  fileInput.setAttribute('accept', uploadConfig.accept)
                  fileInput.classList.add('ql-image')
                  this.container.appendChild(fileInput)
                }
                // 每次点击时重置 value,解决重复上传同一文件不触发 change 事件的问题
                fileInput.value = ''
                fileInput.onchange = function () {
                  if (fileInput.files.length > 0) {
                    var formData = new FormData()
                    formData.append(uploadConfig.name, fileInput.files[0])
                    formData.append('fileName', `${uploadConfig.folder}/${new Date().getTime()}`)

                    uploadFile(formData).then(({ data }) => {
                      const length = self.quill.getSelection(true).index
                      self.quill.insertEmbed(length, 'image', data[0])
                      self.quill.setSelection(length + 1)
                    })
                  }
                }

                fileInput.click()
              }
            }
          },
          history: {
            delay: 1000,
            maxStack: 50,
            userOnly: false
          },
          imageDrop: true,

          imageResize: {
            displayStyles: {
              backgroundColor: 'black',
              border: 'none',
              color: 'black'
            },
            modules: ['Resize', 'DisplaySize', 'Toolbar']
          },
          clipboard: {
            matchers: [
              [
                'IMG',
                (node, delta) => {
                  const image = node.getAttribute('src')
                  console.log(node)

                  if (image && image.startsWith('data:image')) {
                    // 将 base64 图片转换为文件
                    const base64Data = image.split(',')[1]
                    const binaryData = atob(base64Data)
                    const array = new Uint8Array(binaryData.length)
                    for (let i = 0; i < binaryData.length; i++) {
                      array[i] = binaryData.charCodeAt(i)
                    }
                    const file = new File([array], 'pasted-image.png', { type: 'image/png' })

                    // 创建 FormData 并上传
                    const formData = new FormData()
                    formData.append('file', file)
                    formData.append('fileName', `${uploadConfig.folder}/${new Date().getTime()}`)

                    // 上传图片
                    uploadFile(formData).then(({ data }) => {
                      const quill = this.$refs.myQuillEditor.quill
                      const range = quill.getSelection()
                      const index = range ? range.index : 0
                      quill.insertEmbed(index, 'image', data[0])
                    })

                    // 返回空 delta 以阻止默认的粘贴行为
                    return new Delta()
                  }
                  return delta
                }
              ]
            ]
          }
        }
      },
      // toolbar标题
      titleConfig: [
        { Choice: '.ql-insertMetric', title: '跳转配置' },
        { Choice: '.ql-bold', title: '加粗' },
        { Choice: '.ql-italic', title: '斜体' },
        { Choice: '.ql-underline', title: '下划线' },
        { Choice: '.ql-header', title: '段落格式' },
        { Choice: '.ql-strike', title: '删除线' },
        { Choice: '.ql-blockquote', title: '块引用' },
        { Choice: '.ql-code', title: '插入代码' },
        { Choice: '.ql-code-block', title: '插入代码段' },
        { Choice: '.ql-font', title: '字体' },
        { Choice: '.ql-size', title: '字体大小' },
        { Choice: '.ql-list[value="ordered"]', title: '编号列表' },
        { Choice: '.ql-list[value="bullet"]', title: '项目列表' },
        { Choice: '.ql-direction', title: '文本方向' },
        { Choice: '.ql-header[value="1"]', title: 'h1' },
        { Choice: '.ql-header[value="2"]', title: 'h2' },
        { Choice: '.ql-align', title: '对齐方式' },
        { Choice: '.ql-color', title: '字体颜色' },
        { Choice: '.ql-background', title: '背景颜色' },
        { Choice: '.ql-image', title: '图像' },
        { Choice: '.ql-video', title: '视频' },
        { Choice: '.ql-link', title: '添加链接' },
        { Choice: '.ql-formula', title: '插入公式' },
        { Choice: '.ql-clean', title: '清除字体格式' },
        { Choice: '.ql-script[value="sub"]', title: '下标' },
        { Choice: '.ql-script[value="super"]', title: '上标' },
        { Choice: '.ql-indent[value="-1"]', title: '向左缩进' },
        { Choice: '.ql-indent[value="+1"]', title: '向右缩进' },
        { Choice: '.ql-header .ql-picker-label', title: '标题大小' },
        { Choice: '.ql-header .ql-picker-item[data-value="1"]', title: '标题一' },
        { Choice: '.ql-header .ql-picker-item[data-value="2"]', title: '标题二' },
        { Choice: '.ql-header .ql-picker-item[data-value="3"]', title: '标题三' },
        { Choice: '.ql-header .ql-picker-item[data-value="4"]', title: '标题四' },
        { Choice: '.ql-header .ql-picker-item[data-value="5"]', title: '标题五' },
        { Choice: '.ql-header .ql-picker-item[data-value="6"]', title: '标题六' },
        { Choice: '.ql-header .ql-picker-item:last-child', title: '标准' },
        { Choice: '.ql-size .ql-picker-item[data-value="small"]', title: '小号' },
        { Choice: '.ql-size .ql-picker-item[data-value="large"]', title: '大号' },
        { Choice: '.ql-size .ql-picker-item[data-value="huge"]', title: '超大号' },
        { Choice: '.ql-size .ql-picker-item:nth-child(2)', title: '标准' },
        { Choice: '.ql-align .ql-picker-item:first-child', title: '居左对齐' },
        { Choice: '.ql-align .ql-picker-item[data-value="center"]', title: '居中对齐' },
        { Choice: '.ql-align .ql-picker-item[data-value="right"]', title: '居右对齐' },
        { Choice: '.ql-align .ql-picker-item[data-value="justify"]', title: '两端对齐' }
      ],
      videoFlag: false,
      action: window.config.VUE_APP_BASE_API + '/system/upload/file',
      header: {
        Authorization: null
      },
      videoFileList: [],
      TiLength: 0,
      indexVideo: 0,
      uploading: false,
      width: null,
      height: null
    }
  },
  watch: {
    folder: {
      handler(val) {
        uploadConfig.folder = val
      },
      immediate: true
    }
  },
  mounted() {
    this.initTitle()
    // this.$refs['myQuillEditor'].quill.container.style = `height:${this.quillHeight}`
  },
  methods: {
    onEditorReady(quill) {
      // 监听 drop 事件
      quill.root.addEventListener(
        'drop',
        (e) => {
          console.log(e)
          const dt = e.dataTransfer
          if (!dt || !dt.files || !dt.files.length) return
          // 判断是否拖拽的是图片文件
          const imageFiles = Array.from(dt.files).filter((file) => /^image\//.test(file.type))
          if (imageFiles.length > 0) {
            e.preventDefault() // 阻止默认图片插入
            e.stopPropagation()
            imageFiles.forEach((file) => {
              this.handleDropImage(file)
            })
          }
        },
        true
      )
    },
    handleDropImage(file) {
      // 这里写你的自定义逻辑，比如上传到服务器
      var formData = new FormData()
      formData.append(uploadConfig.name, file)
      formData.append('fileName', `${uploadConfig.folder}/${new Date().getTime()}`)
      uploadFile(formData).then(({ data }) => {
        const quill = this.$refs.myQuillEditor.quill
        const length = quill.getSelection(true).index
        quill.insertEmbed(length, 'image', data[0])
        quill.setSelection(length + 1)
      })
      console.log('自定义处理拖拽图片：', file)
      // 上传成功后，可以手动插入图片
    },
    onEditorChange(event) {
      event.quill.deleteText(10000, 1)
      this.$emit('change', event)
    },
    setContent(data) {
      document.querySelectorAll('.ql-editor')[1].innerHTML = data
    },
    clearContent() {
      document.querySelectorAll('.ql-editor')[1].innerHTML = '<p><br/></p>'
    },
    initTitle() {
      document.getElementsByClassName('quill-editor')[0].dataset.placeholder = ''
      for (const item of this.titleConfig) {
        const tip = document.querySelector('.quill-editor ' + item.Choice)
        if (!tip) continue
        tip.setAttribute('title', item.title)
      }
    },
    onVideo() {
      this.videoFlag = true
      // 当编辑器中没有输入文本时，这里获取到的 range 为 null   获取光标位置
      var range = this.$refs.myQuillEditor.quill.getSelection()
      if (range == null) {
        this.indexVideo = 0
      } else {
        this.indexVideo = range.index
      }
    },
    videoBeforeUpload(file) {
      this.header.Authorization = `Bearer ${this.$store.getters.token}`
      const isJPG = file.type === 'video/mp4'
      const isLt2M = file.size / 1024 / 1024 < 5000
      if (!isLt2M) {
        this.$message.error('上传附件大小不能超过 5000MB!')
      }
      if (!isJPG) {
        this.$message.warning('只能上传.mp4格式的附件')
      }
      return isJPG && isLt2M
    },
    videoUploadProgress(event, file, fileList) {
      this.uploading = true
    },
    videoUploadSuccess(response, file, fileList) {
      this.uploading = false
      this.videoFileList = fileList
    },
    videoUploadRemove(file, fileList) {
      this.videoFileList = fileList
    },
    confirmAddVideo() {
      if (this.uploading === false) {
        const videoPathList = this.videoFileList.map((item) => item.response.data[0])
        // 获取富文本
        const quill = this.$refs.myQuillEditor.quill // 在光标所在位置 插入视频
        videoPathList.forEach((item) => {
          quill.insertEmbed(this.indexVideo, 'simpleVideo', {
            url: item,
            controls: 'controls',
            width: this.width ? `${this.width}px` : `100%`,
            height: this.height ? `${this.height}px` : 'auto'
          })
          quill.setSelection(this.indexVideo + 1)
        })
        this.videoFlag = false
        this.videoFileList = []
        this.width = null
        this.height = null
        this.uploading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .disabled .el-upload--picture-card {
    display: none;
  }
  .myQuillEditor {
    height: 100% !important;
    padding: 0;
    padding-top: 0 !important;
    padding-left: 0 !important;
    line-height: normal;
  }

  .myQuillEditor {
    .ql-editor {
      min-height: calc(600px - 66px);
      overflow: auto;
    }
  }
  .quillUploadVideo {
    display: flex;
    margin-top: 30px;
    .el-input {
      width: 160px;
    }
    .el-input__inner {
      width: 100%;
      display: inline-block;
    }
  }
}
</style>
