<template>
  <div>
    <div class="markdown-renderer" v-html="renderedContent"></div>
    <div v-if="content && !isProcessing" class="markdown-actions">
      <el-button class="download" icon="el-icon-download" @click="exportToWord">导出Word</el-button>
    </div>
  </div>
</template>

<script>
import MarkdownIt from 'markdown-it'

import FileSaver from 'file-saver'
import htmlDocx from 'html-docx-js/dist/html-docx'
export default {
  name: 'MarkdownRenderer',
  props: {
    content: {
      type: String,
      default: ''
    },
    // 是否启用HTML标签
    html: {
      type: Boolean,
      default: true
    },
    // 是否启用链接
    linkify: {
      type: Boolean,
      default: true
    },
    // 是否启用排版优化
    typographer: {
      type: Boolean,
      default: true
    },
    // 会议主题
    conferenceTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      md: null
    }
  },
  computed: {
    isProcessing() {
      return this.content === '会议纪要正在解析中，请稍后'
    },
    renderedContent() {
      if (!this.content || !this.md) {
        return ''
      }
      try {
        return this.md.render(this.content)
      } catch (error) {
        console.error('Markdown渲染错误:', error)
        return this.content
      }
    }
  },
  created() {
    this.initMarkdown()
  },
  methods: {
    initMarkdown() {
      this.md = new MarkdownIt({
        html: this.html,
        linkify: this.linkify,
        typographer: this.typographer,
        breaks: true // 启用换行符转换
      })

      // 自定义渲染规则，优化会议纪要显示
      this.customizeRenderer()
    },

    customizeRenderer() {
      // 自定义表格渲染
      const defaultTableRender =
        this.md.renderer.rules.table_open ||
        function (tokens, idx, options, env, renderer) {
          return renderer.renderToken(tokens, idx, options)
        }

      this.md.renderer.rules.table_open = function (tokens, idx, options, env, renderer) {
        tokens[idx].attrSet('class', 'markdown-table')
        return defaultTableRender(tokens, idx, options, env, renderer)
      }

      // 自定义代码块渲染
      const defaultCodeBlockRender =
        this.md.renderer.rules.code_block ||
        function (tokens, idx, options, env, renderer) {
          return renderer.renderToken(tokens, idx, options)
        }

      this.md.renderer.rules.code_block = function (tokens, idx, options, env, renderer) {
        tokens[idx].attrSet('class', 'markdown-code-block')
        return defaultCodeBlockRender(tokens, idx, options, env, renderer)
      }
    },
    exportToWord() {
      const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <style>
          body { font-family: PingFang SC; line-height: 1.5; color: #333; }
          hr { border: none; border-top: 1px solid #eee; margin: 20px 0;}
          strong { font-weight: 600; color: #2c3e50; }
          em { font-style: italic; color: #666; }
          h1, h2, h3, h4, h5, h6 { margin: 16px 0 8px 0; font-weight: 600; font-family: PingFang SC; color: #2c3e50; }
          h1 { font-size: 24px; border-bottom: 2px solid #eee; padding-bottom: 8px;}
          h2 { font-size: 20px; border-bottom: 1px solid #eee; padding-bottom: 6px; }
          h3 { font-size: 18px; }
          h4 { font-size: 16px; }
          p { margin: 8px 0; text-align: justify;}
          ul,ol { margin: 8px 0; padding-left: 24px;}
          ol { list-style:none; }
          li { margin: 4px 0; }
          pre { background: #f5f5f5; padding: 10px; border-radius: 6px; }
          .markdown-table { width: 100%; border-collapse: collapse; margin: 16px 0; border: 1px solid #ddd; }
          .markdown-table th,.markdown-table td { border: 1px solid #ddd; padding: 8px 12px; text-align: left; }
          .markdown-table th { background-color: #f5f5f5; font-weight: 600;}
          .markdown-table tr:nth-child(even) { background-color: #f9f9f9; }
        </style>
      </head>
      <body>${this.renderedContent}</body>
    </html>
  `
      const blob = htmlDocx.asBlob(html)
      FileSaver.saveAs(blob, `${this.conferenceTitle}-会议纪要.docx`)
    }
  }
}
</script>

<style lang="scss">
.markdown-renderer {
  line-height: 1.6;
  color: #333;

  // 标题样式
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    color: #2c3e50;
  }

  h1 {
    font-size: 24px;
    border-bottom: 2px solid #eee;
    padding-bottom: 8px;
  }

  h2 {
    font-size: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 6px;
  }

  h3 {
    font-size: 18px;
  }

  h4 {
    font-size: 16px;
  }

  // 段落样式
  p {
    margin: 8px 0;
    text-align: justify;
  }

  // 列表样式
  ul,
  ol {
    margin: 8px 0;
    padding-left: 24px;
  }

  li {
    margin: 4px 0;
  }

  // 表格样式
  .markdown-table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    border: 1px solid #ddd;
  }

  .markdown-table th,
  .markdown-table td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
  }

  .markdown-table th {
    background-color: #f5f5f5;
    font-weight: 600;
  }

  .markdown-table tr:nth-child(even) {
    background-color: #f9f9f9;
  }

  // 代码样式
  code {
    background-color: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
  }

  .markdown-code-block {
    background-color: #f4f4f4;
    padding: 12px;
    border-radius: 6px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
  }

  // 引用样式
  blockquote {
    border-left: 4px solid #ddd;
    margin: 16px 0;
    padding: 8px 16px;
    background-color: #f9f9f9;
    color: #666;
  }

  // 链接样式
  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  // 分割线样式
  hr {
    border: none;
    border-top: 1px solid #eee;
    margin: 20px 0;
  }

  // 强调样式
  strong {
    font-weight: 600;
    color: #2c3e50;
  }

  em {
    font-style: italic;
    color: #666;
  }
}
.markdown-actions {
  width: 100%;
  padding-top: 15px;
  border-top: 1px solid #eaeced;
  text-align: right;

  .download {
    padding: 10px 15px;
    background: #f3f5f8;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e2e2e2;

    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
  }
}
</style>
