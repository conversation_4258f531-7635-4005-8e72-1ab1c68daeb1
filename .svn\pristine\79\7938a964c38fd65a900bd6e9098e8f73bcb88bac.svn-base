<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：<router-link to="/meeting">会议管理</router-link> /</span>
        <span>查看会议</span>
      </el-col>
    </el-row>
    <div class="box">
      <el-descriptions v-if="JSON.stringify(meetingDetails) !== `{}`" title="会议详情" :column="1" style="margin: 0 auto">
        <el-descriptions-item label="会议主题">
          <span v-if="meetingDetails.meetingName" class="meetingName">
            {{ meetingDetails.meetingName }}
          </span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="会议内容">
          <span v-if="meetingDetails.remark" class="universal">{{ meetingDetails.remark }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="会议状态">
          <span v-if="meetingDetails.meetingStatus" class="universal" style="color: #3464e0"> {{ meetingDetails.meetingStatus | meetingStatus }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="预定时间段">
          <span v-if="meetingDetails.startTime && meetingDetails.endTime" class="universal">{{ meetingDetails.startTime }} 至 {{ meetingDetails.endTime }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="预定人">
          <span v-if="meetingDetails.realName" class="universal">
            {{ meetingDetails.realName }}
          </span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="会议室">
          <span v-if="meetingDetails.meetingRoomName" :class="[{ first: meetingDetails.meetingRoomName === '第一会议室', second: meetingDetails.meetingRoomName === '第二会议室' }, 'meetingRoomName']">{{ meetingDetails.meetingRoomName }}</span>
          <span v-else class="noData">暂无</span>
        </el-descriptions-item>
        <el-descriptions-item label="参会人员">
          <div class="checkPerson">
            <div v-for="item in meetingDetails.userDtos" :key="item.userId">
              <div>
                <span>{{ item.realName }}</span>
                <img v-if="item.sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
                <img v-else src="@/assets/meeting/man.png" alt="" />
              </div>
              <span>{{ item.organizationName }}-{{ item.jobName }}</span>
              <p v-if="item.signTime">{{ item.signTime }} 签到</p>
              <p v-else style="color: red">没有签到</p>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="上传附件">
          <ul v-if="meetingDetails.fileDtos" class="fileList">
            <li v-for="item in meetingDetails.fileDtos" :key="item.fileId">
              <div>
                <div class="fileImg">
                  <img src="@/assets/meeting/file.png" alt="" />
                  <span v-if="item.fileSize">{{ item.fileSize }}KB</span>
                </div>
                <div>
                  <el-tooltip class="item" effect="dark" :content="item.fileName" placement="top">
                    <span>{{ item.fileName }}</span>
                  </el-tooltip>
                </div>
              </div>
              <div v-if="item.percentage && item.percentage !== 100">
                <el-progress :percentage="item.percentage"></el-progress>
              </div>
              <div>
                <span v-if="item.realName && item.createTime">{{ item.realName }}上传于{{ item.createTime }}</span>
                <i class="el-icon-download" @click="downloadFile(item)"></i>
              </div>
            </li>
          </ul>
        </el-descriptions-item>
      </el-descriptions>
      <el-row type="flex" justify="center" style="margin-top: 35px">
        <el-button type="primary" size="small" icon="el-icon-back" @click="$router.push('/meeting')">返回</el-button>
      </el-row>
    </div>
  </div>
</template>

<script>
import { meetingDetails } from '@/api/meeting.js'

export default {
  name: '',
  data() {
    return {
      meetingDetails: []
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    async getDetails() {
      const { data } = await meetingDetails({ meetingId: this.$route.params.meetingId, belongType: 1 })
      this.meetingDetails = data
    },
    // 下载文件
    downloadFile(row) {
      if (row.fileId) {
        window.open(row.fileUrl, '_blank')
      } else {
        window.open(row.response.data[0], '_blank')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  padding-right: 40px;
  padding-bottom: 30px;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
}
.top {
  position: absolute;
  top: 0px;
  width: 100%;
  background-color: #e8eaed;
  padding: 24px 0 16px 0;
  z-index: 999;
  .el-col {
    i {
      font-size: 14px;
      color: #657081;
    }
    span {
      &:first-of-type {
        margin: 0 5px;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #657081;
      }
      &:last-of-type {
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
    }
  }
}
.box {
  width: 1754px;
  min-height: 600px;
  background: #ffffff;
  border-radius: 8px;
  padding-bottom: 10px;
  opacity: 1;
  .noData {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    font-weight: 400;
    color: #b1bac7;
  }
  .meetingName {
    font-size: 14px;
    font-family: Microsoft YaHei-Bold, Microsoft YaHei;
    font-weight: bold;
    color: #0b1a44;
  }
  .universal {
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    color: #0b1a44;
  }
  .meetingRoomName {
    display: block;
    width: 128px;
    height: 94px;
    line-height: 94px;
    background: #565555;
    border-radius: 8px 8px 8px 8px;
    text-align: center;
    font-size: 14px;
    font-family: Microsoft YaHei-Regular, Microsoft YaHei;
    color: #ffffff;
  }
  // 会议室背景图片
  .first {
    background: url('../../../assets/meeting/firstMeeting.png') no-repeat;
    background-size: 100% 100%;
  }
  .second {
    background: url('../../../assets/meeting/secondMeeting.png') no-repeat;
    background-size: 100% 100%;
  }
  // 会议室背景图片 over

  .checkPerson {
    display: flex;
    flex-wrap: wrap;
    & > div {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;
      width: 174px;
      height: 76px;
      margin-right: 18px;
      margin-bottom: 18px;
      padding: 8px 14px;
      background: #f5f5f5;
      border-radius: 4px 4px 4px 4px;
      box-sizing: border-box;
      & > div {
        display: flex;
        align-items: center;
        span {
          padding: 0;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        img {
          margin-left: 5px;
          width: initial;
          height: initial;
        }
      }
      span {
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #a3a8bb;
        text-overflow: ellipsis; /*文字隐藏后添加省略号*/
        white-space: nowrap; /*强制不换行*/
      }
      p {
        font-size: 12px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        color: #868b9f;
      }
    }
  }
  .fileList {
    display: flex;
    li {
      margin-right: 32px;
      padding: 14px 10px 20px 10px;
      width: 268px;
      height: 124px;
      background: #f9f9f9;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #eeeeef;
      box-sizing: border-box;
      & > div {
        &:first-of-type {
          display: flex;
          justify-content: flex-start;
          .fileImg {
            display: flex;
            flex-direction: column;
            // justify-content: center;
            align-items: center;
            img {
              width: 46px;
              height: 38px;
            }
            span {
              font-size: 12px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #868b9f;
              line-height: initial;
            }
          }
          & > div {
            margin-left: 3px;
            line-height: 40px;
            &:last-of-type {
              span {
                display: inline-block;
                width: 160px;
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              i {
                margin-left: 5px;
                color: #ff7e26;
                font-size: 18px;
                cursor: pointer;
              }
            }
          }
        }
        &:last-of-type {
          margin-top: 18px;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
          line-height: initial;
          i {
            font-size: 18px;
            float: right;
            margin-left: 15px;
            cursor: pointer;
          }
          .el-icon-download:hover {
            color: #3464e0;
          }
          .el-icon-delete:hover {
            color: #eb6557;
          }
        }
      }
    }
  }
}
::v-deep {
  .el-descriptions__header {
    padding: 24px 0 10px 56px;
    border-bottom: 1px solid #eeeeef;
    .el-descriptions__title {
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
    }
  }
  .el-descriptions__body {
    padding-left: 72px;
    .el-descriptions-item {
      padding-bottom: 28px;
      line-height: initial;
      .el-descriptions-item__container {
        align-items: center;
        .el-descriptions-item__label {
          min-width: 75px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #868b9f;
        }
      }
    }
  }
}
</style>
