import request from '@/utils/request'
// 获取角色列表
export function getroleList(params) {
  return request({
    url: '/system/role/roleList',
    method: 'get',
    params
  })
}
// 删除角色
export function roleRemove(roleId) {
  return request({
    url: '/system/role/roleRemove',
    method: 'DELETE',
    params: {
      roleId
    }
  })
}
// 修改角色
export function roleUpdate(data) {
  return request({
    url: '/system/role/roleUpdate',
    method: 'POST',
    data
  })
}
// 分配角色
export function assignRoles(data) {
  return request({
    url: '/system/role/assignRoles',
    method: 'POST',
    data
  })
}
// 添加角色
export function roleSave(data) {
  return request({
    url: '/system/role/roleSave',
    method: 'POST',
    data
  })
}
export function roleAll(userId) {
  return request({
    url: '/system/role/roleAll',
    method: 'GET',
    params: {
      userId
    }
  })
}
// 获取当前用户的权限
export function findUserRoleConfiguration(token) {
  return request({
    url: '/system/role/findUserRoleConfiguration',
    method: 'GET',
    params: {
      token
    }
  })
}
// 给角色分配权限
export function saveRoleConfiguration(data) {
  return request({
    url: '/system/role/saveRoleConfiguration',
    method: 'post',
    data
  })
}
