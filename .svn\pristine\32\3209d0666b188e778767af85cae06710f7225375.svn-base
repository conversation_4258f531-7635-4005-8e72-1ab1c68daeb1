// 小微秘 日志管理
import request from '@/utils/request'
/** 添加日志管理 */
export function secretLog_saveLog(data) {
  return request({
    url: '/secret/log/saveLog',
    method: 'POST',
    data
  })
}
/** 修改日志管理 */
export function secretLog_updateLog(data) {
  return request({
    url: '/secret/log/updateLog',
    method: 'POST',
    data
  })
}

/** 日志管理列表 */
export function secretLog_logList(params) {
  return request({
    url: '/secret/log/logListDD',
    method: 'GET',
    params
  })
}

/** 日志管理统计 */
export function secretLog_logStatis(params) {
  return request({
    url: '/secret/log/logStatis',
    method: 'GET',
    params
  })
}

/** 查询昨日计划 */
export function secretLog_yesterdayPlan(time) {
  return request({
    url: '/secret/log/yesterdayPlan',
    method: 'GET',
    params: { time }
  })
}

/** 查询所有客户  @description customerId 客户id  customerName 客户名称 */
export function secretLog_allCustomer() {
  return request({
    url: '/secret/log/allCustomer',
    method: 'GET'
  })
}
/** 查询所有客户下专业  @description majorId 专业id  majorName 专业名称 */
export function secretLog_allMajorByCustomerId(customerId) {
  return request({
    url: '/secret/log/allMajorByCustomerId',
    method: 'GET',
    params: { customerId }
  })
}

/** 查询所有客户下专业对应客户联系人  @description contactsId 客户联系人id  contactName 客户联系人名称 */
export function secretLog_allContactsByCustomerIdAndMajorId(customerId, majorId) {
  return request({
    url: '/secret/log/allContactsByCustomerIdAndMajorId',
    method: 'GET',
    params: { customerId, majorId }
  })
}

/** 日志评论列表 */
export function secretLog_logCommentList(logId) {
  return request({
    url: '/secret/log/logCommentList',
    method: 'GET',
    params: { logId }
  })
}

/** 添加日志评论 */
export function secretLog_addComment(data) {
  return request({
    url: '/secret/log/addComment',
    method: 'POST',
    data
  })
}

/** 查询所有客户下所有专业所有客户联系人 */
export function secretLog_allCustomerAndMajorAndContacts() {
  return request({
    url: '/secret/log/allCustomerAndMajorAndContacts',
    method: 'GET'
  })
}

/** 日志管理昨日、今日、明日计划 */
export function secretLog_logPlanDetails(time, createUser) {
  return request({
    url: '/secret/log/logPlanDetail',
    method: 'GET',
    params: {
      time,
      createUser
    }
  })
}
