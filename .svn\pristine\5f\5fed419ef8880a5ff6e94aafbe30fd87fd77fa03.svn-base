// 小微秘 合同管理
import request from '@/utils/request'
/** 添加 */
export function contract_saveContract(data) {
  return request({
    url: '/secret/contract/saveContract',
    method: 'POST',
    data
  })
}
/** 修改 */
export function contract_updateContract(data) {
  return request({
    url: '/secret/contract/updateContract',
    method: 'POST',
    data
  })
}

/** 列表 */
export function contract_contractList(params) {
  return request({
    url: '/secret/contract/contractList',
    method: 'GET',
    params
  })
}

/** 详情 */
export function contract_contractDetails(params) {
  return request({
    url: '/secret/contract/contractDetails',
    method: 'GET',
    params
  })
}

