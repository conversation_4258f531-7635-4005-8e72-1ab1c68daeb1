<template>
  <div class="dashboard">
    <el-empty>
      <img slot="image" src="@/assets/dashboard/noData_bg.png" alt="" />
      <img slot="description" src="@/assets/dashboard/noData_text.png" alt="" />
    </el-empty>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Dashboard',
  computed: {
    ...mapGetters(['name'])
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  position: relative;
  width: 100%;
  min-height: 100%;
}
::v-deep {
  .el-empty {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    .el-empty__image {
      width: 414px;
      height: 192px;
    }
  }
}
</style>
