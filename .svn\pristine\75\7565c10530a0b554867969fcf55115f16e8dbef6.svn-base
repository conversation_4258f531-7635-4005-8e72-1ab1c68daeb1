<template>
  <div class="app-container">
    <div class="dashboard_left">
      <el-card class="left_header_box">
        <div slot="header">
          <div>
            <span>{{ todoInfo.todoCount }}</span>
            <span>待处理</span>
          </div>
          <div>
            <span>{{ todoInfo.weekCount }}</span>
            <span>周处理</span>
          </div>
          <div>
            <span>{{ todoInfo.monthCount }}</span>
            <span>月处理</span>
          </div>
        </div>
        <div class="backlog">
          <span class="backlog_title">我的待办</span>
          <div :class="[{ backlogType1: checked_backlogType === '流程', backlogType2: checked_backlogType === '会议', backlogType3: checked_backlogType === '培训', backlogType4: checked_backlogType === '项目' }, 'backlogType']">
            <div @click="checked_backlogType = '任务'">
              <img src="@/assets/dashboard/task.png" alt="" />
              <span>任务: {{ backlogInfo.taskCount }}</span>
            </div>
            <div @click="checked_backlogType = '流程'">
              <img src="@/assets/dashboard/process.png" alt="" />
              <span>流程: {{ backlogInfo.processCount }}</span>
            </div>
            <div @click="checked_backlogType = '会议'">
              <img src="@/assets/dashboard/meeting.png" alt="" />
              <span>会议: {{ backlogInfo.meetingCount }}</span>
            </div>
            <div @click="checked_backlogType = '培训'">
              <img src="@/assets/dashboard/training.png" alt="" />
              <span>培训: {{ backlogInfo.trainingCount }}</span>
            </div>
            <div @click="checked_backlogType = '项目'">
              <img src="@/assets/dashboard/project.png" alt="" />
              <span>项目: {{ backlogInfo.projectCount }}</span>
            </div>
          </div>
        </div>
        <div class="backlogList">
          <!-- 流程管理 -->
          <el-table v-show="checked_backlogType === '流程' && processList.length > 0" :data="processList" style="width: 100%" header-cell-class-name="tableHeader">
            <el-table-column align="center" prop="title" label="事项名称" width="width">
              <template v-slot="{ row }">
                <el-tooltip class="item" effect="dark" :content="row.title" placement="top-start">
                  <span class="Title">{{ row.title }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="processName" label="事项类型" width="width"> </el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="width">
              <template v-slot="{ row }">
                <span>{{ row.status | currentTaskStatus }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="realName" label="发起人" width="width"> </el-table-column>
            <el-table-column align="center" prop="createTime" label="发起时间" width="width"> </el-table-column>
            <el-table-column align="center" width="70">
              <template v-slot:header>
                <span class="skipButton" @click="jumpRouter({ type: 2, path: '/process/management' })"><i class="el-icon-arrow-right"></i></span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 会议列表 -->
          <el-table v-show="checked_backlogType === '会议' && meetingList.length > 0" :data="meetingList" style="width: 100%" header-cell-class-name="tableHeader">
            <el-table-column align="center" prop="meetingName" label="会议主题" width="width">
              <template v-slot="{ row }">
                <el-tooltip class="item" effect="dark" :content="row.meetingName" placement="top-start">
                  <span class="Title">{{ row.meetingName }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="meetingRoomName" label="会议室" width="width"> </el-table-column>
            <el-table-column align="center" label="状态" width="width">
              <template v-slot="{ row }">
                <span :class="[{ meetingStatus1: row.meetingStatus == 1, meetingStatus2: row.meetingStatus == 2 }, 'meetingStatus']">{{ row.meetingStatus | meetingStatus }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="realName" label="预定人" width="width"> </el-table-column>
            <el-table-column align="center" prop="startTime" label="会议时间" width="width"> </el-table-column>
            <el-table-column align="center" prop="attendMeetingUsers" label="参会成员" width="width">
              <template v-slot="{ row }">
                <el-tooltip class="item" effect="dark" :content="row.attendMeetingUsers" placement="top-start">
                  <span class="attendMeetingUsers">{{ row.attendMeetingUsers }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" width="70">
              <template v-slot:header>
                <span class="skipButton" @click="jumpRouter({ type: 3, path: '/meeting' })"><i class="el-icon-arrow-right"></i></span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 培训列表 -->
          <el-table v-show="checked_backlogType === '培训' && TrainList.length > 0" :data="TrainList" style="width: 100%" header-cell-class-name="tableHeader">
            <el-table-column align="center" prop="trainName" label="培训标题" width="width">
              <template v-slot="{ row }">
                <el-tooltip class="item" effect="dark" :content="row.trainName" placement="top-start">
                  <span class="Title">{{ row.trainName }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column align="center" label="状态" width="width">
              <template v-slot="{ row }">
                <span :class="[{ trainStatus1: row.trainStatus == 1, trainStatus2: row.trainStatus == 2 }, 'trainStatus']">{{ row.trainStatus | trainStatus }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="trainUserName" label="培训人" width="width"> </el-table-column>
            <el-table-column align="center" label="培训范围" width="width">
              <template v-slot="{ row }">
                <span>{{ row.powerType | powerType }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="endTime" label="报名结束时间" width="width"> </el-table-column>
            <el-table-column align="center" prop="startTime" label="培训开始时间" width="width"> </el-table-column>
            <el-table-column align="center" width="70">
              <template v-slot:header>
                <span class="skipButton" @click="jumpRouter({ type: 5, path: '/training' })"><i class="el-icon-arrow-right"></i></span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 项目列表 -->
          <el-table v-show="checked_backlogType === '项目' && projectList.length > 0" :data="projectList" style="width: 100%" header-cell-class-name="tableHeader">
            <el-table-column align="center" prop="code" label="项目编号" width="width"> </el-table-column>
            <el-table-column align="center" prop="name" label="项目名称" width="width">
              <template v-slot="{ row }">
                <div class="projectName">
                  <el-tooltip class="item" effect="dark" :content="row.name" placement="top">
                    <span>{{ row.name }}</span>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="customerName" label="客户名称"> </el-table-column>
            <el-table-column align="center" prop="organizationName" label="负责部门"> </el-table-column>
            <el-table-column align="center" prop="realName" label="项目负责人" width="width"> </el-table-column>
            <el-table-column align="center" prop="stage" label="项目阶段">
              <template v-slot="{ row }">
                <span v-if="row.stage" class="stage" :style="[{ color: row.stage === 1 ? '#868B9F' : row.stage === 2 || row.stage === 3 || row.stage === 4 ? '#0B1A44' : '#fff' }, { borderColor: row.stage === 1 ? '#D8DBE1' : row.stage === 2 || row.stage === 3 || row.stage === 4 ? '#F9C6A3' : '#e0f6ee' }, , { backgroundColor: row.stage === 1 ? '#faf9f9' : row.stage === 2 || row.stage === 3 || row.stage === 4 ? '#ffead6' : row.stage === null ? 'transparent' : '#23BB87' }]">{{ row.stage | projectStage }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updateTime" label="更新时间"> </el-table-column>
            <el-table-column align="center" width="70">
              <template v-slot:header>
                <span class="skipButton" @click="jumpRouter({ type: 4, path: '/project' })"><i class="el-icon-arrow-right"></i></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <div class="dashboard_left_bottom">
        <div class="dashboard_left_bottom_header">
          <span>待办统计</span>
          <div class="search">
            <span>日期筛选:</span>
            <el-radio-group v-model="backlogStatisInfo.type" style="margin-right: 32px" @change="radioChange">
              <el-radio :label="1">本周</el-radio>
              <el-radio :label="2">本月</el-radio>
              <el-radio :label="3">全年</el-radio>
            </el-radio-group>
            <el-date-picker v-model="date" size="small" type="daterange" range-separator="→" start-placeholder="开始日期" end-placeholder="结束日期" @change="datePickerChange"> </el-date-picker>
          </div>
        </div>
        <div class="dashboard_left_bottom_body">
          <el-row class="statistics">
            <div :class="[{ selectedItem: currentSelectStatistics === '任务' }, 'task', 'statisticsItem']" @click="currentChange('任务')">
              <div class="left">
                <span></span>
                任务
              </div>
              <div class="right">
                <el-progress type="circle" :percentage="backlogStatisData.taskCount" :stroke-width="10" :width="81" stroke-linecap="square" color="#3464e0" :show-text="false"></el-progress>
                <div class="progress_text">
                  <span>完成率</span>
                  <span>{{ backlogStatisData.taskCount }}%</span>
                </div>
              </div>
            </div>
            <div :class="[{ selectedItem: currentSelectStatistics === '流程' }, 'process', 'statisticsItem']" @click="currentChange('流程')">
              <div class="left">
                <span></span>
                流程
              </div>
              <div class="right">
                <el-progress type="circle" :percentage="backlogStatisData.processCount" :stroke-width="10" :width="81" stroke-linecap="square" color="#6970ff" :show-text="false"></el-progress>
                <div class="progress_text">
                  <span>完成率</span>
                  <span>{{ backlogStatisData.processCount }}%</span>
                </div>
              </div>
            </div>
            <div :class="[{ selectedItem: currentSelectStatistics === '会议' }, 'meeting', 'statisticsItem']" @click="currentChange('会议')">
              <div class="left">
                <span></span>
                会议
              </div>
              <div class="right">
                <el-progress type="circle" :percentage="backlogStatisData.meetingCount" :stroke-width="10" :width="81" stroke-linecap="square" color="#0eaeff" :show-text="false"></el-progress>
                <div class="progress_text">
                  <span>完成率</span>
                  <span>{{ backlogStatisData.meetingCount }}%</span>
                </div>
              </div>
            </div>
            <div :class="[{ selectedItem: currentSelectStatistics === '培训' }, 'train', 'statisticsItem']" @click="currentChange('培训')">
              <div class="left">
                <span></span>
                培训
              </div>
              <div class="right">
                <el-progress type="circle" :percentage="backlogStatisData.trainingCount" :stroke-width="10" :width="81" stroke-linecap="square" color="#33B4CF" :show-text="false"></el-progress>
                <div class="progress_text">
                  <span>完成率</span>
                  <span>{{ backlogStatisData.trainingCount }}%</span>
                </div>
              </div>
            </div>
            <div :class="[{ selectedItem: currentSelectStatistics === '项目' }, 'project', 'statisticsItem']" @click="currentChange('项目')">
              <div class="left">
                <span></span>
                项目
              </div>
              <div class="right">
                <el-progress type="circle" :percentage="backlogStatisData.projectCount" :stroke-width="10" :width="81" stroke-linecap="square" color="#4190FF" :show-text="false"></el-progress>
                <div class="progress_text">
                  <span>完成率</span>
                  <span>{{ backlogStatisData.projectCount }}%</span>
                </div>
              </div>
            </div>
          </el-row>
          <div class="echarts">
            <template v-if="currentSelectStatistics === '流程'">
              <processStatisEchart ref="processStatisEchart" style="width: 100%; height: 100%" @jumpPage="jumpRouter({ type: 2, path: '/process/management' }, true)" />
            </template>
            <template v-if="currentSelectStatistics === '会议'">
              <el-row>
                <span class="meetingStatisEcharttitle">会议情况统计</span>
              </el-row>
              <el-row class="statisticsNum" type="flex">
                <div>
                  <span></span>
                  <span>日均会议数：{{ meetingStatisNum.averageDayCount }}场</span>
                </div>
                <div>
                  <span></span>
                  <span>平均参会人员：{{ meetingStatisNum.averageManCount }}人</span>
                </div>
                <div>
                  <span></span>
                  <span>平均会议时长：{{ parseInt(meetingStatisNum.averageMeetingTime / 60) }}h</span>
                </div>
              </el-row>
              <meetingStatisEchart ref="meetingStatisEchart" style="height: 350px; width: 100%" @jumpPage="jumpRouter({ type: 3, path: '/meeting' }, true)" />
            </template>
            <template v-if="currentSelectStatistics === '培训'">
              <trainStatisEchart ref="trainStatisEchart" style="width: 100%; height: 100%" @jumpPage="jumpRouter({ type: 5, path: '/training' }, true)" />
            </template>
            <template v-if="currentSelectStatistics === '项目'">
              <projectStatisEchart ref="projectStatisEchart" style="width: 100%; height: 100%" @jumpPage="jumpRouter({ type: 4, path: '/project' }, true)" />
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="dashboard_right">
      <div class="dashboard_right_top" :style="organizationId != 56642510 ? 'height:200px' : ''">
        <div class="userInfo">
          <div>
            <el-avatar :size="44" :src="avatar"></el-avatar>
            <p class="realName">{{ getTimeState }}{{ realName }}</p>
            <p class="restsInfo"><img src="@/assets/dashboard/dashboard_right_top_icon1.png" alt="" /> {{ jobName }}</p>
            <p class="restsInfo" style="margin-top: 6px"><img src="@/assets/dashboard/dashboard_right_top_icon2.png" alt="" /> 中飞科技-{{ organizationName }}</p>
          </div>
          <div>
            <img src="@/assets/dashboard/dashboard_right_top_img.png" alt="" />
          </div>
        </div>
        <div v-if="organizationId == 56642510" class="signInfo">
          <span @click="sign(0)">
            <span>进校</span>
            <span>{{ time }}</span>
          </span>
          <span @click="sign(1)">
            <span>出校</span>
            <span>{{ time }}</span>
          </span>
          <span @click="sign(2)">
            <span>晚签</span>
            <span>{{ time }}</span>
          </span>
        </div>
      </div>
      <div class="dashboard_right_center">
        <el-row type="flex" justify="space-between">
          <span>本月个人工时统计</span>
          <span>(单位：小时)</span>
        </el-row>
        <WorkingHoursEchart class="WorkingHoursEchart" />
      </div>
      <div class="dashboard_right_bottom">
        <el-row>
          <img src="@/assets/dashboard/dashboard_right_bottom_title.png" alt="" />
        </el-row>
        <div v-for="item in recordList" :key="item.id" class="recordList">
          <el-row type="flex" justify="space-between" align="middle">
            <div class="recordList_avatar">
              <el-avatar :size="26" :src="item.headurl"></el-avatar>
              <span>{{ item.realName }}</span>
            </div>
            <div class="createTime">{{ item.createTime }}</div>
          </el-row>
          <el-row>
            <el-tooltip ref="tooltip" class="item" effect="dark" placement="top-start" popper-class="tooltipRecord">
              <template v-slot:content>
                <div>
                  {{ item.realName }}在<span class="module">" {{ item.module }} "</span>{{ item.content.substring(0, item.content.indexOf(' ', 0)) }}为<span class="content">{{ item.content.substring(item.content.indexOf(' ', 0)) }}</span>
                </div>
              </template>
              <div>
                {{ item.realName }}在<span class="module">" {{ item.module }} "</span>{{ item.content.substring(0, item.content.indexOf(' ', 0)) }}为<span class="content">{{ item.content.substring(item.content.indexOf(' ', 0)) }}</span>
              </div>
            </el-tooltip>
          </el-row>
        </div>
      </div>
    </div>

    <!-- <el-empty>
      <img slot="image" src="@/assets/dashboard/noData_bg.png" alt="" />
      <img slot="description" src="@/assets/dashboard/noData_text.png" alt="" />
    </el-empty> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { workbenchTodo, workbenchMyHandling, workBenchLog, HandlingStatis, processStatis, meetingStatis, trainStatis, projectStatis } from '@/api/dashboard.js'
import { instanceList } from '@/api/process'
import { meetingList } from '@/api/meeting'
import { trainList } from '@/api/training'
import { projectList } from '@/api/project.js'
import { secret_sign } from '@/api/sign.js'
import { formatDate, processStatus, projectStage } from '@/filters'
import WorkingHoursEchart from '@/views/dashboard/echarts/Working_hours'
import processStatisEchart from '@/views/dashboard/echarts/processStatis'
import meetingStatisEchart from '@/views/dashboard/echarts/meetingStatisEchart'
import trainStatisEchart from '@/views/dashboard/echarts/trainStatisEchart'
import projectStatisEchart from '@/views/dashboard/echarts/projectStatisEchart'
export default {
  name: 'Dashboard',
  components: {
    WorkingHoursEchart,
    processStatisEchart,
    meetingStatisEchart,
    trainStatisEchart,
    projectStatisEchart
  },
  data() {
    return {
      times: null, // 时间定时器
      time: null, // 当前时间
      checked_backlogType: '流程',
      todoInfo: {}, // 待处理
      backlogInfo: {}, // 我的待办
      recordList: [], // 操作记录
      // 流程列表请求参数
      processInfo: {
        type: 1,
        pageSize: 6,
        pageNum: 1
      },
      processList: [], // 流程列表
      meetingInfo: {
        // 会议列表请求参数
        type: 1,
        pageSize: 6,
        pageNum: 1
      },
      meetingList: [], // 会议列表
      trainInfo: {
        // 培训列表请求参数
        organizationId: null,
        type: 1,
        pageNum: 1,
        pageSize: 6
      },
      TrainList: [], // 培训列表
      projectInfo: {
        // 项目列表请求参数
        type: 1,
        pageNum: 1,
        pageSize: 6
      },
      projectList: [], // 项目列表
      backlogStatisInfo: {
        // 待办统计请求参数
        type: 1, // 日期筛选 1 本周 2 本月 3 全年
        startTime: null,
        endTime: null,
        userId: null
      },
      backlogStatisData: {},
      date: null, // 待办统计选择时间框绑定值
      currentSelectStatistics: '流程', // 当前选中的统计图
      meetingStatisNum: {}
    }
  },
  computed: {
    ...mapGetters(['realName', 'avatar', 'jobName', 'organizationName', 'organizationId', 'keyList']),
    getTimeState() {
      // 获取当前时间
      const timeNow = new Date()
      // 获取当前小时
      const hours = timeNow.getHours()
      // 设置默认文字
      let state = ``
      // 判断当前时间段
      if (hours >= 0 && hours <= 10) {
        state = `早上好,`
      } else if (hours > 10 && hours <= 14) {
        state = `中午好,`
      } else if (hours > 14 && hours <= 18) {
        state = `下午好,`
      } else if (hours > 18 && hours <= 24) {
        state = `晚上好,`
      }
      return state
    }
  },
  created() {
    this.getWorkbenchTodo()
    this.getWorkbenchMyHandling()
    this.getWorkBenchLog()
    this.getBacklogList()
    this.getHandlingStatis()
    this.getTime()
    // this.currentChange()
  },

  methods: {
    // 实时获取当前时间
    getTime() {
      this.times = setInterval(() => {
        this.time = formatDate(new Date(), 'hh:mm:ss')
      }, 1000)
    },
    // 待处理查询
    async getWorkbenchTodo() {
      const { data } = await workbenchTodo()
      this.todoInfo = data
    },
    // 我的待办查询
    async getWorkbenchMyHandling() {
      const { data } = await workbenchMyHandling()
      this.backlogInfo = data
    },
    // 待办事项列表获取
    getBacklogList() {
      this.getInstanceList()
      this.getMeetingList()
      this.geTrainList()
      this.getProjectList()
    },
    // 获取流程列表
    async getInstanceList() {
      const { data } = await instanceList(this.processInfo)
      this.processList = data.list
    },
    // 获取会议列表
    async getMeetingList() {
      const { data } = await meetingList(this.meetingInfo)
      this.meetingList = data.list
    },
    // 获取培训列表
    async geTrainList() {
      this.trainInfo.organizationId = this.organizationId
      const { data } = await trainList(this.trainInfo)
      this.TrainList = data.list
    },
    // 获取项目列表
    async getProjectList() {
      const { data } = await projectList(this.projectInfo)
      this.projectList = data.list
    },
    // 操作记录
    async getWorkBenchLog() {
      const { data } = await workBenchLog()
      this.recordList = data
    },
    // 待办统计占比
    async getHandlingStatis() {
      const { data } = await HandlingStatis(this.backlogStatisInfo)
      for (const key in data) {
        this.backlogStatisData[key] = parseInt(data[key])
      }
      this.currentChange()
    },
    // 点击列表中的箭头跳转相对于的管理模块
    jumpRouter(item, isEchartJump) {
      this.$store.commit('checkedData/set_data_type', item.type)
      if (isEchartJump) {
        // 判断是否是点击echart图例跳转
        if (this.backlogStatisInfo.startTime && this.backlogStatisInfo.endTime) {
          //  判断是选择时间，是则存起来，跳转到指定页面后根据时间进行查询
          const obj = {
            startTime: this.backlogStatisInfo.startTime,
            endTime: this.backlogStatisInfo.endTime
          }
          window.localStorage.setItem('moduleTime', JSON.stringify(obj))
        }
      }

      this.$nextTick(() => {
        if (item.type === 2) {
          if (this.keyList.includes('process')) {
            this.$router.push(item.path)
          } else {
            this.$message.warning('您暂无进入流程管理的权限')
          }
        } else if (item.type === 3) {
          if (this.keyList.includes('meeting')) {
            this.$router.push(item.path)
          } else {
            this.$message.warning('您暂无进入会议管理的权限')
          }
        } else if (item.type === 4) {
          if (this.keyList.includes('project')) {
            this.$router.push(item.path)
          } else {
            this.$message.warning('您暂无进入项目管理的权限')
          }
        } else if (item.type === 5) {
          if (this.keyList.includes('training')) {
            this.$router.push(item.path)
          } else {
            this.$message.warning('您暂无进入培训管理的权限')
          }
        }
      })
    },
    // 日期改变触发
    radioChange(val) {
      this.backlogStatisInfo.type = val
      this.getHandlingStatis()
    },
    // 待办统计选择时间触发
    datePickerChange(val) {
      if (val) {
        this.backlogStatisInfo.startTime = formatDate(val[0])
        this.backlogStatisInfo.endTime = formatDate(val[1], 'yyyy-MM-dd') + ' 23:59:59'
      } else {
        this.backlogStatisInfo.startTime = null
        this.backlogStatisInfo.endTime = null
      }
      this.getHandlingStatis()
    },
    // 点击待办统计的模块触发事件
    currentChange(val) {
      if (val) {
        this.currentSelectStatistics = val
      }
      switch (this.currentSelectStatistics) {
        case '流程':
          this.getProcessStatis()
          break
        case '会议':
          this.getMeetingStatis()
          break
        case '培训':
          this.getTrainStatis()
          break
        case '项目':
          this.getProjectStatis()
          break
        default:
          break
      }
    },
    // 待办统计 - 流程查询
    async getProcessStatis() {
      const { data } = await processStatis(this.backlogStatisInfo)
      const x = data.map((item) => item.name)
      // const y = data.map((item) => item.code)
      const y = []
      data.forEach((item) => {
        y.push({
          value: item.code,
          msg: item.msg
        })
      })
      y.forEach((item) => {
        if (item.msg.length > 0) {
          item.msg.forEach((item) => {
            item.status = processStatus(item.status)
          })
        }
      })
      const dataInfo = {
        x,
        y
      }
      this.$nextTick(() => {
        this.$refs['processStatisEchart'].init(dataInfo)
      })
    },
    // 待办统计 - 会议查询
    async getMeetingStatis() {
      const { data } = await meetingStatis(this.backlogStatisInfo)
      this.meetingStatisNum = {
        averageDayCount: data.averageDayCount,
        averageManCount: data.averageManCount,
        averageMeetingTime: data.averageMeetingTime
      }
      const x = data.msg.map((item) => item.name)
      const y = []
      const lineData = data.msg.map((item) => parseFloat(item.length / 60).toFixed(1))
      console.log(lineData)

      data.msg.forEach((item) => {
        y.push({
          value: item.code,
          msg: item.msg
        })
      })
      const dataInfo = {
        x,
        y,
        line: lineData
      }
      console.log(dataInfo)
      this.$nextTick(() => {
        this.$refs['meetingStatisEchart'].init(dataInfo)
      })
    },
    // 待办统计 - 培训查询
    async getTrainStatis() {
      const { data } = await trainStatis(this.backlogStatisInfo)
      const x = data.map((item) => item.name)
      const department = data.map((item) => item.department)
      const company = data.map((item) => item.company)
      const external = data.map((item) => item.external)
      const dataInfo = {
        x,
        department,
        company,
        external
      }
      this.$nextTick(() => {
        this.$refs['trainStatisEchart'].init(dataInfo)
      })
    },
    // 待办统计 - 项目查询
    async getProjectStatis() {
      const { data } = await projectStatis(this.backlogStatisInfo)
      const ehartData = []
      data.forEach((item) => {
        ehartData.push({
          value: item.count,
          name: projectStage(item.versionStage)
        })
      })
      this.$nextTick(() => {
        this.$refs['projectStatisEchart'].init(ehartData)
      })
    },
    // 签到
    async sign(type) {
      await secret_sign({ type })
      this.$message.success('操作成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  // justify-content: space-between;
  position: relative;
  width: 100%;
  min-height: 100%;
  padding-right: 20px;
  padding-left: 10px;
  background: #e8eaed;
  .dashboard_left {
    margin-right: 16px;
    .left_header_box {
      width: 1339px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      ::v-deep {
        .el-card__header {
          height: 111px;
          padding: 0;
          padding: 26px 0 14px 0;
          border: none;
          background: url('../../assets/dashboard/header_img.png') no-repeat;
          background-size: cover;
          & > div {
            display: flex;
            justify-content: center;
            width: 314px;
            height: 71px;
            margin-right: 48px;
            margin-left: auto;
            padding-top: 13px;
            background: rgba($color: #3d589e, $alpha: 0.5);
            border-radius: 6px;
            & > div {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              align-items: center;
              height: 48px;
              padding: 0 32px;
              &:first-of-type {
                padding-left: 0;
              }
              &:nth-of-type(2) {
                position: relative;
                &::before {
                  content: '';
                  position: absolute;
                  left: 0;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 1px;
                  height: 40px;
                  background: #d8dbe1;
                }
                &::after {
                  content: '';
                  position: absolute;
                  right: 0;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 1px;
                  height: 40px;
                  background: #d8dbe1;
                }
              }
              &:last-of-type {
                padding-right: 0;
                border: none;
              }
              span {
                color: #fff;
                &:first-of-type {
                  font-size: 20px;
                  font-family: D-DIN Exp-DINExp-Bold, D-DIN Exp-DINExp;
                  font-weight: bold;
                }
                &:last-of-type {
                  font-size: 14px;
                }
              }
            }
          }
        }
        .el-card__body {
          padding: 16px 36px;
          .backlog {
            display: flex;
            align-items: center;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
            .backlog_title {
              font-size: 16px;
              margin-right: 13px;
            }
            .backlogType {
              display: flex;
              position: relative;
              &::after {
                content: '';
                position: absolute;
                width: 1249px;
                height: 1px;
                bottom: 0;
                background: #eeeeef;
              }
              &::before {
                content: '';
                position: absolute;
                width: 172px;
                left: 0;
                bottom: 0;
                height: 1px;
                background: #3464e0;
                z-index: 1;
                transition: all 0.2s;
              }
              & > div {
                display: flex;
                align-items: center;
                position: relative;
                width: 140px;
                margin-left: 32px;
                cursor: pointer;
                &::after {
                  content: '';
                  position: absolute;
                  right: 0;
                  width: 1px;
                  height: 33px;
                  background: #eeeeef;
                }
                img {
                  margin-right: 8px;
                }
              }
            }
            .backlogType1 {
              &::before {
                left: 172px;
              }
            }
            .backlogType2 {
              &::before {
                left: calc(172px * 2);
              }
            }
            .backlogType3 {
              &::before {
                left: calc(172px * 3);
              }
            }
            .backlogType4 {
              &::before {
                left: calc(172px * 4);
              }
            }
          }
          .backlogList {
            margin-top: 24px;
            // 设置表头单元格样式
            .tableHeader {
              background: #eff1f3;
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            // 表格字段溢出隐藏操作
            .Title {
              display: inline-block;
              width: 100%;
              overflow: hidden; //超出隐藏
              text-overflow: ellipsis; //溢出用省略号显示
              white-space: nowrap;
            }
            // 设置每个单元格的样式
            .el-table__row {
              height: 40px;
              box-sizing: border-box;
              .el-table__cell {
                padding: 0;
                font-size: 13px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #0b1a44;
              }
            }
            // 设置鼠标经过表格的样式
            .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
              background-color: #eff1f3;
            }
            // 取消表格边框
            .el-table td.el-table__cell,
            .el-table th.el-table__cell.is-leaf {
              border: none;
            }
            // 取消表格边框
            .el-table {
              &::before {
                display: none;
              }
            }
            // < -- 设置会议状态前面的小圆点
            .meetingStatus,
            .trainStatus {
              position: relative;
              color: #868b9f;
              &::before {
                content: '';
                position: absolute;
                left: -10px;
                top: 50%;
                transform: translateY(-35%);
                width: 6px;
                height: 6px;
                background: #868b9f;
                border-radius: 6px;
              }
            }
            .meetingStatus1,
            .trainStatus1 {
              color: #0b1a44;
              &::before {
                background: #3465df;
              }
            }
            .meetingStatus2,
            .trainStatus2 {
              color: #0b1a44;
              &::before {
                background: #ff7e26;
              }
            }
            // 设置会议状态前面的小圆点 -- >

            // 项目列表名称样式
            .projectName {
              span {
                display: inline-block;
                width: 100%;
                font-weight: bold;
                overflow: hidden; //超出隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap;
              }
            }
            // 项目列表阶段样式
            .stage {
              display: block;
              margin: 0 auto;
              width: 54px;
              height: 22px;
              line-height: 22px;
              border: 1px solid;
            }
            // 设置会议列表中参会人员溢出隐藏
            .attendMeetingUsers {
              display: inline-block;
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .skipButton {
              display: inline-block;
              width: 16px;
              height: 16px;
              line-height: 16px;
              background: #fff;
              border-radius: 4px;
              box-shadow: 0px 3px 6px 1px rgba(47, 69, 157, 0.16);
              cursor: pointer;
              i {
                color: #0b1a44;
                font-weight: bold;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    .dashboard_left_bottom {
      width: 1339px;
      height: 690px;
      margin-top: 16px;
      padding: 24px 0;
      padding-left: 28px;
      padding-right: 36px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      .dashboard_left_bottom_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        & > span {
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        .search {
          display: flex;
          align-items: center;
          span {
            height: 15px;
            margin-right: 16px;
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #868b9f;
          }
          ::v-deep {
            // 单选框的样式
            .el-radio__input.is-checked .el-radio__inner {
              // background: #3464e0;
              background: #fff;
              width: 16px;
              height: 16px;
              border-color: #3464e0;
              // border: none;
              &::after {
                background-color: #3464e0;
                width: 7px;
                height: 7px;
              }
            }
            .el-radio__inner {
              width: 16px;
              height: 16px;
            }
            .el-radio__input.is-checked + .el-radio__label {
              color: #0b1a44;
            }
            .el-radio__label {
              color: #0b1a44;
              font-size: 13px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
            }
            // 时间选择框样式
            .el-range-editor--small.el-input__inner {
              width: 249px;
              height: 32px;
              background: #f5f7f7;
              border: none;
            }
            .el-range-input {
              background: #f5f7f7;
            }
            .app-container .dashboard_left .dashboard_left_bottom .dashboard_left_bottom_header .search[data-v-106c86ed] .el-range-editor--small .el-range__close-icon,
            .app-container .dashboard_left .dashboard_left_bottom .dashboard_left_bottom_header .search[data-v-106c86ed] .el-range-editor--small .el-range__icon {
              line-height: 26px;
            }
            .el-range-editor--small .el-range__close-icon,
            .el-range-editor--small .el-range__icon {
              line-height: 28px;
            }
          }
        }
      }
      .dashboard_left_bottom_body {
        padding-left: 43px;
        padding-right: 35px;
        padding-top: 20px;
        .statistics {
          display: flex;
          margin-bottom: 27px;
          .statisticsItem {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 230px;
            height: 106px;
            padding: 0 40px;
            margin-right: 16px;
            background: #f6f8f9;
            border-radius: 4px 4px 4px 4px;
            cursor: pointer;
            transition: all 0.2s;

            &:last-of-type {
              margin-right: 0;
            }
            .left {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
              span {
                display: inline-block;
                width: 8px;
                height: 8px;
                background: #ffffff;
                border: 2px solid #3464e0;
                border-radius: 8px;
                box-sizing: border-box;
              }
            }
            .right {
              width: 81px;
              height: 81px;
              position: relative;
              .progress_text {
                position: absolute;
                top: 50%;
                left: 51%;
                transform: translate(-50%, -50%);
                text-align: center;
                & > span:first-of-type {
                  font-size: 12px;
                  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                  font-weight: 400;
                  color: #a3a8bb;
                }
                & > span:last-of-type {
                  font-size: 14px;
                  font-family: D-DIN Exp-DINExp-Bold, D-DIN Exp-DINExp;
                  font-weight: bold;
                  color: #0b1a44;
                }
              }
            }
          }
          .selectedItem {
            background: #ffffff;
            box-shadow: 0px 6px 12px 1px rgba(49, 100, 167, 0.19);
            border-radius: 4px 4px 4px 4px;
            border-bottom: 2px solid #3465df;
            transition: all 0.2s;
          }
          .process {
            border-color: #6970ff;
            .left {
              span {
                border-color: #6970ff;
              }
            }
          }
          .meeting {
            border-color: #0eaeff;
            .left {
              span {
                border-color: #0eaeff;
              }
            }
          }
          .train {
            border-color: #33b4cf;

            .left {
              span {
                border-color: #33b4cf;
              }
            }
          }
          .project {
            border-color: #4190ff;

            .left {
              span {
                border-color: #4190ff;
              }
            }
          }
        }
        .echarts {
          width: 1214px;
          height: 460px;
          padding-top: 16px;
          padding-left: 24px;
          padding-bottom: 24px;
          padding-right: 24px;
          border-radius: 8px 8px 8px 8px;
          opacity: 1;
          border: 1px solid #eeeeef;
          .meetingStatisEcharttitle {
            font-size: 14px;
            font-family: Microsoft YaHei-Bold, Microsoft YaHei;
            font-weight: bold;
            color: #0b1a44;
          }
          .statisticsNum {
            margin-top: 16px;
            div {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 160px;
              height: 46px;
              margin-right: 16px;
              background: #eff4ff;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid #eeeeef;
              & > span:first-of-type {
                display: inline-block;
                width: 4px;
                height: 12px;
                background: #0eaeff;
                border-radius: 3px 3px 3px 3px;
              }
              &:nth-of-type(2) > span:first-of-type {
                background: #ff7e26;
              }
              &:last-of-type > span:first-of-type {
                background: #33b4cf;
              }
              & > span:last-of-type {
                margin-left: 6px;
                font-size: 14px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #0b1a44;
              }
            }
          }
        }
      }
    }
  }
  .dashboard_right {
    overflow: hidden;
    .dashboard_right_top {
      width: 382px;
      height: 258px;
      padding: 17px 31px 19px 40px;
      background-image: linear-gradient(to right, #fbfcff, #e4eeff);
      border-radius: 8px;
      box-sizing: border-box;
      .userInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .realName {
          margin: 12px 0 14px 0;
          font-size: 16px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #203c8b;
        }
        .restsInfo {
          display: flex;
          align-items: center;
          padding-bottom: 6px;
          border-bottom: 1px solid #eeeeef;
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #868b9f;
          img {
            margin-right: 3px;
          }
        }
      }
      .signInfo {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        & > span {
          padding-top: 6px;
          width: 96px;
          height: 48px;
          border-radius: 8px;
          cursor: pointer;

          &:first-of-type {
            background-image: linear-gradient(#3464e0, #4478fe, #3464e0);
            box-shadow: 0 3px 6px #b8c9f4;
          }
          &:nth-of-type(2) {
            background-image: linear-gradient(#ff7e26, #ff934a, #ff7e26);
            box-shadow: 0 3px 6px #ffceac;
          }
          &:last-of-type {
            background-image: linear-gradient(#0b1a44, #10286b, #0b1a44);
            box-shadow: 0 3px 6px #bbbfcb;
          }
          & > span {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #ffffff;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            &:first-of-type {
              margin-bottom: 6px;
              font-size: 13px;
              font-weight: bold;
            }
            &:last-of-type {
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
    }
    // 本月个人工时统计
    .dashboard_right_center {
      display: flex;
      flex-direction: column;
      width: 382px;
      height: 354px;
      margin-top: 16px;
      padding: 24px 16px 0 28px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      .el-row {
        & > span:first-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        & > span:last-of-type {
          font-size: 12px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #a3a8bb;
        }
      }
      .WorkingHoursEchart {
        flex: 1;
      }
    }
    // 操作记录
    .dashboard_right_bottom {
      margin-top: 16px;
      width: 382px;
      max-width: 382px;
      height: 484px;
      padding: 24px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      overflow: auto;
      &::-webkit-scrollbar-track-piece {
        background: #d3dce6;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #99a9bf;
        border-radius: 20px;
      }
      .recordList {
        padding-bottom: 14px;
        border-bottom: 1px solid #eeeeef;
        &:nth-of-type(n + 2) {
          padding-top: 14px;
        }
        & > .el-row:first-of-type {
          .recordList_avatar {
            display: flex;
            align-items: center;
            & > span:last-of-type {
              margin-left: 8px;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
          .createTime {
            font-size: 12px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
          }
        }
        & > .el-row:last-of-type {
          width: 100%;
          margin-top: 8px;
          font-size: 14px;
          color: #868b9f;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .module {
            margin: 0 5px;
            color: #0b1a44;
          }
          .content {
            margin-left: 5px;
            color: #3464e0;
          }
        }
      }
    }
  }
}
::v-deep {
  .el-card {
    box-shadow: none;
    border: none;
  }
  .el-empty {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    .el-empty__image {
      width: 414px;
      height: 192px;
    }
  }
}
</style>
<style lang="scss">
.tooltipRecord {
  padding: 17px 32px;
  background: #363636;
  box-shadow: 0px 6px 12px 1px rgba(141, 163, 200, 0.16);
  border-radius: 4px 4px 4px 4px;
  opacity: 0.84;
}
</style>
