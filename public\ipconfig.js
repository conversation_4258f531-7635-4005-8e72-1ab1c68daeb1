;(function (window) {
  window.config = {
    /**本地地址-闫 */
    // VUE_APP_BASE_API: 'http://192.168.1.14:8701',
    // VUE_APP_WS_URL: 'ws://192.168.1.14:8701/ws/workmanage',
    // VUE_APP_WEBGLURL: 'http://work.sdzft.com/cloudFile/workmanage/webgl/',
    // VUE_APP_DOWNLOAD_URL: 'http://192.168.1.14:8091', // 目前用于软硬件库下载excel模板、销售方案导出word地址、其他图片等
    // VUE_APP_UPLOAD_Library_URL: 'http://192.168.1.14:8701' //  知识库、资源库、虚拟仿真、产品发布 上传接口地址
    /** 线上地址 */
    // VUE_APP_BASE_API: 'http://work.sdzft.com/zf-work-manage',
    // VUE_APP_WS_URL: 'ws://work.sdzft.com/ws/workmanage',
    // VUE_APP_WEBGLURL: 'http://192.168.1.68/cloudFile/workmanage/webgl/',
    // // VUE_APP_WEBGLURL: 'http://work.sdzft.com/cloudFile/workmanage/webgl/',
    // VUE_APP_DOWNLOAD_URL: 'http://work.sdzft.com', // 目前用于软硬件库下载excel模板
    // VUE_APP_UPLOAD_Library_URL: 'http://192.168.1.68:8701', //  知识库、资源库、虚拟仿真、产品发布 上传接口地址
    VUE_APP_OSS_API: 'http://yun.51-x.cn/oss',
    /**本地地址-王 */
    VUE_APP_BASE_API: 'http://192.168.1.33:8701',
    VUE_APP_WS_URL: 'ws://192.168.1.33:8701/ws/workmanage',

    VUE_APP_UPLOAD_Library_URL: 'http://192.168.1.33:8702' //  知识库、资源库、虚拟仿真、产品发布 会议纪要上传 上传接口地址
  }
})(window)
