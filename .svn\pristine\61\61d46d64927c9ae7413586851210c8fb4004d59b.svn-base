<template>
  <div class="app-container">
    <el-row :gutter="50" class="top" type="flex" justify="space-between" align="middle" style="margin-bottom: 16px">
      <el-col :span="6">
        <div class="top_left">
          <span class="meeting_icon">
            <img src="@/assets/repository/repository_icon.png" alt="" />
            培训管理
          </span>
          <span class="add"> <i class="el-icon-circle-plus-outline"></i> 分享知识</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { knowledgeBaseAddBrowse, knowledgeBaseDetails } from '@/api/repository'
export default {
  name: '',
  data() {
    return {}
  },
  created() {
    this.addKnowledgeBaseAddBrowse()
    this.getKnowledgeBaseDetails()
  },
  methods: {
    // 添加浏览次数
    async addKnowledgeBaseAddBrowse() {
      await knowledgeBaseAddBrowse({ knowledgeBaseId: this.$route.params.knowledgeBaseId })
    },
    async getKnowledgeBaseDetails() {
      const { data } = await knowledgeBaseDetails({ knowledgeBaseId: this.$route.params.knowledgeBaseId, belongType: 3 })
      console.log(data)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background-color: #e8eaed;
  min-height: 100%;
  font-family: Microsoft YaHei-Bold, Microsoft YaHei;
}
.top {
  .top_left {
    display: flex;
    align-items: center;
    .meeting_icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      img {
        margin-right: 8px;
      }
    }
    .add {
      display: none;
      width: 104px;
      height: 34px;
      line-height: 34px;
      font-size: 16px;
      background-color: #3464e0;
      font-family: Microsoft YaHei;
      color: #fff;
      text-align: center;
      border-radius: 8px;
      cursor: pointer;
    }
  }
}</style>
