<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：绩效管理 /</span>
        <span>{{ pageType }}</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        <div class="left">
          <img :src="avatar" alt="" class="avatar" />
          <div class="userInfo">
            <div>
              <span class="realName">{{ realName }}</span>
              <img v-if="sex === 'F'" src="@/assets/meeting/wuman.png" alt="" />
              <img v-else src="@/assets/meeting/man.png" alt="" />
              <i>|</i>
              <span class="jobName">{{ jobName }}</span>
            </div>
            <div class="organizationName">{{ organizationName }}</div>
          </div>
        </div>
        <div class="center">
          <div>
            <span>个人任务</span>
            <span>0</span>
          </div>
          <div>
            <span>自评分</span>
            <span>---</span>
          </div>
          <div>
            <span>上级评分</span>
            <span>---</span>
          </div>
          <div>
            <span>联查评分</span>
            <span>---</span>
          </div>
          <div>
            <span>最终得分</span>
            <span>---</span>
          </div>
        </div>
        <div class="right">
          <div class="print">打 印</div>
          <div class="submit">提 交</div>
        </div>
      </div>
      <div class="content">
        <div class="title">
          <span>选择添加绩效月份：</span>
          <el-date-picker v-model="month" type="month" placeholder="绩效月份" format="yyyy/MM月 KPI"> </el-date-picker>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: '',
  data() {
    return {
      pageType: '新增绩效',
      month: null
    }
  },
  computed: {
    ...mapGetters(['avatar', 'realName', 'sex', 'jobName', 'organizationName'])
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    height: 791px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    background: url('~@/assets/performance/add_bg.png') no-repeat;
    background-size: cover;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 48px;
      padding-left: 94px;
      padding-right: 40px;
      .left {
        display: flex;
        align-items: center;
        .avatar {
          width: 108px;
          height: 108px;
          margin-right: 29px;
          border-radius: 50%;
        }
        .userInfo {
          & > div:first-of-type {
            display: flex;
            align-items: center;
            .realName {
              font-size: 20px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            img {
              margin-left: 4px;
              width: 14px;
              height: 20px;
            }
            i {
              margin: 0 12px;
            }
            .jobName {
              font-size: 16px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #0b1a44;
            }
          }
          .organizationName {
            margin-top: 17px;
            width: 56px;
            height: 24px;
            line-height: 22px;
            background: #3464e0;
            border-radius: 4px 4px 4px 4px;
            font-size: 13px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
          }
        }
      }
      .center {
        display: flex;
        align-items: center;
        & > div {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 120px;
          height: 58px;
          border-right: 1px dashed #dcdcdc;
          &:last-of-type {
            border: none;
          }
          span {
            &:first-of-type {
              font-size: 14px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
            &:last-of-type {
              margin-top: 12px;
              font-size: 24px;
              font-family: Microsoft YaHei-Bold, Microsoft YaHei;
              font-weight: bold;
              color: #0b1a44;
            }
          }
        }
      }
      .right {
        position: relative;
        top: -45px;
        display: flex;
        div {
          width: 118px;
          height: 44px;
          line-height: 42px;
          border-radius: 6px;
          cursor: pointer;
        }
        .print {
          margin-right: 24px;
          background: #fdfdff;
          font-size: 18px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
          text-align: center;
        }
        .submit {
          background: #3465df;
          font-size: 18px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #fff;
          text-align: center;
        }
      }
    }
    .content {
      margin-top: 42px;
      .title {
        display: flex;
        justify-content: center;
        align-items: center;
        & > span {
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
        }
        ::v-deep {
          .el-input__inner {
            border: none;
            background: none;
            padding: 0;
            font-size: 24px;
            font-family: Source Han Serif CN-Bold, Source Han Serif CN;
            font-weight: bold;
            color: #3464e0;
            &::placeholder {
              font-size: 24px;
            }
          }
          .el-input__prefix,
          .el-input__suffix {
            display: none;
          }
        }
      }
    }
  }
}
</style>
