// 产品发布
import request from '@/utils/request'
/** 产品 - 添加 */
export function releaseProductAdd(data) {
  return request({
    url: '/releaseProduct/add',
    method: 'POST',
    data
  })
}
/** 产品 - 修改 */
export function releaseProductUpdate(data) {
  return request({
    url: '/releaseProduct/update',
    method: 'POST',
    data
  })
}
/** 产品 - 更新产品状态 */
export function releaseProductUpdateState(params) {
  return request({
    url: '/releaseProduct/updateState',
    method: 'DELETE',
    params
  })
}
/** 产品 - 详情 */
export function releaseProductDetail(params) {
  return request({
    url: '/releaseProduct/detail',
    method: 'GET',
    params
  })
}
/** 产品 - 删除 */
export function releaseProductRemove(params) {
  return request({
    url: '/releaseProduct/remove',
    method: 'DELETE',
    params
  })
}
/** 产品 - 列表(三个区域每个区域传对应的类型，需要调用三次) */
export function releaseProductList(params) {
  return request({
    url: '/releaseProduct/list',
    method: 'get',
    params
  })
}
/** 产品 - 列表 - 产品动态列表 */
export function releaseProductLogList(params) {
  return request({
    url: '/releaseProduct/log/list',
    method: 'get',
    params
  })
}
/** 产品 - 列表 - 测试bug列表 */
export function releaseProductBugList(params) {
  return request({
    url: '/releaseProduct/bug/list',
    method: 'get',
    params
  })
}
/** bug - 添加bug */
export function releaseProductBugAddBug(data) {
  return request({
    url: '/releaseProduct/bug/addBug',
    method: 'post',
    data
  })
}
/** bug - 添加回复 */
export function releaseProductBugAddReply(data) {
  return request({
    url: '/releaseProduct/bug/addReply',
    method: 'post',
    data
  })
}

/** 产品 - 添加查看日志 */
export function releaseProduct_log_addView({ productId }) {
  return request({
    url: '/releaseProduct/log/addView',
    method: 'get',
    params: {
      productId
    }
  })
}
/** 产品 - 查看日志列表 */
export function releaseProduct_log_viewList({ pageNum, pageSize, productId }) {
  return request({
    url: '/releaseProduct/log/viewList',
    method: 'get',
    params: {
      pageNum,
      pageSize,
      productId
    }
  })
}
