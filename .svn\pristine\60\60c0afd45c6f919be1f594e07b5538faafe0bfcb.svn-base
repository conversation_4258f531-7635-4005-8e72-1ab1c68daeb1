<template>
  <div class="navbar">
    <el-row :gutter="50" type="flex" justify="space-between" align="middle">
      <el-col :span="5" style="min-width: 360px">
        <div class="left_title">
          <router-link to="/">
            <img src="@/assets/Home/logo.png" alt="" class="logo" />
            <img src="@/assets/Home/title.png" alt="" class="title" />
          </router-link>

        </div>
      </el-col>
      <el-col :span="18">
        <div class="right-menu">
          <div class="fastTab" style="margin-right: 30px">
            <el-dropdown trigger="click">
              <div class="cut">
                <img src="@/assets/Home/fastTab.png" alt="" />
                <span v-if="type == 1" style="margin: 0 5px">基础数据管理</span>
                <span v-if="type == 2" style="margin: 0 5px">流程管理</span>
                <span v-if="type == 3" style="margin: 0 5px">会议管理</span>
                <span v-if="type == 4" style="margin: 0 5px">项目管理</span>
                <span v-if="type == 5" style="margin: 0 5px">培训/知识库管理</span>
                <span v-if="type == 6" style="margin: 0 5px">制度管理</span>
                <i class="el-icon-caret-bottom" style="font-size: 12px" />
              </div>
              <el-dropdown-menu slot="dropdown" size="small">
                <el-dropdown-item v-for="item in mode" :key="item.path" @click.native="checkTab(item)">
                  <div class="cutTabs" style="display: flex; align-items: center">
                    <svg-icon :icon-class="item.icon"></svg-icon> <span v-if="keyList.includes(item.flag)" style="margin-left:10px">{{ item.name }}</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="messageHint">
            <i class="el-icon-message-solid"></i>
            <i class="el-icon-caret-bottom"></i>
          </div>
          <el-dropdown class="avatar-container" trigger="click">
            <div class="cut">
              <img :src="avatar + '?imageView2/1/w/80/h/80'" class="user-avatar" />
              <i class="el-icon-caret-bottom" />
            </div>
            <el-dropdown-menu slot="dropdown" class="user-dropdown">
              <router-link to="/">
                <el-dropdown-item>
                  <span style="display: block">首页</span>
                </el-dropdown-item>
              </router-link>
              <el-dropdown-item @click.native="changePassword"> 修改密码 </el-dropdown-item>

              <el-dropdown-item divided @click.native="logout">
                <span style="display: block">退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-col>
    </el-row>
    <el-dialog title="修改密码" :visible.sync="changePasswordDialog" width="420px" append-to-body>
      <div>
        <el-form ref="passwordForm" :model="changePasswordInfo" label-width="80px" :rules="passwordRules">
          <el-form-item label="原密码" prop="oldPassword">
            <el-input v-model="changePasswordInfo.oldPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input v-model="changePasswordInfo.newPassword" type="password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="changePasswordInfo.confirmPassword" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="changePasswordDialog = false">取 消</el-button>
        <el-button type="primary" @click="onClickPassword">确 定</el-button>
      </div>
    </el-dialog>
    <!-- <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" /> -->

    <!-- <breadcrumb class="breadcrumb-container" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { changePassword } from '@/api/systemUser'
import { getPassword } from '@/utils/auth'

// import Breadcrumb from '@/components/Breadcrumb'
// import Hamburger from '@/components/Hamburger'
export default {
  name: 'NavBar',
  components: {
    // Breadcrumb
    // Hamburger
  },
  data() {
    return {
      mode: [
        {
          name: '基础信息管理',
          path: '/basicInfo/home',
          type: 1,
          flag: 'basicInfo',
          icon: 'dashboard'
        },
        {
          name: '流程管理',
          path: '/process/management',
          type: 2,
          flag: 'process',
          icon: 'process'
        },
        {
          name: '会议管理',
          path: '/meeting',
          type: 3,
          flag: 'meeting',
          icon: 'meeting'
        },
        {
          name: '项目管理',
          path: '/project',
          type: 4,
          flag: 'project_outer',
          icon: 'project'
        },
        {
          name: '培训/知识库管理',
          path: '/training',
          type: 5,
          flag: 'training_repository',
          icon: 'training'
        },
        {
          name: '制度管理',
          path: '/institution',
          type: 6,
          flag: 'institution',
          icon: 'institution'
        }
      ],
      type: 1,
      changePasswordDialog: false,
      changePasswordInfo: {
        oldPassword: null,
        newPassword: null,
        confirmPassword: null
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
        confirmPassword: [{ required: true, message: '请确认新密码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'keyList', 'userId'])
  },
  created() {
    this.type = window.localStorage.getItem('zf_oa_type')
  },
  methods: {
    toggleSideBar() {
      // this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    },
    checkTab(item) {
      this.type = item.type
      this.$store.commit('checkedData/set_data_type', item.type)
      console.log(item.path)
      this.$nextTick(() => {
        if (item.type === 4) {
          if (this.keyList.includes('project')) {
            this.$router.push(item.path)
          } else {
            this.$router.push('/aftermarket')
          }
        } else if (item.type === 5) {
          if (this.keyList.includes('training')) {
            this.$router.push(item.path)
          } else {
            this.$router.push('/repository')
          }
        } else {
          this.$router.push(item.path)
        }
      })
    },
    changePassword() {
      this.changePasswordDialog = true
    },
    onClickPassword() {
      console.log(this.name)
      this.$refs['passwordForm'].validate(async (val) => {
        if (val) {
          if (this.changePasswordInfo.oldPassword === getPassword()) {
            if (this.changePasswordInfo.confirmPassword === this.changePasswordInfo.newPassword) {
              // 通过
              const res = await changePassword({
                userId: this.userId,
                newPassword: this.changePasswordInfo.newPassword,
                oldPassword: this.changePasswordInfo.oldPassword
              })
              this.$message.success('修改密码成功')
              this.changePasswordDialog = false
              // 修改密码后退出登录
              this.$nextTick(() => {
                this.logout()

                this.$message.warning('请重新登录')
              })
              console.log(res)
            } else {
              this.$message.warning('两次密码不一致')
            }
          } else {
            this.$message.warning('原密码不正确')
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 68px;
  width: 100%;
  overflow: hidden;
  position: relative;
  // background: #fff;
  background: url('../../assets/Home/nav_bg.png');
  background-size: cover;
  // box-shadow: 0 1px 4px rgba(0,21,41,.08);

  // .hamburger-container {
  //   line-height: 46px;
  //   height: 100%;
  //   float: left;
  //   cursor: pointer;
  //   transition: background 0.3s;
  //   -webkit-tap-highlight-color: transparent;

  //   &:hover {
  //     background: rgba(0, 0, 0, 0.025);
  //   }
  // }

  // .breadcrumb-container {
  //   float: left;
  // }

  .left_title {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    .logo {
      width: 48px;
      height: 38px;
    }
    .title {
      width: 240px;
      height: 25px;
      padding-left: 8px;
    }
  }
  .right-menu {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    line-height: 50px;
    &:focus {
      outline: none;
    }
    .cut {
      display: flex;
      align-items: center;
      height: 100%;
      font-size: 16px;
      img {
        width: 16px;
        height: 16px;
      }
    }
    ::v-deep {
      .el-dropdown-menu__item {
        .cutTabs {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .messageHint {
      display: flex;
      align-items: center;
      margin-right: 58px;
      .el-icon-message-solid {
        position: relative;
        font-size: 21px;
        &::after {
          position: absolute;
          top: 0;
          right: 3px;
          z-index: 2;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #eb6557;
          content: '';
        }
      }
      .el-icon-caret-bottom {
        color: #606266;
        font-size: 12px;
      }
    }
    .avatar-container {
      margin-right: 30px;

      .cut {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
.el-row {
  height: 100%;
  .el-col {
    height: 100%;
  }
}
.el-dropdown {
  height: 100%;
}
</style>
