<template>
  <div class="future-preview">
    <h3 class="section-title">未来两天预告</h3>

    <div v-if="futureMeetings.length > 0" class="future-container">
      <div v-for="(dayData, index) in futureMeetings" :key="index" class="day-section">
        <div class="day-header">
          <span class="date">{{ dayData.reservedDate }}</span>
          <span class="weekday">{{ weekday(dayData.reservedDate) }}</span>
        </div>

        <div class="meetings-list">
          <div class="meeting-card" :class="dayData.conferenceRoom === 'one' ? 'large-meeting' : 'small-meeting'">
            <div class="meeting-header">
              <svg-icon icon-class="newMeeting" class="room-icon"></svg-icon>
              <span class="meeting-title"> {{ dayData.conferenceTitle }}</span>
              <span class="meeting-time">({{ dayData.startTime }} ~ {{ dayData.endTime }})</span>
            </div>

            <div class="meeting-participants">预约人：{{ dayData.createUserName }}</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="no-future-meetings">
      <img src="@/assets/meeting/empty.png" alt="" />
      <span>未来两天暂无会议安排</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FuturePreview',
  props: {
    futureMeetings: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    weekday(date) {
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const day = new Date(date).getDay()
      return weekDays[day]
    }
  }
}
</script>

<style scoped lang="scss">
.future-preview {
  position: relative;
  width: 100%;
  height: 181px;
  margin-top: 16px;
  padding: 20px 30px 0;
  background: #ffffff;
  border-radius: 14px 14px 14px 14px;
  border: 1px solid #e9e9e9;
  @media (min-height: 970px) {
    height: 200px;
  }
}

.section-title {
  margin: 0;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
}

.future-container {
  display: flex;
  gap: 20px;
  margin-top: 5px;
}

.day-section {
  width: 50%;

  .day-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    @media (min-height: 970px) {
      margin-bottom: 15px;
    }
    .date,
    .weekday {
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #666666;
      margin-right: 8px;
    }
  }
}

.meetings-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meeting-card {
  height: 80px;
  padding: 18px 30px 0 25px;
  border-radius: 8px;
  border-left: 6px solid;
  background: #f9f9f9;

  @media (min-height: 970px) {
    height: 90px;
  }
  &.large-meeting {
    border-left-color: #3465df;
    background: #f7f9fe;
    .meeting-header {
      .room-icon {
        color: #3465df;
      }
      .meeting-time {
        color: #3465df;
      }
    }
  }

  &.small-meeting {
    border-left-color: #13b755;
    background: #f7fdfa;
    .meeting-header {
      .room-icon {
        color: #13b755;
      }
      .meeting-time {
        color: #13b755;
      }
    }
  }

  .meeting-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .room-icon {
      position: relative;
      top: 13px;
      margin-right: 12px;
      font-size: 28px;
    }

    .meeting-title {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      margin-right: 8px;
    }

    .meeting-time {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .meeting-participants {
    padding-left: 40px;
    margin-top: 6px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
}

.no-future-meetings {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666666;
  img {
    height: 110px;
  }
  span {
    position: relative;
    top: -15px;
    font-size: 16px;
  }
}
</style>
