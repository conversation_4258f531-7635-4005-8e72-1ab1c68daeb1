<template>
  <div class="app-container">
    <!-- 搜索查询 -->
    <el-row style="min-width: 1200px; margin-bottom: 20px">
      <el-col :span="24">
        <el-form ref="form" :model="searchForm" label-width="80px" inline>
          <el-form-item label="字典标签" label-width="70px">
            <el-input v-model="searchForm.dictLabel" size="small" placeholder="请输入字典标签" clearable></el-input>
          </el-form-item>
          <el-form-item label="字典状态" label-width="70px">
            <el-select v-model="searchForm.status" placeholder="请选择状态" size="small" clearable>
              <el-option v-for="item in state" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="success" size="small" @click="getdictDataList">查询</el-button>
            <el-button type="primary" size="small" @click="addDictTypeData">新增数据字典</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <!-- 列表展示 -->
    <el-table v-loading="listLoading" :data="dictDataList" element-loading-text="Loading" border fit highlight-current-row>
      <el-table-column label="序号" align="center" width="120" type="index"> </el-table-column>
      <el-table-column label="字典标签" align="center" prop="dictLabel"> </el-table-column>
      <el-table-column label="字典键值" align="center" prop="dictValue"> </el-table-column>
      <el-table-column label="字典排序" align="center" prop="dictSort"> </el-table-column>
      <el-table-column label="字典状态" align="center" prop="status">
        <template v-slot="{ row }">
          <span>{{ row.status | stateHandle }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-button type="warning" size="small" @click="updateDictData(scope.row)">修改字典数据</el-button>
          <el-button type="danger" size="small" @click="deleteDictData(scope.row)">删除字典数据</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加修改dialog -->
    <el-dialog :title="showTitle" :visible.sync="showDialog" width="600px" :close-on-click-modal="false" @close="close">
      <el-form ref="form" :model="DictTypeData" label-width="80px" :rules="rules">
        <el-form-item label="字典类型" prop="dictType"><el-input v-model="DictTypeData.dictType" maxlength="20" placeholder="请输入字典类型" size="small" disabled></el-input></el-form-item>
        <el-form-item label="字典标签" prop="dictLabel"><el-input v-model="DictTypeData.dictLabel" maxlength="20" placeholder="请输入字典名称" size="small"></el-input></el-form-item>
        <el-form-item label="字典键值" prop="dictValue"><el-input v-model="DictTypeData.dictValue" maxlength="20" placeholder="请输入字典键值" size="small"></el-input></el-form-item>
        <el-form-item label="排序" prop="dictSort"><el-input v-model.number="DictTypeData.dictSort" type="number" min="0" placeholder="请输入字典排序" size="small"></el-input></el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="DictTypeData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="DictTypeData.remark" maxlength="300" size="small" placeholder="请输入备注" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="confirmOnClick">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { dictDataList, deleteDictData, addDictData, updateDictData } from '@/api/dictionaries'
export default {
  name: 'DictData',
  data() {
    return {
      listLoading: true,

      dictDataList: [],
      searchForm: {
        dictType: null,
        dictLabel: null,
        status: null,
        pageNum: 1,
        pageSize: 5
      },
      state: [
        {
          label: '启用',
          value: 1
        },
        {
          label: '禁用',
          value: 0
        }
      ],
      showDialog: false,
      DictTypeData: {
        dictType: null,
        dictLabel: null,
        dictValue: null,
        dictSort: null,
        status: 0,
        remark: null
      },
      rules: {
        dictLabel: [{ required: true, tiggle: 'blur', message: '字典标签不能为空' }],
        dictType: [{ required: true, tiggle: 'blur', message: '字典类型不能为空' }],
        dictValue: [{ required: true, tiggle: 'blur', message: '字典键值不能为空' }],
        dictSort: [{ min: 0, type: 'number', tiggle: 'blur', message: '排序不得小于0' }]
      }
    }
  },
  computed: {
    showTitle() {
      return this.DictTypeData.dictDataId ? '修改字典数据' : '添加字典数据'
    }
  },
  created() {
    this.searchForm.dictType = this.$route.params.dictType
    this.DictTypeData.dictType = this.$route.params.dictType
    this.getdictDataList()
  },
  methods: {
    async getdictDataList() {
      const { data } = await dictDataList(this.searchForm)
      console.log(data)
      this.dictDataList = data
      this.listLoading = false
    },
    addDictTypeData() {
      this.showDialog = true
    },
    updateDictData(row) {
      console.log(row)
      this.showDialog = true
      this.DictTypeData = row
    },
    async deleteDictData(row) {
      try {
        await this.$confirm('确定要删除该字典数据吗, 是否继续?', '删除字典数据', {
          type: 'warning'
        })
        await deleteDictData({ dictDataId: row.dictDataId })
        this.getdictDataList()
        this.$message.success('删除成功')
      } catch (err) {
        return new Error(err)
      }
      console.log(row)
    },
    lookDictType(row) {
      this.$router.push(`/dictionaries/data/${row.dictType}`)
    },
    close() {
      this.DictTypeData = {
        dictType: this.$route.params.dictType,
        dictLabel: null,
        dictValue: null,
        dictSort: null,
        status: 0,
        remark: null
      }
      this.$refs['form'].resetFields()
      this.getdictDataList()
      this.showDialog = false
    },
    // 点击确认后触发的事件
    async confirmOnClick() {
      if (this.DictTypeData.dictDataId) {
        // 有id代表修改
        const res = await updateDictData(this.DictTypeData)
        console.log(res)
        this.$message.success('修改成功')
        this.close()
      } else {
        this.$refs['form'].validate((val) => {
          if (val) {
            addDictData(this.DictTypeData)
              .then((res) => {
                console.log(res)
                this.$message.success('添加成功')
                this.close()
              })
              .catch((err) => {
                console.log(err)
              })
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="sass"></style>
