<template>
  <div class="app-container">
    <el-row :gutter="10" class="top">
      <el-col :span="24">
        <i class="el-icon-location"></i>
        <span>当前位置：销售方案 /</span>
        <span>方案详情</span>
      </el-col>
    </el-row>
    <div class="box">
      <div class="header">
        <span>方案详情</span>
        <div>
          <el-button type="warning" icon="el-icon-edit" size="small" @click="goEdit">编辑</el-button>
          <el-button type="primary" icon="el-icon-download" size="small" @click="exportData('word')">导出Word</el-button>
          <el-button type="primary" icon="el-icon-download" size="small" @click="exportData('excel')">导出Excel</el-button>
        </div>
      </div>
      <el-descriptions v-if="info" :column="1" style="margin-left: 118px; margin-top: 28px">
        <el-descriptions-item label="方案名称">
          <span class="name">{{ info.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item v-if="organizationId !== 61311410" label="客户名称">
          <span>{{ info.customerName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户预算（万元）">{{ info.budget }}</el-descriptions-item>
        <el-descriptions-item label="产品列表">
          <div v-if="info.productDetailDtos.length" class="productList">
            <div class="total">合计（万元）: {{ info.totalMoney }}</div>
            <el-table :data="info.productReqs" style="width: 1064px" border header-cell-class-name="tableHeader">
              <el-table-column prop="name" align="center" label="产品名称" width="width"> </el-table-column>
              <el-table-column prop="offer" align="center" label="单价(万元)" width="100">
                <template v-slot="{ row }">
                  <span>{{ row.offer === '-1' ? '待定' : row.offer }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" label="单位" width="100">
                <template v-slot="{ row }">
                  <span>{{ row.unit | softwareUnit }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="number" align="center" label="需求数量" width="100"> </el-table-column>
              <el-table-column prop="totalOffer" align="center" label="总价(万元)" width="100">
                <template v-slot="{ row }">
                  <span>{{ row.offer === '-1' ? 0 : row.offer * row.number }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="showContent" align="center" show-overflow-tooltip :label="showContentLabel" width="width"> </el-table-column>
              <el-table-column prop="remark" align="center" show-overflow-tooltip label="备注" width="width"> </el-table-column>
            </el-table>
          </div>
        </el-descriptions-item>
        <el-descriptions-item>
          <span slot="label">备 <i v-html="'&emsp;&nbsp;'"></i> 注</span>
          <span>{{ info.remark }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
import { tSalesPlanDetail, productExcelExport } from '@/api/tSalesPlan'
import { softwareUnit } from '@/filters'
import { mapGetters } from 'vuex'
import { ExportBriefDataDocx } from '@/vendor/ExportWord.js'

export default {
  name: '',
  data() {
    return {
      info: null,
      totalPrices: 0,
      wordUrl: window.config.VUE_APP_DOWNLOAD_URL + '/cloudFile/workmanage/text.docx'
    }
  },
  computed: {
    showContentLabel() {
      return this.info.isShow === 1 ? '简介' : '参数'
    },
    ...mapGetters(['organizationId'])
  },
  watch: {
    'info.productDetailDtos': {
      deep: true,
      handler(val) {
        if (val) {
          let prices = 0
          val.forEach((item) => {
            item.totalOffer = item.offer * item.number
            prices += parseFloat(item.totalOffer)
          })
          this.totalPrices = prices
        }
      }
    }
  },
  created() {
    this.getDetails()
  },
  methods: {
    async getDetails() {
      const { data } = await tSalesPlanDetail({ id: this.$route.params.id })
      this.info = { ...data, productReqs: data.productDetailDtos }
    },
    async exportData(type) {
      if (type === 'word') {
        // const { data: url } = await productWordExport({ id: this.$route.params.id })
        // window.location.href = url
        const tableData = this.info.productDetailDtos.map((item, index) => {
          console.log(item)
          return {
            index: index + 1,
            name: item.name,
            number: item.number,
            unit: item.unit === 1 ? '套' : item.unit === 2 ? '台' : item.unit === 3 ? '个' : item.unit === 4 ? '端' : '把',
            offer: item.offer === '-1' ? '待定' : item.offer,
            totalOffer: item.totalOffer === -1 ? 0 : item.totalOffer,
            showContent: item.showContent ? item.showContent : '',
            remark: item.remark
          }
        })
        ExportBriefDataDocx(this.wordUrl, { tableData, contentType: this.showContentLabel }, `${this.info.name}.docx`)
      } else {
        const { data } = await productExcelExport({ id: this.$route.params.id })
        const content = data[0].isShow === 1 ? '简介' : '参数'
        const headers = {
          index: '序列',
          name: '产品名称',
          showContent: content,
          offer: '单价(万元)',
          unit: '单位',
          number: '数量',
          totalOffer: '总价(万元)',
          remark: '备注'
        }
        const res = this.formatJson(headers, data)
        import('@/vendor/Export2Excel').then((excel) => {
          excel.export_json_to_excel({
            header: Object.values(headers), // 表头 必填
            data: res, // 具体数据 必填
            filename: this.info.name // 非必填
          })
        })
      }
    },
    formatJson(headers, rows) {
      return rows.map((item, index) => {
        return Object.keys(headers).map((key) => {
          if (key === 'unit') {
            return softwareUnit(item[key])
          } else if (key === 'index') {
            return index + 1
          } else if (key === 'offer' || key === 'number' || key === 'totalOffer') {
            if (key === 'offer') {
              item[key] = item[key] === '-1' ? '待定' : item[key]
              return item[key]
            } else if (key === 'totalOffer') {
              item[key] = item[key] === '-1' ? 0 : item[key]
            }
            return parseFloat(item[key])
          }
          return item[key]
        })
      })
    },
    goEdit() {
      this.$router.replace(`/tSalesPlan/add/${this.$route.params.id}`)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 0;
  background-color: #e8eaed;
  min-height: 100%;
  padding-top: 58px;
  padding-bottom: 10px;
  .top {
    position: absolute;
    top: 0px;
    width: 100%;
    background-color: #e8eaed;
    padding: 24px 0 16px 0;
    z-index: 999;
    .el-col {
      i {
        font-size: 14px;
        color: #657081;
      }
      span {
        &:first-of-type {
          margin: 0 5px;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
        }
        &:last-of-type {
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
      }
    }
  }
  .box {
    width: 1754px;
    height: 791px;
    background: #ffffff;
    padding-bottom: 40px;
    border-radius: 8px 8px 8px 8px;
    overflow: auto;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24px 0 11px 48px;
      border-bottom: 1px solid #eeeeef;
      font-size: 16px;
      font-family: Microsoft YaHei-Bold, Microsoft YaHei;
      font-weight: bold;
      color: #0b1a44;
      & > div {
        margin-right: 24px;
      }
    }
    .productList {
      width: 1064px;
      text-align: left;
      .total {
        margin-bottom: 16px;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .offerInput,
      .numberInput {
        .el-input__inner {
          width: 120px;
          height: 32px;
          background: #f5f5f5;
          border: 1px solid #d8dbe1;
          color: #0b1a44;
          text-align: center;
        }
      }
      .delButton {
        font-size: 14px;
        color: #eb6557;
        cursor: pointer;
      }
    }
    ::v-deep {
      .tableHeader {
        background: #f0f0f0;
        font-size: 14px;
        font-family: Microsoft YaHei-Bold, Microsoft YaHei;
        font-weight: bold;
        color: #0b1a44;
      }
      .el-table td.el-table__cell,
      .el-table th.el-table__cell.is-leaf {
        border-color: #a3a8bb;
      }
      .el-table--border,
      .el-table--group {
        border-color: #a3a8bb;
      }
      .el-table--border::after,
      .el-table--group::after,
      .el-table::before {
        background-color: #a3a8bb;
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
        background: #eff1f3;
      }
      .el-descriptions-item__label {
        width: 128px;
        text-align: right;
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #868b9f;
      }
      .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
        padding-bottom: 28px;
      }
      .el-descriptions-item__content {
        font-size: 14px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #0b1a44;
      }
      .name {
        font-weight: bold;
        color: #0b1a44;
      }
    }
  }
}
</style>
