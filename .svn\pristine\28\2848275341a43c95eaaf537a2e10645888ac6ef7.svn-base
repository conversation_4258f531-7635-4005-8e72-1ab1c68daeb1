const apiKey = 'sk-cedb691114764f4c90da4aed02653aed' // 替换为实际 API Key
const model = 'qwen-turbo-2025-07-15' // 或其他百炼支持的模型

export async function callBailianAPI(systemPrompt, userInput) {
  try {
    const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `<PERSON><PERSON> ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userInput }
        ]
      })
    })
    const data = await response.json()
    return data.choices[0].message.content
  } catch (error) {
    console.error('API调用失败:', error)
  }
}
