<template>
  <!--  批量修改销售负责人 -->
  <div class="UpdateUser">
    <el-dialog title="批量修改销售负责人" :visible.sync="dialogVisible" width="1200px" @close="close">
      <div class="content">
        <el-card class="left">
          <div slot="header">销售负责人</div>
          <section v-for="item in userList" :key="item.userId" :class="{ checkedUser: userId === item.userId }" @click="checkedHandle(item)">
            {{ item.realName }}
          </section>
        </el-card>
        <el-card class="right">
          <div slot="header"><el-checkbox v-show="userId" v-model="allContract" @change="allChecked">所有合同</el-checkbox></div>
          <div v-show="userId">
            <el-row v-for="item in contractList" :key="item.contractId">
              <el-checkbox v-model="item.checked" @change="set_checkedItem(item)">{{ item.contractName }}</el-checkbox>
            </el-row>
          </div>
        </el-card>
      </div>
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getList } from '@/api/systemUser'
import { contractGetAllContract, contractUpdateHeadUsers, contractGetContactByHeaduser } from '@/api/contractNew'
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      userList: [],
      contractList: [],
      userId: null,
      allContract: false
    }
  },
  created() {},
  methods: {
    openDialog() {
      this.dialogVisible = true
      this.getUserList()
      this.getContractAll()
    },
    // 获取所有销售人员
    async getUserList() {
      const { data } = await getList({ organizationId: '56642510', pageNum: 1, pageSize: 200 })
      this.userList = data.list
    },
    // 获取所有合同
    async getContractAll() {
      const { data } = await contractGetAllContract()
      this.contractList = data
      this.contractList.forEach((item) => {
        this.$set(item, 'checked', false)
      })
    },
    async checkedHandle(item) {
      this.userId = item.userId
      const { data } = await contractGetContactByHeaduser(this.userId)
      if (data && data.length) {
        const ids = data.map((item) => item.contractId)
        this.contractList.forEach((item) => {
          if (ids.includes(item.contractId)) {
            item.checked = true
          } else {
            item.checked = false
          }
        })
      } else {
        this.contractList.forEach((item) => (item.checked = false))
      }
      this.allContract = this.contractList.every((item) => item.checked)
      console.log(this.allContract)
    },
    async allChecked(val) {
      this.contractList.forEach((item) => {
        if (val) {
          item.checked = true
        } else {
          item.checked = false
        }
      })
      if (val) {
        const contractIds = this.contractList.filter((item) => item.checked).map((item) => item.contractId)
        contractUpdateHeadUsers({ headUser: this.userId, contractIds })
      } else {
        contractUpdateHeadUsers({ headUser: this.userId, contractIds: [] })
      }
    },
    set_checkedItem() {
      const isAllChecked = this.contractList.some((list) => list.checked === false)
      if (isAllChecked) {
        this.allContract = false
      } else {
        this.allContract = true
      }
      const contractIds = this.contractList.filter((item) => item.checked).map((item) => item.contractId)
      contractUpdateHeadUsers({ headUser: this.userId, contractIds })
    },
    close() {
      this.userId = null
      this.allContract = false
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-dialog {
    .el-dialog__body {
      .content {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .el-card {
          .el-card__header,
          .el-checkbox__label {
            font-size: 16px;
            color: #1a1a1a;
          }
          &:first-of-type {
            flex: 1;
            margin-right: 15px;
            .el-card__body {
              padding: 10px;
              padding-left: 20px;
              padding-top: 18px;
              max-height: 500px;
              overflow: auto;
              &::-webkit-scrollbar {
                width: 3px;
              }
              // 里面的滑块
              &::-webkit-scrollbar-thumb {
                background: #d2d2d2;
              }
              // 外面的背景
              &::-webkit-scrollbar-track-piece {
                background: transparent;
              }
            }
          }
          &:last-of-type {
            flex: 5;
            .el-card__body {
              padding: 10px;
              max-height: 500px;
              overflow: auto;
              &::-webkit-scrollbar {
                width: 3px;
              }
              // 里面的滑块
              &::-webkit-scrollbar-thumb {
                background: #d2d2d2;
              }
              // 外面的背景
              &::-webkit-scrollbar-track-piece {
                background: transparent;
              }
              .el-row {
                display: flex;
                align-items: center;
                // margin: 15px 0;
                height: 35px;
              }
            }
          }
          .el-checkbox {
            margin-right: 5px;
            .el-checkbox__inner {
              width: 20px;
              height: 20px;
              &::after {
                left: 6px;
                height: 11px;
                width: 4px;
              }
            }
          }
        }
        .left {
          section {
            width: 100%;
            height: 35px;
            font-size: 18px;
            cursor: pointer;
            line-height: 35px;
            padding-left: 5px;
            color: #333;
            &:hover {
              background: #3464e0;
              color: #fff;
            }
          }
          .checkedUser {
            background: #3464e0;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
