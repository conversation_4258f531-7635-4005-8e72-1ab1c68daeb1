<template>
  <div>
    <el-drawer title="反馈记录" :visible.sync="drawer" :show-close="false" size="832px" custom-class="feedbackRecord" :direction="direction">
      <ul class="feedbackList">
        <li v-for="item in feedbackList" :key="item.feedbackId">
          <div>
            <span>{{ item.createTime }}</span>
            <span style="margin-left: 15px">创建人：{{ item.realName }}</span>

            <span v-if="item.isSolve === 1" style="margin-left: 15px">解决人：{{ item.solveUserName }}</span>
            <el-button v-else type="text" plain style="margin-left: 15px" @click="solve(item.feedbackId)">解决</el-button>
          </div>
          <div v-html="item.remark"></div>
        </li>
      </ul>
    </el-drawer>
  </div>
</template>
<script>
import { emulationFeedbackList, emulation_solveFeedback } from '@/api/emulation'
export default {
  name: '',

  data() {
    return {
      drawer: false,
      direction: 'rtl',
      feedbackList: [],
      emulationId: null
    }
  },
  methods: {
    async open(emulationId) {
      const { data } = await emulationFeedbackList({ emulationId: emulationId })
      this.emulationId = emulationId
      this.feedbackList = data
      this.drawer = true
    },
    close() {
      this.drawer = false
    },
    async solve(feedbackId) {
      this.$confirm('是否解决该问题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await emulation_solveFeedback(feedbackId)
          this.$message.success('解决成功')
          this.open(this.emulationId)
        })
        .catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .feedbackRecord {
    .el-drawer__header {
      padding: 32px 26px;
      margin-bottom: 0;
      font-size: 18px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-drawer__body {
      padding: 0 24px 0 26px;
      height: calc(100% - 85px);
      overflow: auto;
    }
    .feedbackList {
      border-left: 1px solid #eeeeef;
      li {
        position: relative;
        padding-left: 11px;
        margin-bottom: 28px;
        & > div:first-of-type {
          &::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 3px;
            width: 8px;
            height: 8px;
            background: #b1bac7;
            border-radius: 8px;
          }
          & > span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
          }
          & > span:last-of-type {
            margin-left: 14px;
          }
          .el-button {
            padding: 0;
            border: none !important;
          }
        }
        & > div:last-of-type {
          padding: 16px;
          margin-top: 6px;
          width: 774px;
          background: #eef1f3;
          border-radius: 6px;
          font-size: 15px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
          line-height: 23px;
          img {
            max-width: 100%;
          }
        }
      }
    }
  }
}
</style>
