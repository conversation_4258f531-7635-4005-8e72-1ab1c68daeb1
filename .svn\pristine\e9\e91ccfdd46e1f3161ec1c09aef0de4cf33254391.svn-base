<template>
  <div>
    <el-drawer title="反馈记录" :visible.sync="drawer" :show-close="false" size="832px" custom-class="feedbackRecord" :direction="direction">
      <ul class="feedbackList">
        <li v-for="item in feedbackList" :key="item.feedbackId">
          <div>
            <span>{{ item.createTime }}</span>
            <span style="margin-left: 15px">{{ item.realName }}</span>
          </div>
          <div v-html="item.remark"></div>
        </li>
      </ul>
    </el-drawer>
  </div>
</template>
<script>
import { emulationFeedbackList } from '@/api/emulation'
export default {
  name: '',

  data() {
    return {
      drawer: false,
      direction: 'rtl',
      feedbackList: []
    }
  },
  methods: {
    async open(emulationId) {
      const { data } = await emulationFeedbackList({ emulationId: emulationId })
      this.feedbackList = data
      this.drawer = true
    },
    close() {
      this.drawer = false
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .feedbackRecord {
    .el-drawer__header {
      padding: 32px 26px;
      margin-bottom: 0;
      font-size: 18px;
      font-family: Microsoft YaHei-Regular, Microsoft YaHei;
      font-weight: 400;
      color: #0b1a44;
    }
    .el-drawer__body {
      padding: 0 24px 0 26px;
      height: calc(100% - 85px);
      overflow: auto;
    }
    .feedbackList {
      border-left: 1px solid #eeeeef;
      li {
        position: relative;
        padding-left: 11px;
        margin-bottom: 28px;
        & > div:first-of-type {
          &::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 3px;
            width: 8px;
            height: 8px;
            background: #b1bac7;
            border-radius: 8px;
          }
          span {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #a3a8bb;
          }
          & > span:last-of-type {
            margin-left: 14px;
          }
        }
        & > div:last-of-type {
          padding: 16px;
          margin-top: 6px;
          width: 774px;
          background: #eef1f3;
          border-radius: 6px;
          font-size: 15px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #0b1a44;
          line-height: 23px;
          img {
            max-width: 100%;
          }
        }
      }
    }
  }
}
</style>
