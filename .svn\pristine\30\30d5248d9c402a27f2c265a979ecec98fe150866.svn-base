<template>
  <div class="today-overview">
    <!-- 今日概览 -->
    <div class="today-section">
      <h3 class="section-title">今日概览</h3>
      <div class="current-date">{{ currentDateStr }}</div>

      <div class="meeting-section">
        <h4 class="subsection-title">下一场会议</h4>

        <div v-if="todayMeetings" class="meeting-list">
          <div class="meeting-item" :class="roomTypeClass">
            <div class="meeting-header">
              <svg-icon icon-class="newMeeting" class="meeting-icon"></svg-icon>
              <el-tooltip effect="dark" :content="todayMeetings.conferenceTitle" placement="top">
                <span class="meeting-title"> {{ todayMeetings.conferenceTitle }}</span>
              </el-tooltip>
              <span class="meeting-time">（{{ todayMeetings.startTime }} ~ {{ todayMeetings.endTime }}）</span>
            </div>

            <div class="meeting-participants">预约人: {{ todayMeetings.createUserName }}</div>
          </div>
        </div>
        <div v-else class="no-meetings">
          <img src="@/assets/meeting/empty.png" alt="" />
          <span>今日暂无会议安排</span>
        </div>
      </div>
    </div>

    <!-- 空闲建议 -->
    <div class="free-section">
      <h3 class="section-title">空闲建议</h3>

      <div v-if="processedFreeMeetings.length > 0" class="free-list">
        <div v-for="(room, index) in processedFreeMeetings" :key="index" class="free-item" :class="room.roomType === 'large' ? 'large-room-item' : 'small-room-item'">
          <svg-icon icon-class="newMeeting" class="room-icon"></svg-icon>

          <div class="room-center">
            <div class="room-header">
              <span class="room-title">{{ room.title }}</span>
              <span class="room-time">({{ room.time }})</span>
            </div>
            <div class="room-duration">{{ room.duration }}分钟</div>
          </div>

          <div class="room-action" @click="handleFreeRoomClick(room)">
            <svg-icon icon-class="reservation"></svg-icon>
            预约
          </div>
        </div>
      </div>

      <div v-else class="no-free-rooms">
        <img src="@/assets/meeting/empty.png" alt="" />
        <span>暂无空闲时段</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TodayOverview',
  props: {
    checkedDate: {
      type: String,
      default: ''
    },
    todayMeetings: {
      type: Object,
      default: () => {}
    },
    freeMeetings: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    currentDateStr() {
      const today = this.checkedDate ? new Date(this.checkedDate) : new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const date = String(today.getDate()).padStart(2, '0')
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekDay = weekDays[today.getDay()]

      return `${year}-${month}-${date} ${weekDay}`
    },
    roomTypeClass() {
      return this.todayMeetings.conferenceRoom === 'one' ? 'large-room' : 'small-room'
    },
    // 处理空闲建议数据
    processedFreeMeetings() {
      if (!this.freeMeetings || typeof this.freeMeetings !== 'object') {
        return []
      }

      const result = []

      // 处理大会议室数据
      if (this.freeMeetings.one && Array.isArray(this.freeMeetings.one)) {
        this.freeMeetings.one.forEach((timeSlot) => {
          const duration = this.calculateDuration(timeSlot)
          if (duration >= 60) {
            // 只显示60分钟以上的空闲时段
            result.push({
              roomType: 'large',
              title: '大会议室',
              time: timeSlot,
              duration: duration,
              startTime: timeSlot.split('-')[0],
              endTime: timeSlot.split('-')[1]
            })
          }
        })
      }

      // 处理小会议室数据
      if (this.freeMeetings.two && Array.isArray(this.freeMeetings.two)) {
        this.freeMeetings.two.forEach((timeSlot) => {
          const duration = this.calculateDuration(timeSlot)
          if (duration >= 60) {
            // 只显示60分钟以上的空闲时段
            result.push({
              roomType: 'small',
              title: '小会议室',
              time: timeSlot,
              duration: duration,
              startTime: timeSlot.split('-')[0],
              endTime: timeSlot.split('-')[1]
            })
          }
        })
      }

      return result
    }
  },

  methods: {
    // 计算时间段的分钟数
    calculateDuration(timeSlot) {
      const [startTime, endTime] = timeSlot.split('-')
      const start = this.parseTime(startTime)
      const end = this.parseTime(endTime)
      return Math.round((end - start) * 60)
    },

    // 解析时间字符串为小时数
    parseTime(timeStr) {
      const [hour, minute] = timeStr.split(':').map(Number)
      return hour + (minute || 0) / 60
    },

    // 处理空闲时段点击
    handleFreeRoomClick(room) {
      console.log(room)

      // 检查是否可以预约（不能预约过去的时间）
      if (!this.canBookTime(room.startTime)) {
        this.$message.warning('不能预约过去的时间')
        return
      }
      this.$emit('free-room-click', {
        roomType: room.roomType,
        startTime: room.startTime,
        endTime: room.endTime
      })
    },
    canBookTime() {
      const now = new Date()
      const currentDate = now.toISOString().split('T')[0] // 当前日期 YYYY-MM-DD

      // 如果选择的日期是今天之前，不能预约
      if (this.checkedDate < currentDate) {
        return false
      }

      // 未来日期可以预约
      return true
    }
  }
}
</script>

<style scoped lang="scss">
.today-overview {
  display: flex;
  align-items: center;
  gap: 20px;
}

.today-section,
.free-section {
  position: relative;
  height: 206px;
  padding: 20px 30px;
  background: #ffffff;
  border-radius: 14px 14px 14px 14px;
  border: 1px solid #e9e9e9;
  overflow: hidden;
  @media (min-height: 970px) {
    height: 220px;
  }
}
.today-section {
  width: 396px;
}
.free-section {
  flex: 1;
  padding-right: 30px;
}

.section-title {
  margin: 0;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
}

.current-date {
  position: absolute;
  top: 28px;
  right: 38px;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #666666;
}

.subsection-title {
  margin: 16px 0 17px 0;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #555555;
}

.meeting-list,
.free-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.free-list {
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 10px;
  row-gap: 12px;
}

.meeting-item {
  height: 80px;
  padding: 18px 0 0 25px;
  background: #f6fcf9;
  border-radius: 8px 8px 8px 8px;
  border-left: 6px solid #13b755;
  @media (min-height: 950px) {
    height: 90px;
  }
  &.large-room {
    background: #f7f9fe;
    border-left-color: #3465df;
    .meeting-header {
      .meeting-icon,
      .meeting-time {
        color: #3465df;
      }
    }
  }
  .meeting-header {
    display: flex;
    align-items: center;

    .meeting-icon {
      position: relative;
      top: 10px;
      margin-right: 12px;
      font-size: 28px;
      color: #13b755;
    }

    .meeting-title {
      max-width: 140px;
      font-weight: 500;
      font-size: 16px;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .meeting-time {
      color: #13b755;
      font-size: 14px;
    }
  }

  .meeting-participants {
    margin-top: 6px;
    padding-left: 40px;
    font-size: 14px;
    color: #666;
  }

  .meeting-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .duration-info {
      font-size: 12px;
      color: #999;
    }
  }
}

.free-item {
  position: relative;
  display: flex;
  align-items: center;
  width: 380px;
  height: 60px;
  padding-left: 28px;
  background: #f7fdfa;
  border-radius: 14px 14px 14px 14px;
  border-left: 6px solid #13b755;
  @media (min-height: 950px) {
    height: 65px;
  }
  &.large-room-item {
    border-left-color: #4285f4;
    background: #f7f9fe;
    .room-icon {
      color: #3465df;
    }
    .room-center {
      .room-header {
        .room-time {
          color: #3465df;
        }
      }
    }
    .room-action {
      border-color: #3465df;
      color: #3465df;
      .svg-icon {
        color: #3465df;
      }
    }
  }
  .room-icon {
    margin-right: 8px;
    font-size: 28px;
    color: #13b755;
  }
  .room-center {
    display: flex;
    flex-direction: column;
  }
  .room-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 16px;
    .room-title {
      color: #333333;
      margin-right: 8px;
    }

    .room-time {
      color: #13b755;
      font-size: 14px;
    }
  }
  .room-duration {
    font-size: 12px;
    color: #555;
  }

  .room-action {
    position: absolute;
    right: 20px;
    top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 89px;
    height: 36px;
    background: #ffffff;
    border-radius: 8px 8px 8px 8px;
    border: 1px dashed #13b755;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #13b755;
    cursor: pointer;
    .svg-icon {
      margin-right: 4px;
      font-size: 18px;
      color: #13b755;
    }
  }
}

.no-meetings,
.no-free-rooms {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // padding: 40px 20px;
  color: #666666;

  img {
    height: 110px;
  }
  span {
    position: relative;
    top: -15px;
    font-size: 16px;
  }
}
</style>
