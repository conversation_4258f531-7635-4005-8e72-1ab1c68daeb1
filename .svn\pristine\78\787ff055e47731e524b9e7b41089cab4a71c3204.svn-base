<template>
  <div class="app-container">
    <div class="libarary">
      <div class="header">
        <div>
          <div class="title">资源库管理</div>
          <img src="@/assets/library/addButton.png" alt="" @click="add" />
        </div>
        <el-form ref="form" :model="queryInfo" label-width="80px" inline>
          <el-form-item label="资源名称:" class="name">
            <el-input v-model="queryInfo.resourceName" size="small" maxlength="30" placeholder="请输入资源名称"></el-input>
          </el-form-item>
          <el-form-item label="专业:">
            <el-select v-model="queryInfo.majorId" placeholder="请选择专业" @focus="getMajorList" @change="getList">
              <el-option label="全部" :value="null"> </el-option>
              <el-option v-for="item in majorList" :key="item.majorId" :label="item.majorName" :value="item.majorId"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目:">
            <el-select v-model="queryInfo.projectId" placeholder="请选择项目" @focus="getProjectList" @change="getList">
              <el-option label="全部" :value="null"> </el-option>
              <el-option v-for="item in projectList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上传人:" class="createUser">
            <el-input v-model="queryInfo.createUser" size="small" maxlength="30" placeholder="请输入上传人"></el-input>
          </el-form-item>
          <el-form-item label="文件格式:" class="modelType">
            <el-select v-model="queryInfo.modelType" placeholder="请选择文件格式" @change="getList">
              <el-option label="全部" :value="null"> </el-option>
              <el-option label="FBX" :value="1"> </el-option>
              <el-option label="GLB" :value="2"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资源类型:" class="type" label-width="90px">
            <el-select v-model="queryInfo.type" placeholder="请选择资源类型" @change="getList">
              <el-option label="全部" :value="null"> </el-option>
              <el-option label="模型" :value="1"> </el-option>
              <el-option label="场景" :value="2"> </el-option>
              <el-option label="动画" :value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="日期查询:">
            <el-date-picker v-model="date" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :value-format="'yyyy-MM-dd'" @change="dateChange">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="button">
            <el-button size="small" type="primary" @click="getList">查询</el-button>
            <el-button size="small" type="primary" plain @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="contentBox">
        <div v-for="(item, itemIndex) in list" :key="item.resourceId" @click="detalis(item)">
          <div class="top">
            <div class="definition">{{ item.quality | quality }}</div>
            <img v-if="item.coverFiles" class="coverImg" :src="item.coverFiles[item.imgIndex].fileUrl" alt="" />
            <div v-if="item.coverFiles && item.coverFiles.length > 1" class="previewBox">
              <div class="leftButton">
                <img v-if="item.imgIndex !== 0" src="@/assets/library/left_button_1.png" alt="" @click.stop="previewImg('up', item, itemIndex)" />
                <img v-else src="@/assets/library/left_button_2.png" alt="" />
              </div>
              <div class="preview">
                <div ref="moveBox">
                  <img v-for="(img, index) in item.coverFiles" :key="index" :src="img.fileUrl" alt="" :class="{ checked: item.imgIndex === index }" />
                </div>
              </div>
              <div class="rightButton">
                <img v-if="item.imgIndex !== item.coverFiles.length - 1" src="@/assets/library/right_button_1.png" alt="" @click.stop="previewImg('down', item, itemIndex)" />
                <img v-else src="@/assets/library/right_button_2.png" alt="" />
              </div>
            </div>
            <div class="operation">
              <template v-if="item.status === 1">
                <img src="@/assets/library/editButton.png" alt="" @click.stop="edit(item)" />
                <img src="@/assets/library/removeButton.png" alt="" @click.stop="del(item)" />
              </template>
              <template v-else>
                <img src="@/assets/library/editButton_disabled.png" alt="" @click.stop />
                <img src="@/assets/library/removeButton_disabled.png" alt="" @click.stop />
              </template>
              <!-- <img src="@/assets/library/historyButton.png" alt="" @click="detalis(item)" /> -->
            </div>
          </div>
          <div class="center">{{ item.majorName }} <i>|</i> {{ item.projectName }}</div>
          <div class="bottom">
            <div>
              <div>
                <span class="code">{{ item.code }}</span>
                <span>{{ item.resourceName }}</span>
              </div>
              <div>
                {{ item.type | resourceType }}
              </div>
            </div>
            <div>{{ item.description }}</div>
            <div>
              <div>
                <img :src="item.headurl" alt="" />
                <span>{{ item.realName }}</span>
              </div>
              <div>{{ formatTime(item.createTime) }}</div>
            </div>
          </div>
        </div>
      </div>
      <el-pagination
        v-if="list.length > 0"
        layout="total,  prev, pager, next"
        background
        :total="total"
        :page-size.sync="queryInfo.pageSize"
        :current-page.sync="queryInfo.pageNum"
        @size-change="getList"
        @current-change="getList"
      />
    </div>
    <!-- 删除弹窗 -->
    <el-dialog :visible.sync="delDialog" width="413px">
      <template v-slot:title>
        <img src="@/assets/library/hint.png" alt="" class="hint" />
        <span class="hintText">提示</span>
      </template>
      <div class="delText">确定删除该资源吗？</div>
      <el-form label-width="80px" style="margin-top: 15px">
        <el-form-item label="备注" required class="description">
          <el-input v-model="delInfo.remark" type="textarea" placeholder="请输入备注说明" maxlength="150"></el-input>
        </el-form-item>
      </el-form>
      <div class="operate">
        <span class="closeButton" @click="closeDel">取 消</span>
        <span class="confirmButton" @click="confirmDel">确 定</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { resourceList, resourceRemove } from '@/api/Library'
import { allMajor } from '@/api/specialty'
import { allProjectList } from '@/api/project'
import { formatTime } from '@/filters'
export default {
  name: '',
  data() {
    return {
      queryInfo: {
        resourceName: null, // 资源名称
        modelType: null, // 文件格式
        type: null, // 资源类型 1 模型 2 场景 3 动画
        majorId: null, // 专业id
        projectId: null, // 项目id
        createUser: null, // 上传人
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        pageNum: 1,
        pageSize: 8
      },
      date: null,
      list: [],
      total: 0,
      majorList: [],
      projectList: [],
      delDialog: false,
      delInfo: {
        id: null,
        remark: null
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await resourceList(this.queryInfo)
      this.list = data.list
      this.list.forEach((item) => {
        this.$set(item, 'imgIndex', 0)
        // item.imgIndex = 0
        // this.$set(item, 'imgIndex', 0)
      })
      console.log(this.list)
      this.total = data.total
    },
    // 获取专业列表
    async getMajorList() {
      const { data } = await allMajor()
      this.majorList = data
    },
    // 获取项目列表
    async getProjectList() {
      const { data } = await allProjectList()
      this.projectList = data
    },
    dateChange(val) {
      if (val) {
        this.queryInfo.startTime = val[0]
        this.queryInfo.endTime = val[1]
      } else {
        this.queryInfo.startTime = null
        this.queryInfo.endTime = null
      }
    },
    reset() {
      this.queryInfo = {
        resourceName: null, // 资源名称
        modelType: null, // 文件格式
        type: null, // 资源类型 1 模型 2 场景 3 动画
        majorId: null, // 专业id
        projectId: null, // 项目id
        createUser: null, // 上传人
        startTime: null, // 开始时间
        endTime: null, // 结束时间
        pageNum: 1,
        pageSize: 8
      }
      this.date = null
      this.getList()
    },
    add() {
      this.$router.push(`/library/add/${0}`)
    },
    async del(row) {
      this.delDialog = true
      this.delInfo.id = row.resourceId
    },
    confirmDel() {
      if (!this.delInfo.remark) {
        return this.$message.warning('备注不能为空')
      }
      const loading = this.$loading({
        text: '正在删除，请稍后',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      resourceRemove(this.delInfo)
        .then(() => {
          loading.close()
          this.$message.success('删除成功')
          this.getList()
          this.delDialog = false
          this.delInfo = {
            id: null,
            remark: null
          }
        })
        .catch(() => {
          loading.close()
          this.$message.error('删除失败,请重试')
        })
    },
    closeDel() {
      this.delDialog = false
      this.delInfo = {
        id: null,
        remark: null
      }
    },
    formatTime(val) {
      const time = Date.parse(val)
      return formatTime(time)
    },
    edit(item) {
      this.$router.push(`/library/add/${item.resourceId}`)
    },
    previewImg(type, item, index) {
      let num = 0
      this.list.forEach((li, liIndex) => {
        if (liIndex <= index) {
          if (li.coverFiles.length <= 1) {
            num++
          }
        }
      })
      if (type === 'down') {
        if (item.imgIndex >= 2) {
          this.$refs['moveBox'][index - num].style = `transform: translateX(${(item.imgIndex - 1) * -38}px);`
        }
        return item.imgIndex++
      } else {
        if (item.imgIndex >= 3) {
          this.$refs['moveBox'][index - num].style = `transform: translateX(${(item.imgIndex - 2) * -38 + 38}px);`
        }
        return item.imgIndex--
      }
    },
    detalis(item) {
      this.$router.push(`/library/details/${item.resourceId}`)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  background: #e8eaed;
  height: 100%;
  .libarary {
    width: 1754px;
    height: 826px;
    background: #f5f5f5;
    border-radius: 4px 4px 4px 4px;
    .header {
      display: flex;
      align-items: center;
      padding-left: 48px;
      padding-top: 26px;
      height: 148px;
      background: url('../../assets/library/hearder_bg.png') no-repeat;
      background-size: cover;
      & > div:first-of-type {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 72px;
        .title {
          font-size: 18px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #0b1a44;
        }
        img {
          margin-top: 18px;
          width: 116px;
          height: 42px;
          cursor: pointer;
        }
      }
      .el-form {
        position: relative;
        width: 1300px;
        ::v-deep {
          .el-form-item__label {
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #0b1a44;
          }
          .el-input__inner {
            height: 36px;
            background: #ffffff;
            border: 1px solid #eeeeef;
            &::placeholder {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
            }
          }
          .name {
            .el-input__inner {
              width: 284px;
            }
          }
          .createUser {
            .el-input__inner {
              width: 242px;
            }
          }
          .modelType {
            .el-input__inner {
              width: 122px;
            }
          }
          .type {
            .el-input__inner {
              width: 122px;
            }
          }
          .button {
            position: absolute;
            right: 10px;
            bottom: 5px;
          }
          .el-range-input {
            background: #fff;
          }
        }
      }
    }
    .contentBox {
      display: flex;
      flex-wrap: wrap;
      width: 1576px;
      margin: 0 auto;
      & > div {
        display: flex;
        flex-direction: column;
        width: 364px;
        height: 260px;
        margin-right: 40px;
        margin-top: 32px;
        border-radius: 12px;
        box-shadow: 0 6px 12px 1px rgba($color: #b9cbdc, $alpha: 0.24);
        overflow: hidden;
        cursor: pointer;

        &:nth-of-type(4n) {
          margin-right: 0;
        }
        &:hover {
          .top {
            .operation {
              visibility: visible;
            }
          }
        }
        .top {
          position: relative;
          height: 128px;
          background: #38393c;
          overflow: hidden;
          .definition {
            position: absolute;
            left: 0;
            top: 0;
            width: 60px;
            height: 24px;
            line-height: 22px;
            background: #3464e0;
            border-radius: 8px 0 10px 0;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
          }
          .coverImg {
            height: 100%;
            width: 100%;
            object-fit: contain;
          }
          .previewBox {
            position: absolute;
            right: 4px;
            bottom: 4px;
            display: flex;
            align-items: center;
            min-width: 122px;
            .leftButton,
            .rightButton {
              width: 18px;
              height: 18px;
              cursor: pointer;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .preview {
              min-width: 70px;
              max-width: 114px;
              margin: 0 8px;
              overflow: hidden;
              & > div {
                display: flex;
                width: 5000%;
                transition: all 0.2s;
                img {
                  width: 32px;
                  height: 24px;
                  margin-right: 6px;
                  border: 1px solid #676b7d;
                  &:last-of-type {
                    margin-right: 0;
                  }
                }
                .checked {
                  border: 1px solid #fff;
                }
              }
            }
          }
          .operation {
            position: absolute;
            top: 25px;
            left: 0;
            display: flex;
            flex-direction: column;
            visibility: hidden;
            img {
              width: 76px;
              height: 28px;
              margin-top: 4px;
            }
          }
        }
        .center {
          height: 24px;
          line-height: 22px;
          background: #b1bac7;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #657081;
          text-align: center;
          i {
            margin: 0 5px;
            font-style: normal;
            color: #d8dbe1;
          }
        }
        .bottom {
          flex: 1;
          padding: 16px 16px 0 16px;
          background: #fff;
          & > div:first-of-type {
            display: flex;
            justify-content: space-between;
            & > div:first-of-type {
              span {
                font-size: 16px;
                font-family: Microsoft YaHei-Bold, Microsoft YaHei;
                font-weight: bold;
                color: #0b1a44;
              }
              .code {
                margin-right: 12px;
              }
            }
            & > div:last-of-type {
              width: 50px;
              height: 20px;
              line-height: 18px;
              background: #f3c057;
              border-radius: 5px;
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #657081;
              text-align: center;
            }
          }
          & > div:nth-of-type(2) {
            margin-top: 10px;
            width: 245px;
            font-size: 14px;
            font-family: Microsoft YaHei-Regular, Microsoft YaHei;
            font-weight: 400;
            color: #b1bac7;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          & > div:last-of-type {
            display: flex;
            justify-content: space-between;
            align-items: center;

            margin-top: 10px;
            & > div:first-of-type {
              display: flex;
              align-items: center;
              img {
                margin-right: 8px;
                width: 24px;
                height: 24px;
                border-radius: 50%;
              }
              span {
                font-size: 14px;
                font-family: Microsoft YaHei-Regular, Microsoft YaHei;
                font-weight: 400;
                color: #657081;
              }
            }
            & > div:last-of-type {
              font-size: 14px;
              font-family: Microsoft YaHei-Regular, Microsoft YaHei;
              font-weight: 400;
              color: #b1bac7;
            }
          }
        }
      }
    }
    ::v-deep {
      .el-pagination {
        width: 1576px;
        margin: auto;
        margin-top: 28px;
        text-align: right;
      }
    }
  }
  ::v-deep {
    .el-dialog {
      height: 266px;
      border-radius: 8px !important;
      .el-dialog__header {
        display: flex;
        align-items: center;
        padding-top: 12px;
        padding-left: 24px;
        padding-right: 20px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eeeeef;
        .hint {
          width: 30px;
          height: 30px;
        }
        .hintText {
          margin-left: 6px;
          font-size: 16px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          font-weight: 400;
          color: #303a40;
        }
        .el-dialog__headerbtn {
          right: 20px;
          top: 20px;
          .el-dialog__close {
            font-weight: bold;
          }
        }
      }
      .delText {
        text-align: center;
        font-size: 16px;
        font-family: Microsoft YaHei-Regular, Microsoft YaHei;
        font-weight: 400;
        color: #303a40;
      }
      .description {
        .el-textarea__inner {
          width: 250px;
          height: 80px;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
        }
      }
      .operate {
        display: flex;
        justify-content: center;
        .closeButton {
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #ffffff;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #d8dbe1;
          font-size: 14px;
          font-family: Microsoft YaHei-Regular, Microsoft YaHei;
          color: #a3a8bb;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #f5f5f5;
          }
        }
        .confirmButton {
          margin-left: 32px;
          width: 114px;
          height: 38px;
          line-height: 36px;
          background: #3464e0;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;
          font-family: Microsoft YaHei-Bold, Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          &:hover {
            background: #355fce;
          }
        }
      }
    }
  }
}
</style>
