<template>
  <div class="">
    <el-dialog title="售后维护信息" custom-class="after-sale-dialog" :visible.sync="afterSaleDialogVisible" :close-on-click-modal="false" width="1400px">
      <!-- 查询条件 -->
      <el-form ref="queryForm" :model="queryInfo" inline class="search-form">
        <el-form-item label="客户名称:" prop="customerName">
          <el-input v-model="queryInfo.customerName" placeholder="请输入客户名称" clearable @clear="handleSearch" />
        </el-form-item>

        <el-form-item label="合同名称:" prop="contractName">
          <el-input v-model="queryInfo.contractName" placeholder="请输入合同名称" clearable @clear="handleSearch" />
        </el-form-item>

        <el-form-item label="维护时间:" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="handleDateRangeChange"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="small" @click="handleSearch"> <i class="el-icon-search"></i> 查询 </el-button>
          <el-button size="small" @click="handleReset"> <i class="el-icon-refresh"></i> 重置 </el-button>
        </el-form-item>
      </el-form>

      <!-- 售后服务列表表格 -->

      <el-table v-loading="loading" :data="afterSaleList" element-loading-text="加载中..." border stripe header-cell-class-name="tableHeaderCell" cell-class-name="tableCell" style="width: 100%">
        <!-- 维护人姓名 -->
        <el-table-column prop="realName" label="维护人姓名" width="120" align="center" />

        <!-- 客户名称 -->
        <el-table-column prop="customerName" label="客户名称" width="140" align="center" show-overflow-tooltip />

        <!-- 合同名称 -->
        <el-table-column prop="contractName" label="合同名称" min-width="200" align="center" show-overflow-tooltip />

        <!-- 客户联系人名称 -->
        <el-table-column prop="contactsName" label="客户联系人名称" width="130" align="center" />

        <!-- 维护内容 -->
        <el-table-column prop="content" label="维护内容" min-width="150" align="center" show-overflow-tooltip />

        <!-- 维护方式 1 线上 2 线下 -->
        <el-table-column prop="saleWay" label="维护方式" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.saleWay === 1 ? 'success' : 'info'">
              {{ getSaleWayText(scope.row.saleWay) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 维护开始时间 -->
        <el-table-column prop="startTime" label="维护开始时间" width="120" align="center" />

        <!-- 维护结束时间 -->
        <el-table-column prop="endTime" label="维护结束时间" width="120" align="center" />

        <!-- 操作 -->
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" class="lookDetails_button" @click="viewDetail(scope.row)"> 查看附件详情 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="queryInfo.pageNum"
          :page-size="queryInfo.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="附件详情" :visible.sync="dialogVisible" custom-class="fileDetail-dialog" width="60%" top="80px" :close-on-click-modal="false">
      <!-- 附件列表 -->
      <div v-if="currentRow" class="files-section">
        <el-table :data="currentRow.files" border stripe header-cell-class-name="tableHeaderCell--file" cell-class-name="tableCell--file">
          <el-table-column prop="fileName" label="文件名" align="center" show-overflow-tooltip />
          <el-table-column prop="fileSize" label="文件大小" width="100" align="center">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="上传时间" width="180" align="center">
            <template slot-scope="scope">
              {{ scope.row.createTime | formatDate }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="scope">
              <el-button type="text" class="downLoad_button" size="small" @click="downloadFile(scope.row)"> 下载 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contractAfterAllList } from '@/api/contractNew'
export default {
  name: 'AfterSaleList',
  data() {
    return {
      afterSaleDialogVisible: false,
      dialogVisible: false,
      loading: false,
      afterSaleList: [],
      currentRow: null,
      total: 0,
      dateRange: null,
      queryInfo: {
        pageNum: 1,
        pageSize: 6,
        customerName: '', // 客户名称
        contractName: '', // 合同名称
        startTime: '', // 维护开始时间（yyyy-MM-dd）
        endTime: '' // 维护结束时间（yyyy-MM-dd）
      }
    }
  },
  created() {},
  methods: {
    openDialog() {
      this.getAfterSaleList()
      this.afterSaleDialogVisible = true
    },
    async getAfterSaleList() {
      this.loading = true
      try {
        // 构建查询参数，过滤空值
        const params = {}
        Object.keys(this.queryInfo).forEach((key) => {
          if (this.queryInfo[key] !== '' && this.queryInfo[key] !== null && this.queryInfo[key] !== undefined) {
            params[key] = this.queryInfo[key]
          }
        })

        const { data } = await contractAfterAllList(params)
        console.log('售后服务列表数据:', data)

        if (data && data.list) {
          this.afterSaleList = data.list
          this.total = data.total || 0
        } else if (data && Array.isArray(data)) {
          this.afterSaleList = data
          this.total = data.length
        } else {
          this.afterSaleList = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取售后服务列表失败:', error)
        this.$message.error('获取售后服务列表失败')
        this.afterSaleList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 查询
    handleSearch() {
      this.queryInfo.pageNum = 1
      this.getAfterSaleList()
    },

    // 重置
    handleReset() {
      this.queryInfo = {
        pageNum: 1,
        pageSize: 6,
        customerName: '',
        contractName: '',
        startTime: '',
        endTime: ''
      }
      this.dateRange = null
      this.$refs.queryForm.resetFields()
      this.getAfterSaleList()
    },

    // 日期范围变化
    handleDateRangeChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryInfo.startTime = dateRange[0]
        this.queryInfo.endTime = dateRange[1]
      } else {
        this.queryInfo.startTime = ''
        this.queryInfo.endTime = ''
      }
    },

    // 分页 - 每页条数变化
    handleSizeChange(newSize) {
      this.queryInfo.pageSize = newSize
      this.queryInfo.pageNum = 1
      this.getAfterSaleList()
    },

    // 分页 - 当前页变化
    handleCurrentChange(newPage) {
      this.queryInfo.pageNum = newPage
      this.getAfterSaleList()
    },

    // 获取服务方式文本
    getSaleWayText(saleWay) {
      const saleWayMap = {
        1: '线上',
        2: '线下'
      }
      return saleWayMap[saleWay] || '未知'
    },

    // 查看详情
    viewDetail(row) {
      if (row.files && row.files.length === 0) {
        this.$message.warning('暂无附件')
        return
      }
      this.currentRow = row
      this.dialogVisible = true
    },

    // 查看附件
    viewFiles(row) {
      this.viewDetail(row)
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 KB'
      const units = ['KB', 'MB', 'GB']
      let index = 0
      let fileSize = size

      while (fileSize >= 1024 && index < units.length - 1) {
        fileSize /= 1024
        index++
      }

      return `${fileSize.toFixed(1)} ${units[index]}`
    },

    // 下载文件
    downloadFile(file) {
      if (file.fileUrl) {
        window.open(file.fileUrl, '_blank')
      } else {
        this.$message.warning('文件链接不存在')
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .after-sale-dialog {
    max-height: 780px;
    overflow: auto;
    .el-dialog__header {
      border-bottom: 1px solid #eeeeef;
      padding-bottom: 20px;
      .el-dialog__title {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 18px;
        color: #0b1a44;
      }
      .el-dialog__close {
        font-size: 20px;
      }
    }
    .el-dialog__body {
      padding: 20px;
      .tableHeaderCell {
        padding: 0;
        height: 50px;
        background: #d5dff1;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
        color: #0b1a44;
      }
      .tableCell {
        padding: 0;
        height: 67px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #0b1a44;
      }
      .el-table__row:hover {
        .tableCell {
          background: #f5f5f5;
        }
      }
      .lookDetails_button {
        color: #3464e0;
      }
    }
    // 查询区域样式
    .search-form {
      .el-form-item {
        margin-bottom: 15px;
        margin-right: 20px;
        .el-form-item__label {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #0b1a44;
        }
        .el-input__inner {
          background: #f5f5f5;
          color: #0b1a44;
          .el-range-input {
            background: #f5f5f5;
          }
        }
        .el-input__icon {
          font-size: 17px;
          color: #0b1a44;
        }
      }
    }
  }
}

// 分页样式
.pagination-container {
  margin-top: 20px;
  text-align: right;
  padding: 20px 0;
}

.fileDetail-dialog {
  .files-section {
    margin-top: 20px;
    ::v-deep {
      .tableHeaderCell--file {
        padding: 0;
        height: 50px;
        background: #d5dff1;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
        color: #0b1a44;
      }
      .tableCell--file {
        padding: 0;
        height: 67px;
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #0b1a44;
      }
      .el-table__row:hover {
        .tableCell--file {
          background: #f5f5f5;
        }
      }
      .downLoad_button {
        color: #3464e0;
      }
    }

    h4 {
      margin-bottom: 10px;
      color: #303133;
      font-weight: 600;
    }
  }
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

// 表格样式优化
.el-table {
  .el-table__header {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

// 标签样式
.el-tag {
  font-size: 12px;
}
</style>
