# WebView一体化预览功能使用指南

## 功能概述

新的WebView一体化预览功能完全在当前Vue页面内实现，无需打开额外窗口。用户可以：

1. **在页面内直接浏览网站**：通过嵌入的webview组件
2. **完成网站登录**：登录状态会被保持
3. **智能内容提取**：支持多种网站的内容识别
4. **登录状态检测**：实时检测当前登录状态

## 核心优势

### ✅ 真正的一体化操作
- **单页面完成所有操作**：无需切换窗口
- **Session共享**：webview与主程序共享session
- **登录状态保持**：一次登录，持续有效

### 🔐 智能登录检测
- 自动检测Cookie中的登录信息
- 识别页面中的用户信息元素
- 判断是否需要登录

### 🧠 智能内容提取
- 支持千里马、政府采购网等主流招标网站
- 自动选择最佳内容提取规则
- 支持fallback到通用提取方式

## 使用步骤

### 1. 加载网页
```
输入网址 → 点击"🌐 加载预览"
```

### 2. 浏览和登录
```
在webview区域中正常浏览网站
如需登录，直接在webview中完成登录操作
```

### 3. 检查登录状态
```
点击"🔍 检查登录状态"确认登录是否成功
```

### 4. 提取内容
```
点击"📄 提取内容"获取页面数据
提取的内容会显示在下方的分析结果区域
```

## 技术实现

### WebView配置
```html
<webview 
  :src="currentUrl"
  partition="persist:main"
  allowpopups
  @dom-ready="onWebviewReady"
  @did-start-loading="onWebviewStartLoading"
  @did-stop-loading="onWebviewStopLoading"
></webview>
```

### Session共享
```javascript
// 在background.js中启用webview支持
webPreferences: {
  webviewTag: true  // 关键配置
}

// webview使用persist:main分区，与主程序共享session
partition="persist:main"
```

### 智能内容提取
```javascript
const selectors = [
  '#detailContentHtml', '#detailInfoContainer', '.title-module',  // 千里马
  '.vF_detail_content_container', '.vF_detail_content',          // 政府采购网
  '.detail_con', '.detail-content', '.content',                  // 公共资源交易
  '.vF_detail_main', '.main-content', 'article', '.post-content' // 通用规则
];
```

### 登录状态检测
```javascript
const hasLoginCookie = document.cookie.includes('login') || 
                      document.cookie.includes('session') || 
                      document.cookie.includes('token');
const hasLoginElement = document.querySelector('.logout, .user-info, .username') !== null;
const isLoggedIn = hasLoginCookie || hasLoginElement;
```

## 支持的网站类型

### 招标网站
- ✅ 千里马网站 (qianlima.com)
- ✅ 中国政府采购网 (ccgp.gov.cn)
- ✅ 公共资源交易网站
- ✅ 其他招标平台

### 通用网站
- ✅ 新闻网站
- ✅ 博客文章
- ✅ 企业官网
- ✅ 任何包含结构化内容的网站

## 状态指示器

### 加载状态
- 🌐 **加载预览**：准备加载网页
- ⏳ **加载中...**：正在加载网页
- ✅ **已加载**：网页加载完成

### 登录状态
- 🔐 **已登录**：检测到登录状态
- 🔓 **未登录**：未检测到登录状态
- ⚠️ **需要登录**：检测到登录表单

### 提取状态
- 📄 **提取内容**：准备提取内容
- ⏳ **提取中...**：正在提取内容
- ✅ **提取完成**：内容提取成功

## 故障排除

### 常见问题

1. **webview无法加载**
   - 检查网址是否正确
   - 确认网络连接正常
   - 尝试刷新页面

2. **登录状态检测失败**
   - 手动点击"🔍 检查登录状态"
   - 确认已在webview中完成登录
   - 检查网站是否使用特殊的登录机制

3. **内容提取不完整**
   - 等待页面完全加载后再提取
   - 检查是否有动态加载的内容
   - 尝试滚动页面加载更多内容

4. **Session不共享**
   - 确认webview使用了正确的partition
   - 重启应用程序
   - 清除浏览器缓存

### 调试方法

1. **查看控制台日志**
   ```javascript
   // 在开发者工具中查看webview相关日志
   console.log('webview DOM ready')
   console.log('登录状态检查结果:', result)
   ```

2. **检查webview状态**
   ```javascript
   // 检查webview是否正确加载
   this.$refs.webview.getURL()
   this.$refs.webview.canGoBack()
   ```

## 与传统方式对比

| 功能 | 传统方式 | WebView一体化 |
|------|----------|---------------|
| 窗口数量 | 3-4个窗口 | 1个页面 |
| 操作步骤 | 6-8步 | 3-4步 |
| 登录保持 | 需要手动管理 | 自动保持 |
| 用户体验 | 复杂 | 简单直观 |
| Session共享 | 复杂配置 | 自动共享 |

## 更新日志

### 2025-09-15
- ✅ 实现WebView一体化预览功能
- ✅ 支持页面内登录和浏览
- ✅ 智能登录状态检测
- ✅ 优化内容提取算法
- ✅ 改进用户界面和交互体验
- ✅ 完善错误处理和状态提示
