<template>
  <div class="productDetails">
    <el-dialog title="产品详情" :visible.sync="dialogVisible">
      <template v-if="info">
        <div class="descriptionBox">
          <div class="descriptonItem">
            <span class="label">产品所属部门：</span>
            <span class="value">{{ info.type === 1 ? '产品部' : '虚拟仿真部' }}</span>
          </div>
          <div class="descriptonItem">
            <span class="label">专业：</span>
            <span class="value">{{ info.majorName }}</span>
          </div>
          <div v-if="info.type === 2" class="descriptonItem">
            <span class="label">实验模式：</span>
            <span class="value">{{ info.mode === 1 ? '考核模式' : '学习模式' }}</span>
          </div>
          <div v-if="info.type === 1" class="descriptonItem">
            <span class="label">产品地址：</span>
            <span class="value linkBox">
              <i v-for="link in info.address.split(',')" :key="link" @click="jumpLink(link)">{{ link }}</i>
            </span>
          </div>
          <div class="descriptonItem">
            <span class="label">产品类型：</span>
            <span class="value state" :style="{ background: info.state == 2 ? '#E1EAFF' : info.state === 3 ? '#D4F0EC' : '' }">
              {{ info.state === 1 ? '测试' : info.state === 2 ? '定稿' : info.state === 3 ? '发布' : '归档' }}区
            </span>
          </div>
          <div class="descriptonItem">
            <span class="label">发布时间：</span>
            <span class="value">{{ info.releaseTime }}</span>
          </div>
          <div class="descriptonItem">
            <span class="label">实验版本：</span>
            <span class="value">{{ info.version === 'new' ? '新版实验' : '老版实验' }}</span>
          </div>
          <div class="descriptonItem">
            <span class="label">版本说明：</span>
            <span class="value">{{ info.versionNote ? info.versionNote : '无' }}</span>
          </div>
          <div class="descriptonItem">
            <span class="label">备注：</span>
            <span class="value">{{ info.remark ? info.remark : '无' }}</span>
          </div>
        </div>
        <el-button v-if="info.type === 2" type="primary" round @click="lookTest">查看实验</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { releaseProductDetail, releaseProduct_log_addView } from '@/api/productRelease'
export default {
  name: '',
  data() {
    return {
      dialogVisible: false,
      info: null,
      productId: null,
      state: null
    }
  },
  created() {},
  methods: {
    openDialog(item, state) {
      this.dialogVisible = true
      this.state = state
      this.productId = item.productId
      this.getDetails(item.productId, state)
    },
    async getDetails(productId, state) {
      const { data } = await releaseProductDetail({ productId, state })
      this.info = { ...data }
    },
    jumpLink(link) {
      window.open(link, '_blank')
    },
    async lookTest() {
      await releaseProduct_log_addView({ productId: this.productId })
      const link = this.$router.resolve(`/productRelease/lookTest/${this.productId}/${this.state}/${this.info.version}`)

      window.open(link.href, '_blank', 'height=1080, width=1920, fullscreen="yes"')
    }
  }
}
</script>
<style scoped lang="scss">
.productDetails {
  ::v-deep {
    .el-dialog {
      border-radius: 10px;
      width: 550px;
      .el-dialog__header {
        border-bottom: 1px solid #d9d9d9;
        .el-dialog__title {
          font-family: PingFang SC, PingFang SC;
          font-size: 20px;
          color: #333333;
        }
        .el-dialog__headerbtn {
          top: 12px;
          .el-dialog__close {
            font-size: 30px;
          }
        }
      }
      .el-dialog__body {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 50px;
        .descriptionBox {
          margin-bottom: 40px;
          .descriptonItem {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            & > span {
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
            }
            .label {
              font-size: 18px;
              color: #666666;
            }
            .value {
              flex: 1;
              font-size: 20px;
              color: #333333;
            }
            .linkBox {
              i {
                display: block;
                margin-bottom: 10px;
                font-style: normal;
                color: #3464e0;
                cursor: pointer;
                &:last-of-type {
                  margin-bottom: 0;
                }
                &:hover {
                  text-decoration: underline;
                }
              }
            }
            .state {
              flex: none;
              display: flex;
              justify-content: center;
              width: 74px;
              height: 32px;
              background: #f6e5f9;
              border-radius: 4px 4px 4px 4px;
              font-size: 18px;
              color: #000000;
              line-height: 32px;
            }
          }
        }
      }
    }
  }
}
</style>
