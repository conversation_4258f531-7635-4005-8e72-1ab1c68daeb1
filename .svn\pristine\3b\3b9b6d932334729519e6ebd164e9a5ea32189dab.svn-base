<template>
  <div class="meeting-room-schedule">
    <!-- 大会议室 -->
    <div class="room-row">
      <div class="room-info">
        <svg-icon icon-class="newMeeting" class="room-icon large-room"></svg-icon>
        <span class="room-name">大会议室</span>
      </div>

      <div class="schedule-timeline">
        <!-- 预约按钮网格 -->
        <div class="booking-grid">
          <div v-for="(slot, index) in bookingSlots" :key="`large-${index}`" class="booking-slot" :class="{ occupied: isSlotOccupied('large', slot) }" @click="handleBooking('large', slot)">
            <svg-icon icon-class="reservation"></svg-icon>
            <span v-if="!isSlotOccupied('large', slot)" class="booking-btn"> 预约 </span>
          </div>
        </div>

        <!-- 会议色块 -->
        <div v-for="meeting in largeMeetings" :key="meeting.conferenceId" class="meeting-block large-meeting" :style="getMeetingBlockStyle(meeting)" @click="handleMeetingClick(meeting)">
          <el-tooltip effect="dark" placement="top" popper-class="meetingTooltip">
            <div slot="content">
              会议主题：{{ meeting.conferenceTitle }}<br />
              时间：{{ meeting.startTime }}-{{ meeting.endTime }}<br />
              预约人：{{ meeting.createUserName }}<br />
              参会人：{{ meeting.conferenceUserNames }}
            </div>

            <span class="meeting-title">{{ meeting.conferenceTitle }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 小会议室 -->
    <div class="room-row">
      <div class="room-info">
        <svg-icon icon-class="newMeeting" class="room-icon small-room"></svg-icon>
        <span class="room-name">小会议室</span>
      </div>

      <div class="schedule-timeline">
        <!-- 预约按钮网格 -->
        <div class="booking-grid small">
          <div v-for="(slot, index) in bookingSlots" :key="`small-${index}`" class="booking-slot" :class="{ occupied: isSlotOccupied('small', slot) }" @click="handleBooking('small', slot)">
            <svg-icon icon-class="reservation"></svg-icon>
            <span v-if="!isSlotOccupied('small', slot)" class="booking-btn"> 预约 </span>
          </div>
        </div>

        <!-- 会议色块 -->
        <div v-for="meeting in smallMeetings" :key="meeting.conferenceId" class="meeting-block small-meeting" :style="getMeetingBlockStyle(meeting)" @click="handleMeetingClick(meeting)">
          <el-tooltip effect="dark" placement="top" popper-class="meetingTooltip">
            <div slot="content">
              会议主题：{{ meeting.conferenceTitle }}<br />
              时间：{{ meeting.startTime }}-{{ meeting.endTime }}<br />
              预约人：{{ meeting.createUserName }}<br />
              参会人：{{ meeting.conferenceUserNames }}
            </div>
            <span class="meeting-title">{{ meeting.conferenceTitle }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formaTimeStr } from '@/utils'
export default {
  name: 'MeetingRoomSchedule',
  props: {
    currentWeek: {
      type: Array,
      required: true
    },
    meetingData: {
      type: Object,
      default: () => {}
    },
    selectedDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      timeSlots: ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'],
      // 半小时为单位的预约时间段
      bookingSlots: ['8:00', '8:30', '9:00', '9:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30']
    }
  },
  computed: {
    largeMeetings() {
      return this.meetingData.one
    },
    smallMeetings() {
      return this.meetingData.two
    }
  },
  methods: {
    // 计算会议色块的样式
    getMeetingBlockStyle(meeting) {
      // 基于半小时时间段计算位置
      const startSlotIndex = this.getSlotIndex(meeting.startTime)
      const endSlotIndex = this.getSlotIndex(meeting.endTime)
      const slotCount = endSlotIndex - startSlotIndex

      return {
        left: `${this.$px2rem(startSlotIndex * 67)}`,
        width: `${this.$px2rem(slotCount * 67 - 7)}`
      }
    },

    // 获取时间在bookingSlots中的索引
    getSlotIndex(timeStr) {
      if (timeStr === '18:00') {
        return 20
      }
      return this.bookingSlots.findIndex((slot) => slot === formaTimeStr(timeStr))
    },

    // 解析时间字符串为小时数
    parseTime(timeStr) {
      const [hour, minute] = timeStr.split(':').map(Number)
      return hour + minute / 60
    },

    // 检查时间段是否被占用
    isSlotOccupied(roomType, slotTime) {
      const meetings = roomType === 'large' ? this.largeMeetings : this.smallMeetings
      const slotHour = this.parseTime(slotTime)

      return meetings.some((meeting) => {
        const startHour = this.parseTime(meeting.startTime)
        const endHour = this.parseTime(meeting.endTime)
        return slotHour >= startHour && slotHour < endHour
      })
    },

    // 处理预约点击
    handleBooking(roomType, slotTime) {
      // 检查是否可以预约（不能预约过去的时间）
      if (!this.canBookTime(slotTime)) {
        this.$message.warning('不能预约过去的时间')
        return
      }

      this.$emit('booking-click', {
        roomType,
        startTime: slotTime,
        endTime: this.getNextSlot(slotTime)
      })
    },

    // 处理会议点击
    handleMeetingClick(meeting) {
      const meetingStatus = this.getMeetingStatus(meeting)

      if (meetingStatus === 'finished') {
        // 已结束的会议，显示详情
        this.$emit('meeting-detail', meeting)
      } else if (meetingStatus === 'ongoing') {
        // 正在进行中的会议，不能修改
        this.$message.warning('不能修改正在进行中的会议')
      } else {
        // 未开始的会议，可以修改
        this.$emit('meeting-click', meeting)
      }
    },

    // 检查是否可以预约指定时间
    canBookTime(timeStr) {
      const now = new Date()
      const currentDate = now.toISOString().split('T')[0] // 当前日期 YYYY-MM-DD
      const currentTime = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}` // 当前时间 HH:MM

      // 如果选择的日期是今天之前，不能预约
      if (this.selectedDate < currentDate) {
        return false
      }

      // 如果选择的日期是今天，检查时间是否已过
      if (this.selectedDate === currentDate) {
        const bookTime = this.parseTime(timeStr)
        const nowTime = this.parseTime(currentTime)
        return bookTime > nowTime
      }

      // 未来日期可以预约
      return true
    },

    // 获取会议状态
    getMeetingStatus(meeting) {
      const now = new Date()
      const currentDate = now.toISOString().split('T')[0]
      const currentTime = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`

      // 如果会议日期是今天之前，会议已结束
      if (meeting.reservedDate < currentDate) {
        return 'finished'
      }

      // 如果会议日期是今天，检查具体时间
      if (meeting.reservedDate === currentDate) {
        const meetingStartTime = this.parseTime(meeting.startTime)
        const meetingEndTime = this.parseTime(meeting.endTime)
        const nowTime = this.parseTime(currentTime)

        if (nowTime < meetingStartTime) {
          return 'upcoming' // 未开始
        } else if (nowTime >= meetingStartTime && nowTime < meetingEndTime) {
          return 'ongoing' // 进行中
        } else {
          return 'finished' // 已结束
        }
      }

      // 未来日期的会议
      return 'upcoming'
    },

    // 检查是否可以修改会议
    canModifyMeeting(meeting) {
      const status = this.getMeetingStatus(meeting)
      return status === 'upcoming'
    },

    // 获取下一个时间段
    getNextSlot(currentSlot) {
      const currentIndex = this.bookingSlots.findIndex((slot) => slot === currentSlot)
      if (currentIndex < this.bookingSlots.length - 1) {
        return this.bookingSlots[currentIndex + 1]
      }
      return '18:00' // 默认结束时间
    }
  }
}
</script>

<style scoped lang="scss">
.meeting-room-schedule {
  margin-top: 16px;
}
.room-row {
  display: flex;
  align-items: center;
  height: 65px;
  padding-left: 30px;
  background: #ffffff;
  border-radius: 14px 14px 14px 14px;
  border: 1px solid #e9e9e9;
  margin-bottom: 16px;
  @media (min-height: 970px) {
    height: 70px;
  }
}

.room-info {
  display: flex;
  align-items: center;
  margin-right: 56px;
  .room-icon {
    font-size: 35px;
    margin-right: 23px;

    &.large-room {
      color: #3465df;
    }

    &.small-room {
      color: #34a853;
    }
  }

  .room-name {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22px;
    color: #333333;
  }
}

.schedule-timeline {
  flex: 1;
  position: relative;
  height: 60px;
  margin-right: 40px;
}

.booking-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

.booking-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 40px;
  margin-right: 7px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  border: 1px dashed #3465df;
  transition: all 0.3s;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #3465df;
  cursor: pointer;
  opacity: 0.3;

  &.occupied {
    background-color: rgba(0, 0, 0, 0.02);
    visibility: hidden;
  }
  &:hover {
    opacity: 1;
  }
  & > span {
    margin-left: 4px;
  }
}

.booking-grid.small {
  .booking-slot {
    border: 1px dashed #13b755;
    color: #13b755;
  }
}
// .booking-btn {
//   &:hover {
//     opacity: 1;
//     transform: scale(1.05);
//   }
// }

.meeting-block {
  position: absolute;
  top: 5px;
  height: 50px;
  padding: 0 10px;
  line-height: 50px;
  border-radius: 8px;

  font-family: PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;

  &.large-meeting {
    background: #4285f4;

    &:hover {
      background: #3367d6;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
    }
  }

  &.small-meeting {
    background: #34a853;

    &:hover {
      background: #2d8f47;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(52, 168, 83, 0.3);
    }
  }

  .meeting-title {
    display: inline-block;
    width: 100%;
    font-family: PingFang SC;
    font-size: 18px;
    color: #ffffff;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .meeting-time {
    font-size: 10px;
    opacity: 0.9;
    margin-top: 2px;
  }
}
</style>

<style lang="scss">
.meetingTooltip {
  border-radius: 8px;
  font-family: PingFang SC;
  font-size: 14px;
}
</style>
