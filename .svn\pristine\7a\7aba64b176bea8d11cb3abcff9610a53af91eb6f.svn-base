import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

// export function getInfo(token) {
//   return request({
//     url: '/user/info',
//     method: 'get',
//     params: { token }
//   })
// }

// 获取当前用户的权限
export function findUserRoleConfiguration(token) {
  return request({
    url: '/system/role/findUserRoleConfiguration',
    method: 'GET',
    params: {
      token
    }
  })
}
export function logout() {
  return request({
    url: '/logout',
    method: 'get'
  })
}

